# Django K2GG6373 Refactored Project

This is a refactored version of the Django K2GG6373 project, with improved code organization, documentation, and maintainability.

## Project Structure

```
new/
├── dj2/                  # Main Django project directory
│   ├── __init__.py
│   ├── settings.py       # Project settings
│   ├── urls.py           # Main URL routing
│   ├── views.py          # Main views
│   └── wsgi.py           # WSGI configuration
├── main/                 # Main application
│   ├── __init__.py
│   ├── admin.py          # Admin configuration
│   ├── apps.py           # App configuration
│   ├── config_model.py   # Configuration model
│   ├── config_v.py       # Configuration views
│   ├── model.py          # Base model
│   ├── models.py         # Application models
│   ├── schema_v.py       # Schema views
│   ├── urls.py           # Application URL routing
│   ├── users_model.py    # User model
│   └── users_v.py        # User views
├── util/                 # Utility functions
│   ├── __init__.py
│   ├── auth.py           # Authentication utilities
│   ├── baidubce_api.py   # Baidu BCE API utilities
│   ├── codes.py          # Status codes
│   ├── common.py         # Common utilities
│   ├── configread.py     # Configuration reading utilities
│   ├── CustomJSONEncoder.py # Custom JSON encoder
│   └── message.py        # Message strings
├── xmiddleware/          # Custom middleware
│   ├── __init__.py
│   ├── xauth.py          # Authentication middleware
│   └── xparam.py         # Parameter processing middleware
├── templates/            # Templates directory
│   ├── front/assets/     # Front-end assets
│   └── upload/           # Upload directory
└── manage.py             # Django management script
```

## Features

- Improved code organization and structure
- Better documentation with docstrings
- More consistent naming conventions
- Enhanced error handling
- Cleaner and more maintainable code
- Same functionality as the original project

## Requirements

- Python 3.6+
- Django 2.0+
- MySQL database

## Setup

1. Clone the repository
2. Create a virtual environment
3. Install dependencies: `pip install -r requirements.txt`
4. Configure the database in `config.ini`
5. Run migrations: `python manage.py migrate`
6. Create a superuser: `python manage.py createsuperuser`
7. Run the server: `python manage.py runserver`

## Usage

Access the admin interface at `/xadmin/` and the main application at `/{schemaName}/`.

## License

This project is licensed under the MIT License.
