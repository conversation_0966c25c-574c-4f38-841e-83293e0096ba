# coding:utf-8
import hashlib
import base64
from Crypto.Cipher import DES, AES
from Crypto.Util.Padding import pad, unpad


class Common(object):
    """
    Common utility functions
    """
    def md5(self, pwd):
        """
        Calculate MD5 hash of a string
        :param pwd: String to hash
        :return: MD5 hash string
        """
        md5 = hashlib.md5()
        md5.update(pwd.encode())
        return md5.hexdigest()
    
    def encrypt_des(self, text, key):
        """
        Encrypt text using DES
        :param text: Text to encrypt
        :param key: Encryption key
        :return: Encrypted text
        """
        # Ensure key is 8 bytes
        key = key.ljust(8, '\0')[:8].encode()
        
        # Create cipher
        cipher = DES.new(key, DES.MODE_ECB)
        
        # Pad text and encrypt
        padded_text = pad(text.encode(), DES.block_size)
        encrypted_text = cipher.encrypt(padded_text)
        
        # Return base64 encoded string
        return base64.b64encode(encrypted_text).decode()
    
    def decrypt_des(self, encrypted_text, key):
        """
        Decrypt text using DES
        :param encrypted_text: Encrypted text
        :param key: Decryption key
        :return: Decrypted text
        """
        # Ensure key is 8 bytes
        key = key.ljust(8, '\0')[:8].encode()
        
        # Create cipher
        cipher = DES.new(key, DES.MODE_ECB)
        
        # Decode base64 and decrypt
        encrypted_bytes = base64.b64decode(encrypted_text)
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # Unpad and return
        try:
            unpadded_bytes = unpad(decrypted_bytes, DES.block_size)
            return unpadded_bytes.decode()
        except:
            return decrypted_bytes.decode()
    
    def encrypt_aes(self, text, key):
        """
        Encrypt text using AES
        :param text: Text to encrypt
        :param key: Encryption key
        :return: Encrypted text
        """
        # Ensure key is 16, 24, or 32 bytes
        key = key.ljust(16, '\0')[:16].encode()
        
        # Create cipher
        cipher = AES.new(key, AES.MODE_ECB)
        
        # Pad text and encrypt
        padded_text = pad(text.encode(), AES.block_size)
        encrypted_text = cipher.encrypt(padded_text)
        
        # Return base64 encoded string
        return base64.b64encode(encrypted_text).decode()
    
    def decrypt_aes(self, encrypted_text, key):
        """
        Decrypt text using AES
        :param encrypted_text: Encrypted text
        :param key: Decryption key
        :return: Decrypted text
        """
        # Ensure key is 16, 24, or 32 bytes
        key = key.ljust(16, '\0')[:16].encode()
        
        # Create cipher
        cipher = AES.new(key, AES.MODE_ECB)
        
        # Decode base64 and decrypt
        encrypted_bytes = base64.b64decode(encrypted_text)
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        
        # Unpad and return
        try:
            unpadded_bytes = unpad(decrypted_bytes, AES.block_size)
            return unpadded_bytes.decode()
        except:
            return decrypted_bytes.decode()
