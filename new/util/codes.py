# coding:utf-8
"""
Status codes for API responses
"""
normal_code = 0  # Normal
redirect_code = 301  # Redirect
temporary_redirect_code = 302  # Temporary redirect

empty_param_code = 10001  # Empty or incorrect request parameter
validate_param_code = 10002  # Non-standard request parameter
is_not_json_code = 10003  # Data format is not JSON

other_code = 10020  # Other error
crud_error_code = 10021  # Database operation failed
header_error_code = 10023  # Header error
captcha_error_code = 10024  # Captcha error
id_exist_code = 10025  # ID or username already exists
id_notexist_code = 10026  # ID or username does not exist
username_error_code = 10027  # Username error
password_error_code = 10028  # Password error
file_notexist_code = 10029  # Upload file does not exist

token_error_code = 20001  # Token error
token_expired_code = 20002  # Token expired
non_authorized_code = 20003  # No permission

system_error_code = 40001  # System-level error
request_expired_code = 40002  # Request expired
repeated_request_code = 40003  # Repeated request
