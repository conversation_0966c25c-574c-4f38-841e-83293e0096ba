# coding:utf-8
import base64
import copy
from django.http import JsonResponse
from django.apps import apps

from util.codes import *
from util import message as mes


class Auth(object):
    """
    Authentication utility class
    """
    def authenticate(self, model, req_dict):
        """
        User login, returns token on success, failure reason on failure
        :param model: User model
        :param req_dict: Request parameters
        :return: JsonResponse
        """
        msg = {'code': normal_code, 'msg': mes.normal_code, 'data': {}}
        tablename = model.__tablename__
        encode_dict = {"tablename": tablename, "params": req_dict}

        # Encode token
        encode_str = base64.b64encode(str(encode_dict).encode("utf-8"))
        msg['data']["id"] = req_dict.get("id")
        msg["id"] = req_dict.get("id")
        msg['token'] = encode_str.decode('utf-8')
        
        return JsonResponse(msg)

    def get_token(self, model, req_dict):
        """
        Generate token for user
        :param model: User model
        :param req_dict: Request parameters
        :return: Token string
        """
        tablename = model.__tablename__
        encode_dict = {"tablename": tablename, "params": req_dict}

        encode_str = base64.b64encode(str(encode_dict).encode("utf8"))
        return encode_str.decode('utf-8')

    def identify(self, request):
        """
        User authentication
        :param request: Request object
        :return: Authentication result
        """
        msg = {'code': normal_code, 'msg': mes.normal_code, 'data': {}}
        
        # Get token from header
        token = request.META.get('HTTP_TOKEN')

        if token and token != "null":
            auth_token = copy.deepcopy(token)

            # Decode token
            decode_str = base64.b64decode(auth_token).decode("utf8")
            decode_str = decode_str.replace('"null"', '""').replace('null', '""')
            decode_dict = eval(decode_str)

            tablename2 = decode_dict.get("tablename")
            params2 = decode_dict.get("params", {})

            # Find user in database
            datas = None
            allModels = apps.get_app_config('main').get_models()
            for model in allModels:
                if model.__tablename__ == tablename2:
                    datas = model.getbyparams(model, model, params2)

            if not datas:
                msg['code'] = 401
                msg['msg'] = '找不到该用户信息'
                result = msg
            else:
                request.session['tablename'] = tablename2
                request.session['params'] = params2
                msg['msg'] = '身份验证通过。'
                result = msg
        else:
            msg['code'] = 401
            msg['msg'] = 'headers未包含认证信息。'
            result = msg
        
        return result
