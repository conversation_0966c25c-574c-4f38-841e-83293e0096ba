# coding:utf-8
from configparser import Config<PERSON><PERSON><PERSON>


def config_read(filePath: str):
    """
    Read configuration from a file
    :param filePath: Path to the configuration file
    :return: Database configuration parameters
    """
    cfg = ConfigParser()
    cfg.read(filePath, encoding="utf-8-sig")
    
    if "sql" in cfg.sections():
        dbType = cfg.get('sql', 'type')
        host = cfg.get('sql', 'host')
        port = cfg.getint('sql', 'port')
        user = cfg.get('sql', 'user')
        passwd = cfg.get('sql', 'passwd')
        dbName = cfg.get('sql', 'db')
        charset = cfg.get('sql', 'charset')
        return dbType, host, port, user, passwd, dbName, charset
    else:
        return None, None, None, None, None, None, None
