# coding:utf-8
from datetime import datetime, date
import json
from decimal import Decimal


class CustomJsonEncoder(json.JSONEncoder):
    """
    Custom JSON encoder for handling special data types
    """
    def default(self, obj):
        """
        Convert special data types to JSON-serializable types
        :param obj: Object to convert
        :return: JSON-serializable object
        """
        if isinstance(obj, datetime):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(obj, date):
            return obj.strftime('%Y-%m-%d')
        if isinstance(obj, Decimal):
            # If it's an integer, return an integer
            if obj % 1 == 0:
                return int(obj)
            # Otherwise return a float
            return float(obj)
        return super().default(obj)
