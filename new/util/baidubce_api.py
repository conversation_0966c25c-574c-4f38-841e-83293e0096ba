# coding:utf-8
import base64
import json
import urllib
import urllib.request
import urllib.parse
import ssl


class BaiDuBce(object):
    """
    Baidu BCE API utility class
    """
    def __init__(self):
        """
        Initialize with default API credentials
        """
        self.client_id = 'x20xOjtOsAtbQhm2WBuifuQw'  # AK
        self.client_secret = 'O7yMp2dmOnCtQtBokUt1gN6hgFCcLLcp'  # SK
    
    def get_token(self):
        """
        Get access token from Baidu API
        :return: Access token
        """
        host = 'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={}&client_secret={}'.format(
            self.client_id, self.client_secret)
        
        request = urllib.request.Request(host)
        request.add_header('Content-Type', 'application/json; charset=UTF-8')
        
        response = urllib.request.urlopen(request)
        content = response.read()
        
        if content:
            content = json.loads(content)
            return content.get('access_token')
        
        return None
    
    def bd_check2pic(self, pic1, pic2):
        """
        Compare two face images using Baidu API
        :param pic1: First image path
        :param pic2: Second image path
        :return: Comparison result
        """
        request_url = "https://aip.baidubce.com/rest/2.0/face/v3/match"
        
        # Get access token
        access_token = self.get_token()
        request_url = request_url + "?access_token=" + access_token
        
        # Prepare image data
        image1 = self.get_file_content_as_base64(pic1)
        image2 = self.get_file_content_as_base64(pic2)
        
        params = [
            {
                "image": image1,
                "image_type": "BASE64",
                "face_type": "LIVE",
                "quality_control": "LOW"
            },
            {
                "image": image2,
                "image_type": "BASE64",
                "face_type": "LIVE",
                "quality_control": "LOW"
            }
        ]
        
        params = json.dumps(params).encode('utf-8')
        
        # Send request
        request = urllib.request.Request(url=request_url, data=params)
        request.add_header('Content-Type', 'application/json')
        
        response = urllib.request.urlopen(request)
        content = response.read()
        
        if content:
            return json.loads(content)
        
        return None
    
    def get_file_content_as_base64(self, path, urlencoded=False):
        """
        Get file content as base64 encoded string
        :param path: File path
        :param urlencoded: Whether to URL encode the result
        :return: Base64 encoded string
        """
        with open(path, "rb") as f:
            content = base64.b64encode(f.read()).decode("utf8")
            if urlencoded:
                content = urllib.parse.quote_plus(content)
        return content
