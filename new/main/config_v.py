# coding:utf-8

import logging

from django.http import JsonResponse

from .config_model import config
from util.codes import *
from util import message as mes


def config_page(request):
    """
    Get paginated configuration parameters
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code,
               "data": {"currPage": 1, "totalPage": 1, "total": 1, "pageSize": 10, "list": []}}
        
        req_dict = request.session.get('req_dict')
        
        # Get paginated data
        msg['data']['list'], msg['data']['currPage'], msg['data']['totalPage'], msg['data']['total'], \
        msg['data']['pageSize'] = config.page(config, config, req_dict)
        
        return JsonResponse(msg)


def config_list(request):
    """
    Get configuration list
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code,
               "data": {"currPage": 1, "totalPage": 1, "total": 1, "pageSize": 10, "list": []}}
        
        req_dict = request.session.get("req_dict")
        
        # Get paginated data
        msg['data']['list'], msg['data']['currPage'], msg['data']['totalPage'], msg['data']['total'], \
        msg['data']['pageSize'] = config.page(config, config, req_dict)

        return JsonResponse(msg)


def config_info(request, id_):
    """
    Get configuration information by id
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        # Get configuration by id
        data = config.getbyid(config, config, int(id_))
        if len(data) > 0:
            msg['data'] = data[0]
        
        return JsonResponse(msg)


def config_detail(request, id_):
    """
    Get configuration detail by id
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        # Get configuration by id
        data = config.getbyid(config, config, int(id_))
        if len(data) > 0:
            msg['data'] = data[0]
        
        return JsonResponse(msg)


def config_save(request):
    """
    Save new configuration
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        req_dict = request.session.get('req_dict')
        
        # Check if configuration already exists
        param1 = config.getbyparams(config, config, req_dict)
        if param1:
            msg['code'] = id_exist_code
            msg['msg'] = mes.id_exist_code
            return JsonResponse(msg)

        # Create configuration
        error = config.createbyreq(config, config, req_dict)
        logging.warning("save_config.res=========>{}".format(error))
        
        if error is not None:
            msg['code'] = crud_error_code
            msg['msg'] = error
        
        return JsonResponse(msg)


def config_add(request):
    """
    Add new configuration
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        req_dict = request.session.get("req_dict")

        # Create configuration
        error = config.createbyreq(config, config, req_dict)
        if error is not None:
            msg['code'] = crud_error_code
            msg['msg'] = error
        
        return JsonResponse(msg)


def config_update(request):
    """
    Update configuration
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        req_dict = request.session.get('req_dict')

        # Update configuration
        config.updatebyparams(config, config, req_dict)

        return JsonResponse(msg)


def config_delete(request):
    """
    Delete configuration
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        req_dict = request.session.get('req_dict')
        
        # Delete configuration
        config.deletes(config, config, req_dict.get("ids"))

        return JsonResponse(msg)
