# coding:utf-8

import base64
import copy
import datetime
import json
import logging
import os
import random
import sys
import time
import urllib.parse
import uuid

from django.apps import apps
from django.conf import settings
from django.core.mail import send_mail
from django.db import connection
from django.db.models import Q
from django.forms.models import model_to_dict
from django.http import JsonResponse, HttpResponse, FileResponse
from django.shortcuts import redirect

from util.auth import Auth
from util.codes import *
from util.common import Common
from util.CustomJSONEncoder import CustomJsonEncoder
import util.message as mes
from util.baidubce_api import BaiDuBce
from .config_model import config


def schemaName_cal(request, tableName, columnName):
    """
    Calculate statistics for a table column
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        # Get all models
        allModels = apps.get_app_config('main').get_models()
        model = None
        
        # Find the model by table name
        for m in allModels:
            if m.__tablename__ == tableName:
                model = m
                break
        
        if model is None:
            msg['code'] = crud_error_code
            msg['msg'] = "表不存在"
            return JsonResponse(msg)
        
        # Calculate statistics
        cursor = connection.cursor()
        
        # Get column type
        columnType = ""
        for field in model._meta.fields:
            if field.name == columnName:
                columnType = field.get_internal_type()
                break
        
        # Calculate based on column type
        if columnType in ["IntegerField", "BigIntegerField", "FloatField", "DecimalField"]:
            cursor.execute(f"SELECT SUM({columnName}), AVG({columnName}), MAX({columnName}), MIN({columnName}) FROM {tableName}")
            sum_val, avg_val, max_val, min_val = cursor.fetchone()
            
            msg['data']['sum'] = sum_val
            msg['data']['avg'] = avg_val
            msg['data']['max'] = max_val
            msg['data']['min'] = min_val
        else:
            cursor.execute(f"SELECT COUNT(DISTINCT {columnName}) FROM {tableName}")
            distinct_count = cursor.fetchone()[0]
            
            msg['data']['distinctCount'] = distinct_count
        
        return JsonResponse(msg)


def schemaName_file_download(request):
    """
    Download a file
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        req_dict = request.session.get("req_dict")
        filename = req_dict.get("fileName")
        
        if not filename:
            msg['code'] = crud_error_code
            msg['msg'] = "文件名不能为空"
            return JsonResponse(msg)
        
        # Get file path
        file_path = os.path.join(os.getcwd(), "templates/upload", filename)
        
        if not os.path.isfile(file_path):
            msg['code'] = crud_error_code
            msg['msg'] = "文件不存在"
            return JsonResponse(msg)
        
        # Return file response
        response = FileResponse(open(file_path, 'rb'))
        response['Content-Type'] = 'application/octet-stream'
        response['Content-Disposition'] = f'attachment;filename="{filename}"'
        
        return response


def schemaName_file_upload(request):
    """
    Upload a file
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        file = request.FILES.get("file")
        type = request.POST.get("type")
        
        if not file:
            msg['code'] = crud_error_code
            msg['msg'] = "未上传文件"
            return JsonResponse(msg)
        
        # Save file
        filename = file.name
        filesuffix = filename.split(".")[-1]
        file_name = f"{int(float(time.time()) * 1000)}.{filesuffix}"
        
        if type is not None and '_template' in type:
            file_name = f"{type}.{filesuffix}"
        
        file_path = os.path.join(os.getcwd(), "templates/upload", file_name)
        
        with open(file_path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
        
        msg["file"] = file_name
        
        # Check if need to save as face recognition base photo
        req_dict = request.session.get("req_dict")
        type1 = req_dict.get("type", 0)
        type1 = str(type1)
        
        if type1 == '1':
            params = {"name": "faceFile", "value": file_name}
            config.createbyreq(config, config, params)
        
        return JsonResponse(msg)


def schemaName_follow_level(request, tableName, columnName, level, parent):
    """
    Get follow level data
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        # Get all models
        allModels = apps.get_app_config('main').get_models()
        model = None
        
        # Find the model by table name
        for m in allModels:
            if m.__tablename__ == tableName:
                model = m
                break
        
        if model is None:
            msg['code'] = crud_error_code
            msg['msg'] = "表不存在"
            return JsonResponse(msg)
        
        # Get follow level data
        data = model.objects.filter(**{columnName: parent}).all()
        result = []
        
        for item in data:
            result.append(model_to_dict(item))
        
        msg['data'] = result
        
        return JsonResponse(msg)


def schemaName_follow(request, tableName, columnName):
    """
    Get follow data
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        # Get all models
        allModels = apps.get_app_config('main').get_models()
        model = None
        
        # Find the model by table name
        for m in allModels:
            if m.__tablename__ == tableName:
                model = m
                break
        
        if model is None:
            msg['code'] = crud_error_code
            msg['msg'] = "表不存在"
            return JsonResponse(msg)
        
        # Get distinct values of the column
        data = model.objects.values(columnName).distinct()
        result = []
        
        for item in data:
            result.append(item[columnName])
        
        msg['data'] = result
        
        return JsonResponse(msg)


def schemaName_location(request):
    """
    Get location data
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        # This is a placeholder for location data
        # In a real implementation, this would use a location service
        
        return JsonResponse(msg)


def schemaName_matchface(request):
    """
    Match face recognition
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        req_dict = request.session.get("req_dict")
        face1 = req_dict.get("face1")
        face2 = req_dict.get("face2")
        
        if not face1 or not face2:
            msg['code'] = crud_error_code
            msg['msg'] = "人脸图片不能为空"
            return JsonResponse(msg)
        
        # Use BaiDuBce for face matching
        bdb = BaiDuBce()
        result = bdb.bd_check2pic(face1, face2)
        
        msg['data'] = result
        
        return JsonResponse(msg)


def schemaName_option(request, tableName, columnName):
    """
    Get option data for a column
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        # Get all models
        allModels = apps.get_app_config('main').get_models()
        model = None
        
        # Find the model by table name
        for m in allModels:
            if m.__tablename__ == tableName:
                model = m
                break
        
        if model is None:
            msg['code'] = crud_error_code
            msg['msg'] = "表不存在"
            return JsonResponse(msg)
        
        # Get distinct values of the column
        data = model.objects.values(columnName).distinct()
        result = []
        
        for item in data:
            result.append(item[columnName])
        
        msg['data'] = result
        
        return JsonResponse(msg)


def schemaName_sh(request, tableName):
    """
    Handle approval process
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        req_dict = request.session.get("req_dict")
        id = req_dict.get("id")
        sfsh = req_dict.get("sfsh")
        shhf = req_dict.get("shhf")
        
        if not id:
            msg['code'] = crud_error_code
            msg['msg'] = "缺少参数id"
            return JsonResponse(msg)
        
        # Get all models
        allModels = apps.get_app_config('main').get_models()
        model = None
        
        # Find the model by table name
        for m in allModels:
            if m.__tablename__ == tableName:
                model = m
                break
        
        if model is None:
            msg['code'] = crud_error_code
            msg['msg'] = "表不存在"
            return JsonResponse(msg)
        
        # Update approval status
        data = model.objects.get(id=id)
        data.sfsh = sfsh
        data.shhf = shhf
        data.save()
        
        return JsonResponse(msg)


def schemaName_upload(request, fileName):
    """
    Upload a file with a specific name
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        file = request.FILES.get("file")
        
        if not file:
            msg['code'] = crud_error_code
            msg['msg'] = "未上传文件"
            return JsonResponse(msg)
        
        # Save file with the specified name
        file_path = os.path.join(os.getcwd(), "templates/upload", fileName)
        
        with open(file_path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
        
        msg["file"] = fileName
        
        return JsonResponse(msg)


def schemaName_upload_forecast(request, tableName, fileName):
    """
    Upload a file for forecast
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        
        file = request.FILES.get("file")
        
        if not file:
            msg['code'] = crud_error_code
            msg['msg'] = "未上传文件"
            return JsonResponse(msg)
        
        # Save file with the specified name
        file_path = os.path.join(os.getcwd(), "templates/upload", fileName)
        
        with open(file_path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
        
        msg["file"] = fileName
        
        return JsonResponse(msg)
