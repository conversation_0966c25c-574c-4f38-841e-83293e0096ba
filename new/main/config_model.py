# coding:utf-8

from django.db import models

from .model import BaseModel


class config(BaseModel):
    """
    Configuration model
    """
    name = models.CharField(max_length=100, verbose_name=u'键名')
    value = models.CharField(max_length=100, verbose_name=u'键值')
    url = models.CharField(max_length=100, verbose_name=u'键值')

    __tablename__ = 'config'

    class Meta:
        db_table = 'config'
        verbose_name = verbose_name_plural = u'配置表'
