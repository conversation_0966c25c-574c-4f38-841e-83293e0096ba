# coding:utf-8

from django.http import JsonResponse

from .users_model import users
from util.codes import *
from util.auth import Auth
import util.message as mes
from util.CustomJSONEncoder import CustomJsonEncoder


def users_login(request):
    """
    User login function
    """
    if request.method in ["POST", "GET"]:
        msg = {'code': normal_code, "msg": mes.normal_code}
        req_dict = request.session.get("req_dict")
        
        # Remove role if present
        if req_dict.get('role') is not None:
            del req_dict['role']
        
        # Get user by parameters
        datas = users.getbyparams(users, users, req_dict)
        if not datas:
            msg['code'] = password_error_code
            msg['msg'] = mes.password_error_code
            return JsonResponse(msg)

        # Set id and authenticate
        req_dict['id'] = datas[0].get('id')
        return Auth.authenticate(Auth, users, req_dict)


def users_register(request):
    """
    User registration function
    """
    if request.method in ["POST", "GET"]:
        msg = {'code': normal_code, "msg": mes.normal_code}
        req_dict = request.session.get("req_dict")

        # Create user
        idOrErr = users.createbyreq(users, users, req_dict)
        if isinstance(idOrErr, Exception):
            msg['code'] = crud_error_code
            msg['msg'] = idOrErr
        else:
            msg['data'] = idOrErr
        
        return JsonResponse(msg)


def users_session(request):
    """
    Get user session information
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        # Get user by id
        req_dict = {"id": request.session.get('params').get("id")}
        msg['data'] = users.getbyparams(users, users, req_dict)[0]

        return JsonResponse(msg)


def users_logout(request):
    """
    User logout function
    """
    if request.method in ["POST", "GET"]:
        msg = {
            "msg": "退出成功",
            "code": 0
        }

        return JsonResponse(msg)


def users_page(request):
    """
    Get paginated users
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code,
               "data": {"currPage": 1, "totalPage": 1, "total": 1, "pageSize": 10, "list": []}}
        
        req_dict = request.session.get("req_dict")
        tablename = request.session.get("tablename")
        
        # Check if has message
        try:
            __hasMessage__ = users.__hasMessage__
        except:
            __hasMessage__ = None
        
        if __hasMessage__ and __hasMessage__ != "否":
            if tablename != "users":
                req_dict["userid"] = request.session.get("params").get("id")
        
        # Get paginated data
        if tablename == "users":
            msg['data']['list'], msg['data']['currPage'], msg['data']['totalPage'], msg['data']['total'], \
            msg['data']['pageSize'] = users.page(users, users, req_dict)
        else:
            msg['data']['list'], msg['data']['currPage'], msg['data']['totalPage'], msg['data']['total'], \
            msg['data']['pageSize'] = [], 1, 0, 0, 10

        return JsonResponse(msg)


def users_info(request, id_):
    """
    Get user information by id
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        # Get user by id
        data = users.getbyid(users, users, int(id_))
        if len(data) > 0:
            msg['data'] = data[0]
        
        # Update browse click count if needed
        try:
            __browseClick__ = users.__browseClick__
        except:
            __browseClick__ = None

        if __browseClick__ and "clicknum" in users.getallcolumn(users, users):
            click_dict = {"id": int(id_), "clicknum": str(int(data[0].get("clicknum", 0)) + 1)}
            ret = users.updatebyparams(users, users, click_dict)
            if ret is not None:
                msg['code'] = crud_error_code
                msg['msg'] = ret
        
        return JsonResponse(msg)


def users_save(request):
    """
    Save new user
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}

        req_dict = request.session.get("req_dict")
        
        # Check if username already exists
        if users.objects.filter(username=req_dict['username']).count() > 0:
            msg['code'] = crud_error_code
            msg['msg'] = "账户已存在"
            return JsonResponse(msg, encoder=CustomJsonEncoder)
        
        # Set role and create user
        req_dict['role'] = '管理员'
        idOrErr = users.createbyreq(users, users, req_dict)
        
        if isinstance(idOrErr, Exception):
            msg['code'] = crud_error_code
            msg['msg'] = idOrErr
        else:
            msg['data'] = idOrErr
        
        return JsonResponse(msg)


def users_update(request):
    """
    Update user information
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        req_dict = request.session.get("req_dict")
        
        # Handle password fields
        if req_dict.get("mima") and req_dict.get("password"):
            if "mima" not in users.getallcolumn(users, users):
                del req_dict["mima"]
            if "password" not in users.getallcolumn(users, users):
                del req_dict["password"]
        
        # Remove clicknum if present
        try:
            del req_dict["clicknum"]
        except:
            pass
        
        # Update user
        error = users.updatebyparams(users, users, req_dict)
        if error is not None:
            msg['code'] = crud_error_code
            msg['msg'] = error
        
        return JsonResponse(msg)


def users_delete(request):
    """
    Delete users by ids
    """
    if request.method in ["POST", "GET"]:
        msg = {"code": normal_code, "msg": mes.normal_code, "data": {}}
        req_dict = request.session.get("req_dict")

        # Delete users
        error = users.deletes(users, users, req_dict.get("ids"))
        if error is not None:
            msg['code'] = crud_error_code
            msg['msg'] = error
        
        return JsonResponse(msg)
