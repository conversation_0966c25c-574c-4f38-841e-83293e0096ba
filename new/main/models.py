# coding:utf-8
from django.db import models

from .model import BaseModel

from datetime import datetime


class yonghu(BaseModel):
    """
    用户模型
    """
    __doc__ = u'''yonghu'''
    __tablename__ = 'yonghu'
    __loginUser__ = 'yonghuming'
    __foreEndList__ = '否'  # 表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__ = '否'  # 表属性isAdmin="是",刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    yonghuming = models.CharField(max_length=255, null=False, unique=True, verbose_name='用户名')
    mima = models.CharField(max_length=255, null=False, unique=False, verbose_name='密码')
    xingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='姓名')
    xingbie = models.CharField(max_length=255, null=True, unique=False, verbose_name='性别')
    touxiang = models.TextField(null=True, unique=False, verbose_name='头像')
    nianling = models.IntegerField(null=True, unique=False, verbose_name='年龄')
    youxiang = models.CharField(max_length=255, null=False, unique=False, verbose_name='邮箱')
    shouji = models.CharField(max_length=255, null=False, unique=False, verbose_name='手机')

    class Meta:
        db_table = 'yonghu'
        verbose_name = verbose_name_plural = '用户'


class nonghu(BaseModel):
    """
    农户模型
    """
    __doc__ = u'''nonghu'''
    __tablename__ = 'nonghu'
    __loginUser__ = 'zhanghao'
    __foreEndList__ = '否'  # 表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__ = '否'  # 表属性isAdmin="是",刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    zhanghao = models.CharField(max_length=255, null=False, unique=True, verbose_name='账号')
    mima = models.CharField(max_length=255, null=False, unique=False, verbose_name='密码')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')
    touxiang = models.TextField(null=False, unique=False, verbose_name='头像')
    xingbie = models.CharField(max_length=255, null=False, unique=False, verbose_name='性别')
    nianling = models.IntegerField(null=True, unique=False, verbose_name='年龄')
    youxiang = models.CharField(max_length=255, null=False, unique=False, verbose_name='邮箱')
    dianhua = models.CharField(max_length=255, null=False, unique=False, verbose_name='电话')

    class Meta:
        db_table = 'nonghu'
        verbose_name = verbose_name_plural = '农户'


class guoyuanxinxi(BaseModel):
    """
    果园信息模型
    """
    __doc__ = u'''guoyuanxinxi'''
    __tablename__ = 'guoyuanxinxi'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guoyuanmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果园名称')
    guoyuantupian = models.TextField(null=False, unique=False, verbose_name='果园图片')
    guoyuandizhi = models.CharField(max_length=255, null=False, unique=False, verbose_name='果园地址')
    zhongzhipinzhong = models.CharField(max_length=255, null=False, unique=False, verbose_name='种植品种')
    zhongzhimianji = models.CharField(max_length=255, null=False, unique=False, verbose_name='种植面积')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')
    lianxidianhua = models.CharField(max_length=255, null=False, unique=False, verbose_name='联系电话')

    class Meta:
        db_table = 'guoyuanxinxi'
        verbose_name = verbose_name_plural = '果园信息'


class guoyuanzhuangtai(BaseModel):
    """
    果园状态模型
    """
    __doc__ = u'''guoyuanzhuangtai'''
    __tablename__ = 'guoyuanzhuangtai'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guoyuanmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果园名称')
    zhuangtaimingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='状态名称')
    zhuangtaitupian = models.TextField(null=False, unique=False, verbose_name='状态图片')
    zhuangtaimiaoshu = models.TextField(null=False, unique=False, verbose_name='状态描述')
    faburiqi = models.DateField(null=False, unique=False, verbose_name='发布日期')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')

    class Meta:
        db_table = 'guoyuanzhuangtai'
        verbose_name = verbose_name_plural = '果园状态'


class guopinxinxi(BaseModel):
    """
    果品信息模型
    """
    __doc__ = u'''guopinxinxi'''
    __tablename__ = 'guopinxinxi'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guopinmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品名称')
    guopintupian = models.TextField(null=False, unique=False, verbose_name='果品图片')
    guopinpinzhong = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品品种')
    zhiliangdengji = models.CharField(max_length=255, null=False, unique=False, verbose_name='质量等级')
    guopinjiage = models.FloatField(null=False, unique=False, verbose_name='果品价格')
    shengchanriqi = models.DateField(null=False, unique=False, verbose_name='生产日期')
    baozhiqi = models.CharField(max_length=255, null=False, unique=False, verbose_name='保质期')
    guopinmiaoshu = models.TextField(null=False, unique=False, verbose_name='果品描述')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')

    class Meta:
        db_table = 'guopinxinxi'
        verbose_name = verbose_name_plural = '果品信息'


class cangchu(BaseModel):
    """
    仓储模型
    """
    __doc__ = u'''cangchu'''
    __tablename__ = 'cangchu'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    cangkumingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='仓库名称')
    cangkutupian = models.TextField(null=False, unique=False, verbose_name='仓库图片')
    cangkudizhi = models.CharField(max_length=255, null=False, unique=False, verbose_name='仓库地址')
    rongliang = models.CharField(max_length=255, null=False, unique=False, verbose_name='容量')
    wendu = models.CharField(max_length=255, null=False, unique=False, verbose_name='温度')
    shidu = models.CharField(max_length=255, null=False, unique=False, verbose_name='湿度')
    cangkumiaoshu = models.TextField(null=False, unique=False, verbose_name='仓库描述')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')

    class Meta:
        db_table = 'cangchu'
        verbose_name = verbose_name_plural = '仓储'


class ruku(BaseModel):
    """
    入库模型
    """
    __doc__ = u'''ruku'''
    __tablename__ = 'ruku'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    rukubianhao = models.CharField(max_length=255, null=False, unique=True, verbose_name='入库编号')
    guopinmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品名称')
    guopinpinzhong = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品品种')
    zhiliangdengji = models.CharField(max_length=255, null=False, unique=False, verbose_name='质量等级')
    shuliang = models.IntegerField(null=False, unique=False, verbose_name='数量')
    rukushijian = models.DateTimeField(null=False, unique=False, verbose_name='入库时间')
    cangkumingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='仓库名称')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')

    class Meta:
        db_table = 'ruku'
        verbose_name = verbose_name_plural = '入库'


class chuku(BaseModel):
    """
    出库模型
    """
    __doc__ = u'''chuku'''
    __tablename__ = 'chuku'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    chukubianhao = models.CharField(max_length=255, null=False, unique=True, verbose_name='出库编号')
    guopinmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品名称')
    guopinpinzhong = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品品种')
    zhiliangdengji = models.CharField(max_length=255, null=False, unique=False, verbose_name='质量等级')
    shuliang = models.IntegerField(null=False, unique=False, verbose_name='数量')
    chukushijian = models.DateTimeField(null=False, unique=False, verbose_name='出库时间')
    cangkumingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='仓库名称')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')

    class Meta:
        db_table = 'chuku'
        verbose_name = verbose_name_plural = '出库'


class wuliu(BaseModel):
    """
    物流模型
    """
    __doc__ = u'''wuliu'''
    __tablename__ = 'wuliu'
    __authTables__ = {'zhanghao': 'nonghu'}
    __authPeople__ = '否'  # 用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__ = '否'  # 表sfsh(是否审核，"是"或"否")字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个"审核"按钮，点击"审核"弹出一个页面，包含"是否审核"和"审核回复"，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__ = '否'  # 后台列表权限
    __thumbsUp__ = '否'  # 表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__ = '否'  # 智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__ = '否'  # 表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__ = '否'  # 前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    wuliubianhao = models.CharField(max_length=255, null=False, unique=True, verbose_name='物流编号')
    guopinmingcheng = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品名称')
    guopinpinzhong = models.CharField(max_length=255, null=False, unique=False, verbose_name='果品品种')
    zhiliangdengji = models.CharField(max_length=255, null=False, unique=False, verbose_name='质量等级')
    shuliang = models.IntegerField(null=False, unique=False, verbose_name='数量')
    wuliugongsi = models.CharField(max_length=255, null=False, unique=False, verbose_name='物流公司')
    yunfei = models.FloatField(null=False, unique=False, verbose_name='运费')
    wuliuzhuangtai = models.CharField(max_length=255, null=False, unique=False, verbose_name='物流状态')
    beizhu = models.TextField(null=False, unique=False, verbose_name='备注')
    fahuoshijian = models.DateTimeField(null=False, unique=False, verbose_name='发货时间')
    zhanghao = models.CharField(max_length=255, null=False, unique=False, verbose_name='账号')
    nonghuxingming = models.CharField(max_length=255, null=False, unique=False, verbose_name='农户姓名')
    shouhuoren = models.CharField(max_length=255, null=False, unique=False, verbose_name='收货人')
    shouhuodizhi = models.CharField(max_length=255, null=False, unique=False, verbose_name='收货地址')
    lianxidianhua = models.CharField(max_length=255, null=False, unique=False, verbose_name='联系电话')

    class Meta:
        db_table = 'wuliu'
        verbose_name = verbose_name_plural = '物流'


class zhiliangdengji(BaseModel):
    """
    质量等级模型
    """
    __doc__ = u'''zhiliangdengji'''
    __tablename__ = 'zhiliangdengji'
    __foreEndList__ = '否'  # 表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__ = '否'  # 表属性isAdmin="是",刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    zhiliangdengji = models.CharField(max_length=255, null=False, unique=True, verbose_name='质量等级')

    class Meta:
        db_table = 'zhiliangdengji'
        verbose_name = verbose_name_plural = '质量等级'
