# coding:utf-8

import copy
import logging
import datetime
from django.db import models
from django.forms.models import model_to_dict
from django.core.paginator import Paginator
from django.db.models import ProtectedError
from threadlocals.threadlocals import get_current_request
from django.db.models import Su<PERSON>, <PERSON>, Min, Avg, Count
from django.db.models import Q


class BaseModel(models.Model):
    """
    Base model class for all models in the application
    """
    class Meta:
        abstract = True

    def __Retrieve(self, model):
        """
        Retrieve all records from the model
        """
        datas = model.objects.all()
        return self.to_list(datas, datas, model)

    def retrieve(self, model):
        """
        Retrieve all records from the model and format datetime fields
        """
        datas = self.__Retrieve(model, model)
        for i in datas:
            addtime = i.get("addtime")
            if addtime:
                addtime = str(addtime)[:19].replace("T", " ")
                i["addtime"] = addtime
        return datas

    def to_list(self, datas, datas_, model):
        """
        Convert QuerySet to list of dictionaries
        """
        result = []
        for data in datas_:
            if isinstance(data, dict):
                result.append(data)
            else:
                result.append(model_to_dict(data))
        return result

    def getallcolumn(self, model):
        """
        Get all column names of the model
        """
        column_list = []
        for col in model._meta.fields:
            column_list.append(col.name)
        return column_list

    def __Page(self, model, params, request={}, q=Q()):
        """
        Paginate records from the model
        """
        if not params:
            params = {}

        # Get page parameters
        page = int(params.get('page', 1))
        limit = int(params.get('limit', 10))
        sort = params.get('sort', 'id')
        
        # Handle ordering
        order = params.get('order', 'asc')
        if sort and sort != 'null':
            if order == 'desc':
                sort = '-' + sort
        else:
            sort = 'id'

        # Filter parameters
        new_params = {}
        for k, v in params.items():
            if k not in ['page', 'limit', 'sort', 'order', 'ids', 'token', 'random', 'new', 'userid', 'username']:
                new_params[k] = v

        # Apply filters
        data = model.objects.filter(**new_params).filter(q).order_by(sort)
        
        # Paginate results
        paginator = Paginator(data, limit)
        
        # Get page data
        try:
            data_page = paginator.page(page)
        except:
            data_page = paginator.page(paginator.num_pages)

        # Convert to list
        newData = self.to_list(data, data_page.object_list, model)
        
        # Format datetime fields
        filed_list = []
        from django.apps import apps
        modelobj = apps.get_model('main', model.__tablename__)
        for field in modelobj._meta.fields:
            if 'DateTimeField' in type(field).__name__:
                filed_list.append(field.name)

        for index, i in enumerate(newData):
            for k, v in i.items():
                if k in filed_list:
                    newData[index][k] = str(v)[:19]

        return newData, page, paginator.num_pages, paginator.count, limit

    def page(self, model, params, request={}, q=Q()):
        """
        Paginate records from the model
        """
        return self.__Page(self, model, params, request, q)

    def __GetByColumn(self, model, columnName, new_params):
        """
        Get distinct values of a column from the model
        """
        datas = model.objects.values(columnName).filter(**new_params).all()
        data_set = set()
        for i in datas:
            data_set.add(i.get(columnName))
        return list(data_set)

    def getbycolumn(self, model, columnName, params={}):
        """
        Get distinct values of a column from the model
        """
        return self.__GetByColumn(model, model, columnName, params)

    def __CreateByReq(self, model, params):
        """
        Create a new record in the model
        """
        column_list = []
        for col in model._meta.fields:
            column_list.append(col.name)
        
        paramss = {}
        for k, v in params.items():
            if k in column_list:
                paramss[k] = v
        
        paramss["addtime"] = datetime.datetime.now()
        m = model(**paramss)
        
        try:
            ret = m.save()
            return m.id
        except Exception as e:
            return "{}:{}".format(Exception, e)

    def createbyreq(self, model, params):
        """
        Create a new record in the model
        """
        return self.__CreateByReq(model, model, params)

    def __GetById(self, model, id):
        """
        Get a record by id
        """
        data = model.objects.filter(id=id).all()
        return self.to_list(model, data, model)

    def getbyid(self, model, id):
        """
        Get a record by id
        """
        return self.__GetById(model, model, id)

    def __GetByParams(self, model, params):
        """
        Get records by parameters
        """
        try:
            __loginUser__ = model.__loginUser__
        except:
            __loginUser__ = None

        if __loginUser__ is not None and __loginUser__ != 'username':
            if params.get('username'):
                params[model.__loginUser__] = copy.deepcopy(params.get('username'))
                del params['username']
        
        if model.__tablename__ != 'users':
            if params.get('password'):
                params['mima'] = copy.deepcopy(params.get('password'))

        # Filter parameters
        paramss = {}
        columnList = self.getallcolumn(model, model)
        for k, v in params.items():
            if k in columnList:
                paramss[k] = v

        datas_ = model.objects.filter(**paramss).all()
        return self.to_list(datas_, datas_, model)

    def getbyparams(self, model, params):
        """
        Get records by parameters
        """
        return self.__GetByParams(model, model, params)

    def __UpdateByParams(self, model, params):
        """
        Update a record by parameters
        """
        try:
            id = int(params['id'])
        except:
            return "No id"

        # Get the record
        data = model.objects.get(id=id)
        if not data:
            return "No data"

        # Update fields
        for k, v in params.items():
            if k != 'id':
                setattr(data, k, v)
        
        try:
            data.save()
            return None
        except Exception as e:
            return "{}:{}".format(Exception, e)

    def updatebyparams(self, model, params):
        """
        Update a record by parameters
        """
        return self.__UpdateByParams(model, model, params)

    def __Deletes(self, model, ids):
        """
        Delete records by ids
        """
        if not ids:
            return "No id"

        if isinstance(ids, str):
            ids = ids.split(',')
        
        try:
            model.objects.filter(id__in=ids).delete()
            return None
        except ProtectedError as e:
            return "{}:{}".format(ProtectedError, e)
        except Exception as e:
            return "{}:{}".format(Exception, e)

    def deletes(self, model, ids):
        """
        Delete records by ids
        """
        return self.__Deletes(model, model, ids)
