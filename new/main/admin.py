# coding:utf-8
from django.contrib import admin
from django.apps import apps

from dj2.settings import schemaName
from main.users_model import users
from main.config_model import config

try:
    from main.models import *
except:
    pass

# Change admin site title
admin.site.site_title = schemaName  # Set page title
admin.site.site_header = schemaName  # Set website header
admin.site.index_title = schemaName  # Set index title

# Register all models from the main app
allModels = apps.get_app_config('main').get_models()

for model in allModels:
    class ModelAdmin(admin.ModelAdmin):
        """
        Dynamic ModelAdmin class for all models
        """
        list_display = []
        for col in model._meta.fields:
            list_display.append(col.name)

        search_fields = list_display

    admin.site.register(model, ModelAdmin)
