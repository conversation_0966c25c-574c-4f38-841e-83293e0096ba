# coding:utf-8

import os
from django.urls import path
from main import users_v, config_v, schema_v

# URL rules list
urlpatterns = [
    # User routes
    path(r'users/register', users_v.users_register),
    path(r'users/login', users_v.users_login),
    path(r'users/logout', users_v.users_logout),
    path(r'users/session', users_v.users_session),
    path(r'users/page', users_v.users_page),
    path(r'users/save', users_v.users_save),
    path(r'users/info/<id_>', users_v.users_info),
    path(r'users/update', users_v.users_update),
    path(r'users/delete', users_v.users_delete),

    # Config routes
    path(r'config/page', config_v.config_page),
    path(r'config/list', config_v.config_list),
    path(r'config/save', config_v.config_save),
    path(r'config/add', config_v.config_add),
    path(r'config/info/<id_>', config_v.config_info),
    path(r'config/detail/<id_>', config_v.config_detail),
    path(r'config/update', config_v.config_update),
    path(r'config/delete', config_v.config_delete),
]

# Main app path
mainDir = os.path.join(os.getcwd(), "main")

# Files to exclude from auto-loading
excludeList = [
    "schema_v.py",
    "users_v.py",
    "config_v.py",
]

# Dynamically load view modules
view_tuple = set()
for i in os.listdir(mainDir):
    if i not in excludeList and i.endswith("_v.py"):
        viewName = i[:-3]  # Remove .py suffix
        view_tuple.add("from main import {}".format(viewName))

# Execute imports
for i in view_tuple:
    exec(i)

# Dynamically add routes for each model
for i in os.listdir(mainDir):
    if i not in excludeList and i.endswith("_v.py"):
        tableName = i[:-5]  # Remove _v.py suffix
        
        # Skip if not a model view file
        if tableName == "schema":
            continue
        
        # Add standard routes for each model
        urlpatterns.extend([
            path(r'{}/register'.format(tableName.lower()),
                 eval("{}_v.{}_register".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/login'.format(tableName.lower()),
                 eval("{}_v.{}_login".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/logout'.format(tableName.lower()),
                 eval("{}_v.{}_logout".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/resetPass'.format(tableName.lower()),
                 eval("{}_v.{}_resetPass".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/session'.format(tableName.lower()),
                 eval("{}_v.{}_session".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/default'.format(tableName.lower()),
                 eval("{}_v.{}_default".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/page'.format(tableName.lower()),
                 eval("{}_v.{}_page".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/autoSort'.format(tableName.lower()),
                 eval("{}_v.{}_autoSort".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/list'.format(tableName.lower()),
                 eval("{}_v.{}_list".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/save'.format(tableName.lower()),
                 eval("{}_v.{}_save".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/add'.format(tableName.lower()),
                 eval("{}_v.{}_add".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/info/<id_>'.format(tableName.lower()),
                 eval("{}_v.{}_info".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/detail/<id_>'.format(tableName.lower()),
                 eval("{}_v.{}_detail".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/update'.format(tableName.lower()),
                 eval("{}_v.{}_update".format(tableName.capitalize(), tableName.lower()))),
            path(r'{}/delete'.format(tableName.lower()),
                 eval("{}_v.{}_delete".format(tableName.capitalize(), tableName.lower()))),
        ])

# Add schema routes
urlpatterns.extend([
    path(r'cal/<str:tableName>/<str:columnName>', schema_v.schemaName_cal),
    path(r'file/download', schema_v.schemaName_file_download),
    path(r'file/upload', schema_v.schemaName_file_upload),
    path(r'follow/<tableName>/<columnName>/<level>/<parent>', schema_v.schemaName_follow_level),
    path(r'follow/<tableName>/<columnName>', schema_v.schemaName_follow),
    path(r'location', schema_v.schemaName_location),
    path(r'matchFace', schema_v.schemaName_matchface),
    path(r'option/<tableName>/<columnName>', schema_v.schemaName_option),
    path(r'sh/<tableName>', schema_v.schemaName_sh),
    path(r'upload/<fileName>', schema_v.schemaName_upload),
    path(r'upload/<tableName>/<fileName>', schema_v.schemaName_upload_forecast),
])
