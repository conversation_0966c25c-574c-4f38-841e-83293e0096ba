# coding:utf-8
import os
from django.http import JsonResponse, HttpResponse
from django.apps import apps

def index(request):
    """
    Index view function
    """
    if request.method in ["GET", "POST"]:
        msg = {"code": 200, "msg": "success", "data": []}
        return JsonResponse(msg)

def test(request, p1):
    """
    Test view function with parameter
    """
    if request.method in ["GET", "POST"]:
        msg = {"code": 200, "msg": "success", "data": []}
        return JsonResponse(msg)

def null(request):
    """
    Null view function
    """
    if request.method in ["GET", "POST"]:
        msg = {"code": 200, "msg": "success", "data": []}
        return JsonResponse(msg)

def admin_lib2(request, p1, p2):
    """
    Admin lib2 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/lib", p1, p2)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_lib3(request, p1, p2, p3):
    """
    Admin lib3 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/lib", p1, p2, p3)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_lib4(request, p1, p2, p3, p4):
    """
    Admin lib4 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/lib", p1, p2, p3, p4)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_page(request, p1):
    """
    Admin page view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/page", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_page2(request, p1, p2):
    """
    Admin page2 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/page", p1, p2)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_pages(request, p1):
    """
    Admin pages view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/pages", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def admin_pages2(request, p1, p2):
    """
    Admin pages2 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/pages", p1, p2)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def layui1(request, p1):
    """
    Layui1 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/layui", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def layui2(request, p1, p2):
    """
    Layui2 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/layui", p1, p2)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def layui3(request, p1, p2, p3):
    """
    Layui3 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/layui", p1, p2, p3)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def layui4(request, p1, p2, p3, p4):
    """
    Layui4 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/admin/layui", p1, p2, p3, p4)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def front_pages(request, p1):
    """
    Front pages view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/pages", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def front_pages2(request, p1, p2):
    """
    Front pages2 view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/pages", p1, p2)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def front_modules(request, p1):
    """
    Front modules view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/modules", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)

def css1(request, p1):
    """
    CSS view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/css", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read(), content_type="text/css")
        return HttpResponse("File not found", status=404)

def js1(request, p1):
    """
    JS view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/js", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read(), content_type="application/javascript")
        return HttpResponse("File not found", status=404)

def img1(request, p1):
    """
    Image view function
    """
    if request.method in ["GET", "POST"]:
        file_path = os.path.join(os.getcwd(), "templates/front/img", p1)
        if os.path.isfile(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read())
        return HttpResponse("File not found", status=404)
