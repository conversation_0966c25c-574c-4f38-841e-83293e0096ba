# coding:utf-8

from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.apps import apps

from util.auth import Auth
from util.codes import *
from dj2.settings import schemaName


class Xauth(MiddlewareMixin):
    """
    Authentication middleware
    """
    def process_request(self, request):
        """
        Process request for authentication
        :param request: Request object
        :return: Response or None
        """
        fullPath = request.get_full_path()
        
        # Skip for WebSocket connections
        if request.META.get('HTTP_UPGRADE') == 'websocket':
            return
        
        if request.method == 'GET':
            # List of paths that don't require authentication
            filterList = [
                "/index",
                "/follow",
                "/favicon.ico",
                "/login",
                "/register",
                "/notify",
                "/file",
                '.js',
                ".css",
                ".jpg",
                ".jpeg",
                ".png",
                ".gif",
                ".mp4",
                '.mp3',
                ".ttf",
                ".wotf",
                ".woff",
                ".woff2",
                ".otf",
                ".eot",
                ".svg",
                ".csv",
                ".webp",
                ".xls",
                ".xlsx",
                ".doc",
                ".docx",
                ".ppt",
                ".pptx",
                ".html",
                ".htm",
                "detail",
                "/forum/flist",
                "/forum/list",
                "/admin",
                "/security",
                "/autoSort",
                "/config/list",
                "/news/list",
                "/xadmin",
                "/file/download",
                "/{}/remind/".format(schemaName),
                "/{}/option/".format(schemaName),
                "resetPass",
                "updateBrowseDuration",
            ]

            # Add model-specific paths that don't require authentication
            allModels = apps.get_app_config('main').get_models()
            for m in allModels:
                try:
                    foreEndList = m.__foreEndList__
                except:
                    foreEndList = None
                
                if foreEndList is None or foreEndList != "前要登":
                    filterList.append("/{}/sendemail".format(m.__tablename__))
                    filterList.append("/{}/sendsms".format(m.__tablename__))
                    filterList.append("/{}/list".format(m.__tablename__))
                    filterList.append("/{}/detail".format(m.__tablename__))

            # Check if authentication is required
            auth = True

            if fullPath == '/':
                pass
            else:
                for i in filterList:
                    if i in fullPath:
                        auth = False
                
                if auth:
                    # Authenticate request
                    result = Auth.identify(Auth, request)

                    if result.get('code') != normal_code:
                        return JsonResponse(result)
        
        elif request.method == 'POST':
            # List of POST paths that don't require authentication
            post_list = [
                '/{}/defaultuser/register'.format(schemaName),
                '/{}/defaultuser/login'.format(schemaName),
                '/{}/users/register'.format(schemaName),
                '/{}/users/login'.format(schemaName),
                "/{}/examusers/login".format(schemaName),
                "/{}/examusers/register".format(schemaName),
                "/{}/file/upload".format(schemaName),
                "/update"
            ]
            
            # Check if authentication is required
            if fullPath not in post_list and "register" not in fullPath and "login" not in fullPath and "faceLogin" not in fullPath and "update" not in fullPath and "upload" not in fullPath:
                # Authenticate request
                result = Auth.identify(Auth, request)

                if result.get('code') != normal_code:
                    return JsonResponse(result)
