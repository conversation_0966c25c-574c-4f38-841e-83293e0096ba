# coding:utf-8
import json
import copy
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse


class Xparam(MiddlewareMixin):
    """
    Parameter processing middleware
    """
    def process_request(self, request):
        """
        Process request parameters
        :param request: Request object
        :return: None
        """
        fullPath = request.get_full_path()
        
        # Skip for WebSocket connections
        if request.META.get('HTTP_UPGRADE') == 'websocket':
            return
        
        # Skip for static files
        if "/js/" not in fullPath and "/css/" not in fullPath and "/img/" not in fullPath and "/fonts/" not in fullPath and "/front/" not in fullPath:
            req_dict = {}

            # Get query parameters
            for k, v in request.GET.items():
                req_dict[k] = v

            if request.method == "POST":
                contentType = request.META.get('CONTENT_TYPE', "")
                
                if "json" in contentType:
                    # Parse application/json parameters
                    try:
                        data_ = json.loads(request.body)
                        if isinstance(data_, list):
                            req_dict['ids'] = data_
                        else:
                            for k, v in data_.items():
                                req_dict[k] = v
                    except Exception as e:
                        # Get form data parameters if JSON parsing fails
                        for k, v in request.POST.items():
                            req_dict[k] = v
                else:
                    # Get form data parameters
                    for k, v in request.POST.items():
                        req_dict[k] = v
            
            # Process parameters, remove useless and incorrect parameters
            if req_dict.get("created") is not None:
                req_dict['addtime'] = copy.deepcopy(req_dict.get("created"))
                del req_dict["created"]
            
            if req_dict.get("t") is not None:
                del req_dict["t"]
            
            if req_dict.get("1") is not None:
                req_dict['type'] = copy.deepcopy(req_dict.get("1"))
                del req_dict["1"]
            
            # Store processed parameters in session
            request.session["req_dict"] = req_dict
