#coding:utf-8
from django.db import models

from .model import BaseModel

from datetime import datetime



class yonghu(BaseModel):
    __doc__ = u'''yonghu'''
    __tablename__ = 'yonghu'

    __loginUser__='yonghuming'


    __authTables__={}
    __authPeople__='是'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __loginUserColumn__='yonghuming'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    yonghuming=models.CharField ( max_length=255,null=False,unique=True, verbose_name='用户名' )
    mima=models.CharField ( max_length=255,null=False, unique=False, verbose_name='密码' )
    xingming=models.CharField ( max_length=255,null=False, unique=False, verbose_name='姓名' )
    xingbie=models.CharField ( max_length=255, null=True, unique=False, verbose_name='性别' )
    touxiang=models.TextField   (  null=True, unique=False, verbose_name='头像' )
    nianling=models.IntegerField  (  null=True, unique=False, verbose_name='年龄' )
    youxiang=models.CharField ( max_length=255,null=False, unique=False, verbose_name='邮箱' )
    shouji=models.CharField ( max_length=255,null=False, unique=False, verbose_name='手机' )
    '''
    yonghuming=VARCHAR
    mima=VARCHAR
    xingming=VARCHAR
    xingbie=VARCHAR
    touxiang=Text
    nianling=Integer
    youxiang=VARCHAR
    shouji=VARCHAR
    '''
    class Meta:
        db_table = 'yonghu'
        verbose_name = verbose_name_plural = '用户'
class nonghu(BaseModel):
    __doc__ = u'''nonghu'''
    __tablename__ = 'nonghu'

    __loginUser__='zhanghao'


    __authTables__={}
    __authPeople__='是'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __loginUserColumn__='zhanghao'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    zhanghao=models.CharField ( max_length=255,null=False,unique=True, verbose_name='账号' )
    mima=models.CharField ( max_length=255,null=False, unique=False, verbose_name='密码' )
    nonghuxingming=models.CharField ( max_length=255,null=False, unique=False, verbose_name='农户姓名' )
    touxiang=models.TextField   ( null=False, unique=False, verbose_name='头像' )
    xingbie=models.CharField ( max_length=255,null=False, unique=False, verbose_name='性别' )
    nianling=models.IntegerField  (  null=True, unique=False, verbose_name='年龄' )
    youxiang=models.CharField ( max_length=255,null=False, unique=False, verbose_name='邮箱' )
    dianhua=models.CharField ( max_length=255,null=False, unique=False, verbose_name='电话' )
    '''
    zhanghao=VARCHAR
    mima=VARCHAR
    nonghuxingming=VARCHAR
    touxiang=Text
    xingbie=VARCHAR
    nianling=Integer
    youxiang=VARCHAR
    dianhua=VARCHAR
    '''
    class Meta:
        db_table = 'nonghu'
        verbose_name = verbose_name_plural = '农户'
class guoyuanxinxi(BaseModel):
    __doc__ = u'''guoyuanxinxi'''
    __tablename__ = 'guoyuanxinxi'



    __authTables__={'zhanghao':'nonghu',}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guoyuanbianhao=models.CharField ( max_length=255,null=False,unique=True, verbose_name='果园编号' )
    guoyuanmingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='果园名称' )
    guoyuanguimo=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园规模' )
    guoyuanmianji=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园面积' )
    guoyuanweizhi=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园位置' )
    zhongzhiqingkuang=models.TextField   ( null=False, unique=False, verbose_name='种植情况' )
    zhanghao=models.CharField ( max_length=255,null=False, unique=False, verbose_name='账号' )
    nonghuxingming=models.CharField ( max_length=255, null=True, unique=False, verbose_name='农户姓名' )
    '''
    guoyuanbianhao=VARCHAR
    guoyuanmingcheng=VARCHAR
    guoyuanguimo=VARCHAR
    guoyuanmianji=VARCHAR
    guoyuanweizhi=VARCHAR
    zhongzhiqingkuang=Text
    zhanghao=VARCHAR
    nonghuxingming=VARCHAR
    '''
    class Meta:
        db_table = 'guoyuanxinxi'
        verbose_name = verbose_name_plural = '果园信息'
class guoyuanzhuangtai(BaseModel):
    __doc__ = u'''guoyuanzhuangtai'''
    __tablename__ = 'guoyuanzhuangtai'



    __authTables__={'zhanghao':'nonghu',}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guoyuanmingcheng=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园名称' )
    guoyuanmianji=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园面积' )
    guoyuanweizhi=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果园位置' )
    shifei=models.FloatField   ( null=False, unique=False, verbose_name='施肥(g)' )
    jiaoshuiliang=models.FloatField   ( null=False, unique=False, verbose_name='浇水量(ml)' )
    zhongzhizhuangkuang=models.TextField   ( null=False, unique=False, verbose_name='种植状况' )
    zhanghao=models.CharField ( max_length=255, null=True, unique=False, verbose_name='账号' )
    nonghuxingming=models.CharField ( max_length=255, null=True, unique=False, verbose_name='农户姓名' )
    jilushijian=models.DateTimeField  (  null=True, unique=False, verbose_name='记录时间' )
    '''
    guoyuanmingcheng=VARCHAR
    guoyuanmianji=VARCHAR
    guoyuanweizhi=VARCHAR
    shifei=Float
    jiaoshuiliang=Float
    zhongzhizhuangkuang=Text
    zhanghao=VARCHAR
    nonghuxingming=VARCHAR
    jilushijian=DateTime
    '''
    class Meta:
        db_table = 'guoyuanzhuangtai'
        verbose_name = verbose_name_plural = '果园状态'
class wuliu(BaseModel):
    __doc__ = u'''wuliu'''
    __tablename__ = 'wuliu'



    __authTables__={}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guopinmingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='果品名称' )
    wuliugongsi=models.CharField ( max_length=255,null=False, unique=False, verbose_name='物流公司' )
    danhao=models.CharField ( max_length=255,null=False, unique=False, verbose_name='单号' )
    '''
    guopinmingcheng=VARCHAR
    wuliugongsi=VARCHAR
    danhao=VARCHAR
    '''
    class Meta:
        db_table = 'wuliu'
        verbose_name = verbose_name_plural = '物流'
class guopinxinxi(BaseModel):
    __doc__ = u'''guopinxinxi'''
    __tablename__ = 'guopinxinxi'



    __authTables__={'zhanghao':'nonghu',}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guopinbianhao=models.CharField ( max_length=255,null=False,unique=True, verbose_name='果品编号' )
    guopinmingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='果品名称' )
    zhonglei=models.CharField ( max_length=255,null=False, unique=False, verbose_name='种类' )
    danwei=models.CharField ( max_length=255,null=False, unique=False, verbose_name='单位' )
    chandi=models.CharField ( max_length=255,null=False, unique=False, verbose_name='产地' )
    zhiliangdengji=models.CharField ( max_length=255,null=False, unique=False, verbose_name='质量等级' )
    shengchanriqi=models.DateField   ( null=False, unique=False, verbose_name='生产日期' )
    wuliusuyuan=models.CharField ( max_length=255,null=False, unique=False, verbose_name='物流溯源' )
    danhao=models.CharField ( max_length=255, null=True, unique=False, verbose_name='单号' )
    cangcunxinxi=models.CharField ( max_length=255,null=False, unique=False, verbose_name='仓存信息' )
    zhaopian=models.TextField   ( null=False, unique=False, verbose_name='照片' )
    xuliehao=models.CharField ( max_length=255, null=True, unique=False, verbose_name='序列号' )
    beizhu=models.CharField ( max_length=255, null=True, unique=False, verbose_name='备注' )
    zhanghao=models.CharField ( max_length=255, null=True, unique=False, verbose_name='账号' )
    nonghuxingming=models.CharField ( max_length=255, null=True, unique=False, verbose_name='农户姓名' )
    '''
    guopinbianhao=VARCHAR
    guopinmingcheng=VARCHAR
    zhonglei=VARCHAR
    danwei=VARCHAR
    chandi=VARCHAR
    zhiliangdengji=VARCHAR
    shengchanriqi=Date
    wuliusuyuan=VARCHAR
    danhao=VARCHAR
    cangcunxinxi=VARCHAR
    zhaopian=Text
    xuliehao=VARCHAR
    beizhu=VARCHAR
    zhanghao=VARCHAR
    nonghuxingming=VARCHAR
    '''
    class Meta:
        db_table = 'guopinxinxi'
        verbose_name = verbose_name_plural = '果品信息'
class cangchu(BaseModel):
    __doc__ = u'''cangchu'''
    __tablename__ = 'cangchu'



    __authTables__={}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    cangchumingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='仓储名称' )
    guopinmingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='果品名称' )
    kucun=models.IntegerField  ( null=False, unique=False, verbose_name='在库数量' )
    cangchuweizhi=models.CharField ( max_length=255,null=False, unique=False, verbose_name='仓储位置' )
    '''
    cangchumingcheng=VARCHAR
    guopinmingcheng=VARCHAR
    kucun=Integer
    cangchuweizhi=VARCHAR
    '''
    class Meta:
        db_table = 'cangchu'
        verbose_name = verbose_name_plural = '仓储'
class ruku(BaseModel):
    __doc__ = u'''ruku'''
    __tablename__ = 'ruku'



    __authTables__={}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    guopinmingcheng=models.CharField ( max_length=255, null=True, unique=False, verbose_name='果品名称' )
    rukubianhao=models.CharField ( max_length=255, null=True,unique=True, verbose_name='入库编号' )
    kucun=models.IntegerField  ( null=False, unique=False, verbose_name='入库数量' )
    rukushuoming=models.TextField   ( null=False, unique=False, verbose_name='入库说明' )
    rukushijian=models.DateTimeField  (  null=True, unique=False, verbose_name='入库时间' )
    '''
    guopinmingcheng=VARCHAR
    rukubianhao=VARCHAR
    kucun=Integer
    rukushuoming=Text
    rukushijian=DateTime
    '''
    class Meta:
        db_table = 'ruku'
        verbose_name = verbose_name_plural = '入库'
class chuku(BaseModel):
    __doc__ = u'''chuku'''
    __tablename__ = 'chuku'



    __authTables__={}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    chukubianhao=models.CharField ( max_length=255, null=True,unique=True, verbose_name='出库编号' )
    guopinmingcheng=models.CharField ( max_length=255,null=False, unique=False, verbose_name='果品名称' )
    kucun=models.IntegerField  ( null=False, unique=False, verbose_name='出库数量' )
    chukushuoming=models.TextField   ( null=False, unique=False, verbose_name='出库说明' )
    chukushijian=models.DateTimeField  (  null=True, unique=False, verbose_name='出库时间' )
    '''
    chukubianhao=VARCHAR
    guopinmingcheng=VARCHAR
    kucun=Integer
    chukushuoming=Text
    chukushijian=DateTime
    '''
    class Meta:
        db_table = 'chuku'
        verbose_name = verbose_name_plural = '出库'
class zhiliangdengji(BaseModel):
    __doc__ = u'''zhiliangdengji'''
    __tablename__ = 'zhiliangdengji'



    __authTables__={}
    __authPeople__='否'#用户表，表属性loginUserColumn对应的值就是用户名字段，mima就是密码字段
    __sfsh__='否'#表sfsh(是否审核，”是”或”否”)字段和sfhf(审核回复)字段，后台列表(page)的操作中要多一个”审核”按钮，点击”审核”弹出一个页面，包含”是否审核”和”审核回复”，点击确定调用update接口，修改sfsh和sfhf两个字段。
    __authSeparate__='否'#后台列表权限
    __thumbsUp__='否'#表属性thumbsUp[是/否]，新增thumbsupnum赞和crazilynum踩字段
    __intelRecom__='否'#智能推荐功能(表属性：[intelRecom（是/否）],新增clicktime[前端不显示该字段]字段（调用info/detail接口的时候更新），按clicktime排序查询)
    __browseClick__='否'#表属性[browseClick:是/否]，点击字段（clicknum），调用info/detail接口的时候后端自动+1）、投票功能（表属性[vote:是/否]，投票字段（votenum）,调用vote接口后端votenum+1
    __foreEndListAuth__='否'#前台列表权限foreEndListAuth[是/否]；当foreEndListAuth=是，刷的表新增用户字段userid，前台list列表接口仅能查看自己的记录和add接口后台赋值userid的值
    __foreEndList__='否'#表属性[foreEndList]前台list:和后台默认的list列表页相似,只是摆在前台,否:指没有此页,是:表示有此页(不需要登陆即可查看),前要登:表示有此页且需要登陆后才能查看
    __isAdmin__='否'#表属性isAdmin=”是”,刷出来的用户表也是管理员，即page和list可以查看所有人的考试记录(同时应用于其他表)
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    zhiliangdengji=models.CharField ( max_length=255,null=False,unique=True, verbose_name='质量等级' )
    '''
    zhiliangdengji=VARCHAR
    '''
    class Meta:
        db_table = 'zhiliangdengji'
        verbose_name = verbose_name_plural = '质量等级'
