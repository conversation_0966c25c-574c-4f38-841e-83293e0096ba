# coding:utf-8

from django.db import models

from .model import BaseModel


class users(BaseModel):
    # id=models.BigIntegerField(verbose_name="自增id")
    username = models.CharField(max_length=100, verbose_name=u'账号')
    password = models.Char<PERSON>ield(max_length=100, verbose_name=u'密码')
    role = models.CharField(max_length=100, verbose_name=u'角色')
    addtime = models.DateTimeField(auto_now_add=False, verbose_name=u'创建时间')
    __isAdmin__ = '是'
    image = models.CharField(max_length=100, verbose_name=u'头像')

    __tablename__ = 'users'

    class Meta:
        db_table = 'users'
        verbose_name = verbose_name_plural = u'管理员表'

    # def __str__(self):
    #     return self.username
