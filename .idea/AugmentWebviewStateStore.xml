<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="eyJjdXJyZW50Q29udmVyc2F0aW9uSWQiOiIzZGMwODBjMC0wMWI2LTQyNTAtODgzNy1jNWNmZjFhMTVkYjgiLCJjb252ZXJzYXRpb25zIjp7ImViOTBmMDAzLWQyMmYtNGRmOC05ZWJhLWNjYmNlNmJiNDZhZSI6eyJpZCI6ImViOTBmMDAzLWQyMmYtNGRmOC05ZWJhLWNjYmNlNmJiNDZhZSIsIm5hbWUiOiJXZWxjb21lIHRvIHRoZSBBdWdtZW50IEFnZW50IiwiY3JlYXRlZEF0SXNvIjoiMjAyNS0wNS0wM1QwNjoyMDoyNy44NTJaIiwibGFzdEludGVyYWN0ZWRBdElzbyI6IjIwMjUtMDUtMDNUMDY6Mzg6MzAuMzYzWiIsImNoYXRIaXN0b3J5IjpbeyJyZXF1ZXN0X2lkIjoiNjlmMWMyODEtMTIzMi00ZDgwLTgzYTktYTFlMjcxOTBlNzM0IiwidXVpZCI6ImY3ZjY1Yjg3LTAzNTItNDQ3ZC1hZWU4LTg1YzhiZGFjM2VkYiIsImNoYXRJdGVtVHlwZSI6ImFnZW50aWMtY2hlY2twb2ludC1kZWxpbWl0ZXIiLCJzdGF0dXMiOiJzdWNjZXNzIiwiZnJvbVRpbWVzdGFtcCI6MCwidG9UaW1lc3RhbXAiOjE3NDYyNTMyMjc4NTUsInNlZW5fc3RhdGUiOiJzZWVuIn0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjA5NTQ4OGE3LWZjODQtNDUwMi04NmQwLTEzMjI4ZjM5NjVkZSIsInJlcXVlc3RfbWVzc2FnZSI6Iuivt+WujOWFqOinhOiMg+WMlumHjeaehOivpeeoi+W6j++8jCDph43mnoTlkI7nmoTnqIvluo/kv53lrZjlnKhuZXfnm67lvZXkuIvjgIIiLCJyaWNoX3RleHRfanNvbl9yZXByIjp7InR5cGUiOiJkb2MiLCJjb250ZW50IjpbeyJ0eXBlIjoicGFyYWdyYXBoIiwiY29udGVudCI6W3sidHlwZSI6InRleHQiLCJ0ZXh0Ijoi6K+35a6M5YWo6KeE6IyD5YyW6YeN5p6E6K+l56iL5bqP77yMIOmHjeaehOWQjueahOeoi+W6j+S/neWtmOWcqG5ld+ebruW9leS4i+OAgiJ9XX1dfSwibWVudGlvbmVkX2l0ZW1zIjpbXSwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxMjJoTEFkYlJZUlE0dzlhb3NjY25DaiIsInRvb2xfbmFtZSI6ImxhdW5jaC1wcm9jZXNzIiwiaW5wdXRfanNvbiI6IntcImNvbW1hbmRcIjogXCJmaW5kIC4gLXR5cGUgZiAtbmFtZSBcXFwiKi5weVxcXCIgfCBzb3J0XCIsIFwid2FpdFwiOiB0cnVlLCBcIm1heF93YWl0X3NlY29uZHNcIjogNjB9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiSSdsbCBoZWxwIHlvdSBjb21wbGV0ZWx5IHJlZmFjdG9yIHRoZSBwcm9ncmFtIGFuZCBzYXZlIHRoZSByZWZhY3RvcmVkIHZlcnNpb24gaW4gYSBuZXcgZGlyZWN0b3J5LiBMZXQgbWUgZmlyc3QgdW5kZXJzdGFuZCB0aGUgY3VycmVudCBzdHJ1Y3R1cmUgb2YgdGhlIGNvZGViYXNlIHRvIGNyZWF0ZSBhIHByb3BlciByZWZhY3RvcmluZyBwbGFuLlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MCwidGV4dF9ub2RlIjp7ImNvbnRlbnQiOiLor7flrozlhajop4TojIPljJbph43mnoTor6XnqIvluo/vvIwg6YeN5p6E5ZCO55qE56iL5bqP5L+d5a2Y5ZyobmV355uu5b2V5LiL44CCIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyMjowMy45MjNaIiwicmVzcG9uc2VfdGV4dCI6IkknbGwgaGVscCB5b3UgY29tcGxldGVseSByZWZhY3RvciB0aGUgcHJvZ3JhbSBhbmQgc2F2ZSB0aGUgcmVmYWN0b3JlZCB2ZXJzaW9uIGluIGEgbmV3IGRpcmVjdG9yeS4gTGV0IG1lIGZpcnN0IHVuZGVyc3RhbmQgdGhlIGN1cnJlbnQgc3RydWN0dXJlIG9mIHRoZSBjb2RlYmFzZSB0byBjcmVhdGUgYSBwcm9wZXIgcmVmYWN0b3JpbmcgcGxhbi5cbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI3NmNhNDkzOC0zNTA5LTRiNTQtOTlhYy00ZWM2NDg1NjVkZTQiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFDeTQxeXBQUVlhNExLeGpFbXQ4bXgxIiwidG9vbF9uYW1lIjoiY29kZWJhc2UtcmV0cmlldmFsIiwiaW5wdXRfanNvbiI6IntcImluZm9ybWF0aW9uX3JlcXVlc3RcIjogXCJTaG93IG1lIHRoZSBzdHJ1Y3R1cmUgb2YgdGhlIERqYW5nbyBwcm9qZWN0LCBpbmNsdWRpbmcgdGhlIG1haW4vdXJscy5weSBmaWxlLCBkajIvdXJscy5weSwgZGoyL3NldHRpbmdzLnB5LCBhbmQgYW55IG1haW4gbW9kZWwgZmlsZXMgdG8gdW5kZXJzdGFuZCB0aGUgb3ZlcmFsbCBhcmNoaXRlY3R1cmUuXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiSSdsbCBoZWxwIHlvdSBjb21wbGV0ZWx5IHJlZmFjdG9yIHRoZSBwcm9ncmFtIGFuZCBzYXZlIHRoZSByZWZhY3RvcmVkIHZlcnNpb24gaW4gYSBuZXcgZGlyZWN0b3J5LiBMZXQgbWUgZmlyc3QgdW5kZXJzdGFuZCB0aGUgY3VycmVudCBzdHJ1Y3R1cmUgb2YgdGhlIGNvZGViYXNlIHRvIGNyZWF0ZSBhIHByb3BlciByZWZhY3RvcmluZyBwbGFuLlxuXG5MZXQgbWUgZXhhbWluZSB0aGUgbWFpbiBzdHJ1Y3R1cmUgb2YgdGhlIHByb2plY3QgYW5kIHNvbWUga2V5IGZpbGVzIHRvIHVuZGVyc3RhbmQgaG93IHRvIGJlc3QgcmVmYWN0b3IgaXQuXG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJIZXJlIGFyZSB0aGUgcmVzdWx0cyBmcm9tIGV4ZWN1dGluZyB0aGUgY29tbWFuZC5cbiAgICAgICAgPHJldHVybi1jb2RlPlxuICAgICAgICAwXG4gICAgICAgIDwvcmV0dXJuLWNvZGU+XG4gICAgICAgIDxzdGRvdXQ+XG4gICAgICAgIC4vZGoyL19faW5pdF9fLnB5XG4uL2RqMi9zZXR0aW5ncy5weVxuLi9kajIvdXJscy5weVxuLi9kajIvdmlld3MucHlcbi4vZGoyL3dzZ2kucHlcbi4vbWFpbi9hZG1pbi5weVxuLi9tYWluL2FwcHMucHlcbi4vbWFpbi9DYW5nY2h1X3YucHlcbi4vbWFpbi9DaHVrdV92LnB5XG4uL21haW4vY29uZmlnX21vZGVsLnB5XG4uL21haW4vY29uZmlnX3YucHlcbi4vbWFpbi9HdW9waW54aW54aV92LnB5XG4uL21haW4vR3VveXVhbnhpbnhpX3YucHlcbi4vbWFpbi9HdW95dWFuemh1YW5ndGFpX3YucHlcbi4vbWFpbi9fX2luaXRfXy5weVxuLi9tYWluL21vZGVsLnB5XG4uL21haW4vbW9kZWxzLnB5XG4uL21haW4vTm9uZ2h1X3YucHlcbi4vbWFpbi9SdWt1X3YucHlcbi4vbWFpbi9zY2hlbWFfdi5weVxuLi9tYWluL3Rlc3RzLnB5XG4uL21haW4vdXJscy5weVxuLi9tYWluL3VzZXJzX21vZGVsLnB5XG4uL21haW4vdXNlcnNfdi5weVxuLi9tYWluL3ZpZXdzLnB5XG4uL21haW4vV3VsaXVfdi5weVxuLi9tYWluL1lvbmdodV92LnB5XG4uL21haW4vWmhpbGlhbmdkZW5namlfdi5weVxuLi9tYW5hZ2UucHlcbi4vdXRpbC9hdXRoLnB5XG4uL3V0aWwvYmFpZHViY2VfYXBpLnB5XG4uL3V0aWwvY29kZXMucHlcbi4vdXRpbC9jb21tb24ucHlcbi4vdXRpbC9jb25maWdyZWFkLnB5XG4uL3V0aWwvQ3VzdG9tSlNPTkVuY29kZXIucHlcbi4vdXRpbC9fX2luaXRfXy5weVxuLi91dGlsL2xvY2F0ZS5weVxuLi91dGlsL21lc3NhZ2UucHlcbi4vdXRpbC9zcWxpbml0LnB5XG4uL3htaWRkbGV3YXJlL19faW5pdF9fLnB5XG4uL3htaWRkbGV3YXJlL3hhdXRoLnB5XG4uL3htaWRkbGV3YXJlL3hwYXJhbS5weVxuXG4gICAgICAgIDwvc3Rkb3V0PlxuICAgICAgICA8c3RkZXJyPlxuICAgICAgICBcbiAgICAgICAgPC9zdGRlcnI+IiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxMjJoTEFkYlJZUlE0dzlhb3NjY25DaiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjoyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyMzozNi4yOTdaIiwicmVzcG9uc2VfdGV4dCI6IkknbGwgaGVscCB5b3UgY29tcGxldGVseSByZWZhY3RvciB0aGUgcHJvZ3JhbSBhbmQgc2F2ZSB0aGUgcmVmYWN0b3JlZCB2ZXJzaW9uIGluIGEgbmV3IGRpcmVjdG9yeS4gTGV0IG1lIGZpcnN0IHVuZGVyc3RhbmQgdGhlIGN1cnJlbnQgc3RydWN0dXJlIG9mIHRoZSBjb2RlYmFzZSB0byBjcmVhdGUgYSBwcm9wZXIgcmVmYWN0b3JpbmcgcGxhbi5cblxuTGV0IG1lIGV4YW1pbmUgdGhlIG1haW4gc3RydWN0dXJlIG9mIHRoZSBwcm9qZWN0IGFuZCBzb21lIGtleSBmaWxlcyB0byB1bmRlcnN0YW5kIGhvdyB0byBiZXN0IHJlZmFjdG9yIGl0LlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjkyYjczN2U4LTk3YzAtNGVhYi04N2FkLWQwZjNiMmJmY2Q1NSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFhSmt1SHlaYmVvbXpzNVBaM2VpdmsiLCJ0b29sX25hbWUiOiJjb2RlYmFzZS1yZXRyaWV2YWwiLCJpbnB1dF9qc29uIjoie1wiaW5mb3JtYXRpb25fcmVxdWVzdFwiOiBcIlNob3cgbWUgdGhlIHN0cnVjdHVyZSBvZiBvbmUgb2YgdGhlIHZpZXcgZmlsZXMgbGlrZSB1c2Vyc192LnB5IGFuZCBjb25maWdfdi5weSB0byB1bmRlcnN0YW5kIHRoZSB2aWV3IGltcGxlbWVudGF0aW9uIHBhdHRlcm5zLlwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IkxldCBtZSBleGFtaW5lIHNvbWUgb2YgdGhlIHZpZXcgZmlsZXMgdG8gdW5kZXJzdGFuZCBob3cgdGhleSdyZSBzdHJ1Y3R1cmVkOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogZGoyL3VybHMucHlcbi4uLlxuZnJvbSBkamFuZ28udmlld3MuZ2VuZXJpYyBpbXBvcnQgVGVtcGxhdGVWaWV3XG5cbmZyb20gLiBpbXBvcnQgdmlld3NcbiMgZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5cbnNjaGVtYU5hbWUgPSBcImRqYW5nb2syZ2c2MzczXCJcblxudXJscGF0dGVybnMgPSBbXG4gICAgcGF0aCgneGFkbWluLycsIGFkbWluLnNpdGUudXJscyksXG4gICAgcGF0aChyJ2luZGV4Lycsdmlld3MuaW5kZXgpLFxuICAgIHBhdGgoJ3t9LycuZm9ybWF0KHNjaGVtYU5hbWUpLGluY2x1ZGUoJ21haW4udXJscycpKSwj5a+85YWlc2NoZW1hTmFtZVxuICAgIHJlX3BhdGgocidhZG1pbi9saWIvKD9QPHAxPi4qKS8oP1A8cDI+LiopJCcsIHZpZXdzLmFkbWluX2xpYjIpLFxuICAgIHJlX3BhdGgocidhZG1pbi9saWIvKD9QPHAxPi4qKS8oP1A8cDI+LiopLyg/UDxwMz4uKikkJywgdmlld3MuYWRtaW5fbGliMyksXG4gICAgcmVfcGF0aChyJ2FkbWluL2xpYi8oP1A8cDE+LiopLyg/UDxwMj4uKikvKD9QPHAzPi4qKS8oP1A8cDQ+LiopJCcsIHZpZXdzLmFkbWluX2xpYjQpLFxuICAgIHJlX3BhdGgocidhZG1pbi9wYWdlLyg/UDxwMT4uKikkJywgdmlld3MuYWRtaW5fcGFnZSksXG4gICAgcmVfcGF0aChyJ2FkbWluL3BhZ2UvKD9QPHAxPi4qKS8oP1A8cDI+LiopJCcsIHZpZXdzLmFkbWluX3BhZ2UyKSxcbiAgICByZV9wYXRoKHInYWRtaW4vcGFnZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlcyksXG4gICAgcmVfcGF0aChyJ2FkbWluL3BhZ2VzLyg/UDxwMT4uKikvKD9QPHAyPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlczIpLFxuLi4uXG4gICAgcmVfcGF0aChyJ2xheXVpLyg/UDxwMT4uKikvKD9QPHAyPi4qKS8oP1A8cDM+LiopLyg/UDxwND4uKikkJywgIHZpZXdzLmxheXVpNCksXG4gICAgcmVfcGF0aChyJ3BhZ2VzLyg/UDxwMT4uKikkJywgdmlld3MuZnJvbnRfcGFnZXMpLFxuICAgIHJlX3BhdGgocidwYWdlcy8oP1A8cDE+LiopLyg/UDxwMj4uKikkJywgIHZpZXdzLmZyb250X3BhZ2VzMiksXG4gICAgIyByZV9wYXRoKHIncGFnZXMvKD9QPHAxPi4qKSQnLCAgdmlld3MuZnJvbnRfZmlsZTEpLFxuICAgICMgcmVfcGF0aChyJyg/UDxwMT5jc3N8anNzfGltZ3xpbWFnZXxpYW1nZXN8Zm9udHxmb250cykvKD9QPHAyPi4qKSQnLCB2aWV3cy5mcm9udF9maWxlMiksXG4gICAgcmVfcGF0aChyJ21vZHVsZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5mcm9udF9tb2R1bGVzKSxcbiAgICByZV9wYXRoKHInY3NzLyg/UDxwMT4uKikkJywgdmlld3MuY3NzMSksXG4gICAgcmVfcGF0aChyJ2pzLyg/UDxwMT4uKikkJywgdmlld3MuanMxKSxcbiAgICByZV9wYXRoKHInaW1nLyg/UDxwMT4uKikkJywgdmlld3MuaW1nMSksXG4gICAgcGF0aChyJ3Rlc3QvPHN0cjpwMT4vJyx2aWV3cy50ZXN0KSxcbiAgICBwYXRoKHInbnVsbCcsdmlld3MubnVsbCksXG5dXG4uLi5cblBhdGg6IG1haW4vdXJscy5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IG9zXG5mcm9tIGRqYW5nby51cmxzIGltcG9ydCBwYXRoXG5mcm9tIG1haW4gaW1wb3J0IHVzZXJzX3YsIGNvbmZpZ192LCBzY2hlbWFfdlxuIyBmcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgZGJOYW1lIGFzIHNjaGVtYU5hbWVcbiMgdXJs6KeE5YiZ5YiX6KGoXG51cmxwYXR0ZXJucyA9IFtcbiAgICBwYXRoKHIndXNlcnMvcmVnaXN0ZXInLCB1c2Vyc192LnVzZXJzX3JlZ2lzdGVyKSxcbiAgICBwYXRoKHIndXNlcnMvbG9naW4nLCB1c2Vyc192LnVzZXJzX2xvZ2luKSxcbiAgICBwYXRoKHIndXNlcnMvbG9nb3V0JywgdXNlcnNfdi51c2Vyc19sb2dvdXQpLFxuICAgIHBhdGgocid1c2Vycy9zZXNzaW9uJywgdXNlcnNfdi51c2Vyc19zZXNzaW9uKSxcbiAgICBwYXRoKHIndXNlcnMvcGFnZScsIHVzZXJzX3YudXNlcnNfcGFnZSksXG4gICAgcGF0aChyJ3VzZXJzL3NhdmUnLCB1c2Vyc192LnVzZXJzX3NhdmUpLFxuICAgIHBhdGgocid1c2Vycy9pbmZvLzxpZF8+JywgdXNlcnNfdi51c2Vyc19pbmZvKSxcbiAgICBwYXRoKHIndXNlcnMvdXBkYXRlJywgdXNlcnNfdi51c2Vyc191cGRhdGUpLFxuICAgIHBhdGgocid1c2Vycy9kZWxldGUnLCB1c2Vyc192LnVzZXJzX2RlbGV0ZSksXG5cbiAgICBwYXRoKHInY29uZmlnL3BhZ2UnLCBjb25maWdfdi5jb25maWdfcGFnZSksXG4gICAgcGF0aChyJ2NvbmZpZy9saXN0JywgY29uZmlnX3YuY29uZmlnX2xpc3QpLFxuICAgIHBhdGgocidjb25maWcvc2F2ZScsIGNvbmZpZ192LmNvbmZpZ19zYXZlKSxcbiAgICBwYXRoKHInY29uZmlnL2FkZCcsIGNvbmZpZ192LmNvbmZpZ19hZGQpLFxuICAgIHBhdGgocidjb25maWcvaW5mby88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19pbmZvKSxcbiAgICBwYXRoKHInY29uZmlnL2RldGFpbC88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19kZXRhaWwpLFxuICAgIHBhdGgocidjb25maWcvdXBkYXRlJywgY29uZmlnX3YuY29uZmlnX3VwZGF0ZSksXG4gICAgcGF0aChyJ2NvbmZpZy9kZWxldGUnLCBjb25maWdfdi5jb25maWdfZGVsZXRlKSxcblxuXVxuIyBtYWluIGFwcOeahOi3r+W+hFxubWFpbkRpciA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJtYWluXCIpXG5cbiMg6L+H5ruk5paH5Lu255qE5YiX6KGoXG5leGNsdWRlTGlzdCA9IFtcbiAgICBcInNjaGVtYV92LnB5XCIsXG5cbiAgICBcInVzZXJzX3YucHlcIixcbiAgICBcImNvbmZpZ192LnB5XCIsXG5dXG5cbiMg5b6q546v5b2T5YmN55uu5b2V5LiL55qEcHnmlofku7Zcblxudmlld190dXBsZSA9IHNldCgpXG5mb3IgaSBpbiBvcy5saXN0ZGlyKG1haW5EaXIpOlxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpWy01Ol0gPT0gXCJfdi5weVwiOlxuICAgICAgICB2aWV3TmFtZSA9IGlbOi0zXSAgIyDljrvmjokucHnlkI7nvIDlrZfnrKbkuLJcbiAgICAgICAgdmlld190dXBsZS5hZGQoXCJmcm9tIG1haW4gaW1wb3J0IHt9XCIuZm9ybWF0KHZpZXdOYW1lKSlcbi4uLlxuXG4gICAgICAgIHVybHBhdHRlcm5zLmV4dGVuZChcbiAgICAgICAgICAgIFtcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vcmVnaXN0ZXInLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9yZWdpc3RlclwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vbG9naW4nLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9sb2dpblwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vbG9nb3V0Jy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxuICAgICAgICAgICAgICAgICAgICAgZXZhbChcInt9X3Yue31fbG9nb3V0XCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxuICAgICAgICAgICAgICAgIHBhdGgocid7fS9yZXNldFBhc3MnLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9yZXNldFBhc3NcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXG4gICAgICAgICAgICAgICAgcGF0aChyJ3t9L3Nlc3Npb24nLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9zZXNzaW9uXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxuICAgICAgICAgICAgICAgIHBhdGgocid7fS9kZWZhdWx0Jy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxuICAgICAgICAgICAgICAgICAgICAgZXZhbChcInt9X3Yue31fZGVmYXVsdFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbi4uLlxudXJscGF0dGVybnMuZXh0ZW5kKFxuICAgIFtcbiAgICAgICAgcGF0aChyJ2NhbC88c3RyOnRhYmxlTmFtZT4vPHN0cjpjb2x1bW5OYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfY2FsKSxcbiAgICAgICAgcGF0aChyJ2ZpbGUvZG93bmxvYWQnLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZpbGVfZG93bmxvYWQpLFxuICAgICAgICBwYXRoKHInZmlsZS91cGxvYWQnLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZpbGVfdXBsb2FkKSxcbiAgICAgICAgcGF0aChyJ2ZvbGxvdy88dGFibGVOYW1lPi88Y29sdW1uTmFtZT4vPGxldmVsPi88cGFyZW50PicsIHNjaGVtYV92LnNjaGVtYU5hbWVfZm9sbG93X2xldmVsKSxcbiAgICAgICAgcGF0aChyJ2ZvbGxvdy88dGFibGVOYW1lPi88Y29sdW1uTmFtZT4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZvbGxvdyksXG4gICAgICAgIHBhdGgocidsb2NhdGlvbicsIHNjaGVtYV92LnNjaGVtYU5hbWVfbG9jYXRpb24pLFxuICAgICAgICBwYXRoKHInbWF0Y2hGYWNlJywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9tYXRjaGZhY2UpLFxuICAgICAgICBwYXRoKHInb3B0aW9uLzx0YWJsZU5hbWU+Lzxjb2x1bW5OYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfb3B0aW9uKSxcbiAgICAgICAgcGF0aChyJ3NoLzx0YWJsZU5hbWU+Jywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9zaCksXG4gICAgICAgIHBhdGgocid1cGxvYWQvPGZpbGVOYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfdXBsb2FkKSxcbiAgICAgICAgcGF0aChyJ3VwbG9hZC88dGFibGVOYW1lPi88ZmlsZU5hbWU+Jywgc2NoZW1hX3Yuc2NoZW1hTmFtZV91cGxvYWRfZm9yZWNhc3QpLFxuLi4uXG5QYXRoOiBtYWluL2FkbWluLnB5XG5mcm9tIGRqYW5nby5jb250cmliIGltcG9ydCBhZG1pblxuXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzLCBBcHBDb25maWdcbiMgUmVnaXN0ZXIgeW91ciBtb2RlbHMgaGVyZS5cblxuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5mcm9tIG1haW4udXNlcnNfbW9kZWwgaW1wb3J0IHVzZXJzXG5mcm9tIG1haW4uY29uZmlnX21vZGVsIGltcG9ydCBjb25maWdcblxudHJ5OlxuICAgIGZyb20gbWFpbi5tb2RlbHMgaW1wb3J0ICpcbmV4Y2VwdDpcbiAgICBwYXNzXG4jIGNoYW5nZSB0aXRsZVxuYWRtaW4uc2l0ZS5zaXRlX3RpdGxlID0gc2NoZW1hTmFtZSAgIyDorr7nva7pobXpnaLmoIfpophcbmFkbWluLnNpdGUuc2l0ZV9oZWFkZXIgPSBzY2hlbWFOYW1lICAjIOiuvue9rue9keermemhteWktFxuYWRtaW4uc2l0ZS5pbmRleF90aXRsZSA9IHNjaGVtYU5hbWUgICMg6K6+572u6aaW6aG15qCH6K+tXG5cbmFsbE1vZGVscyA9IGFwcHMuZ2V0X2FwcF9jb25maWcoJ21haW4nKS5nZXRfbW9kZWxzKClcblxuZm9yIGluZCwgbW9kZWwgaW4gZW51bWVyYXRlKGFsbE1vZGVscyk6XG5cbiAgICBjbGFzcyBtb2RlbHNpdGUoYWRtaW4uTW9kZWxBZG1pbik6XG4gICAgICAgIGxpc3RfZGlzcGxheSA9IFtdXG4gICAgICAgIGZvciBjb2wgaW4gbW9kZWwuX21ldGEuZmllbGRzOlxuICAgICAgICAgICAgbGlzdF9kaXNwbGF5LmFwcGVuZChjb2wubmFtZSlcblxuICAgICAgICBzZWFyY2hfZmllbGRzID0gbGlzdF9kaXNwbGF5XG5cblxuICAgIGFkbWluLnNpdGUucmVnaXN0ZXIobW9kZWwsIG1vZGVsc2l0ZSlcbi4uLlxuUGF0aDogbWFpbi9tb2RlbHMucHlcbiNjb2Rpbmc6dXRmLThcbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5mcm9tIGRhdGV0aW1lIGltcG9ydCBkYXRldGltZVxuXG5cblxuY2xhc3MgeW9uZ2h1KEJhc2VNb2RlbCk6XG4gICAgX19kb2NfXyA9IHUnJyd5b25naHUnJydcbiAgICBfX3RhYmxlbmFtZV9fID0gJ3lvbmdodSdcblxuICAgIF9fbG9naW5Vc2VyX189J3lvbmdodW1pbmcnXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHlvbmdodW1pbmc9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0n55So5oi35ZCNJyApXG4gICAgbWltYT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WvhueggScgKVxuICAgIHhpbmdtaW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5aeT5ZCNJyApXG4gICAgeGluZ2JpZT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+aAp+WIqycgKVxuICAgIHRvdXhpYW5nPW1vZGVscy5UZXh0RmllbGQgICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflpLTlg48nIClcbiAgICBuaWFubGluZz1tb2RlbHMuSW50ZWdlckZpZWxkICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflubTpvoQnIClcbiAgICB5b3V4aWFuZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+mCrueusScgKVxuICAgIHNob3VqaT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+aJi+acuicgKVxuICAgICcnJ1xuICAgIHlvbmdodW1pbmc9VkFSQ0hBUlxuICAgIG1pbWE9VkFSQ0hBUlxuICAgIHhpbmdtaW5nPVZBUkNIQVJcbiAgICB4aW5nYmllPVZBUkNIQVJcbiAgICB0b3V4aWFuZz1UZXh0XG4gICAgbmlhbmxpbmc9SW50ZWdlclxuICAgIHlvdXhpYW5nPVZBUkNIQVJcbiAgICBzaG91amk9VkFSQ0hBUlxuICAgICcnJ1xuICAgIGNsYXNzIE1ldGE6XG4gICAgICAgIGRiX3RhYmxlID0gJ3lvbmdodSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICfnlKjmiLcnXG5jbGFzcyBub25naHUoQmFzZU1vZGVsKTpcbiAgICBfX2RvY19fID0gdScnJ25vbmdodScnJ1xuICAgIF9fdGFibGVuYW1lX18gPSAnbm9uZ2h1J1xuXG4gICAgX19sb2dpblVzZXJfXz0nemhhbmdoYW8nXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHpoYW5naGFvPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J+i0puWPtycgKVxuICAgIG1pbWE9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflr4bnoIEnIClcbiAgICBub25naHV4aW5nbWluZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WGnOaIt+Wnk+WQjScgKVxuICAgIHRvdXhpYW5nPW1vZGVscy5UZXh0RmllbGQgICAoIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflpLTlg48nIClcbiAgICB4aW5nYmllPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5oCn5YirJyApXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHpoaWxpYW5nZGVuZ2ppPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J+i0qOmHj+etiee6pycgKVxuICAgICcnJ1xuICAgIHpoaWxpYW5nZGVuZ2ppPVZBUkNIQVJcbiAgICAnJydcbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBkYl90YWJsZSA9ICd6aGlsaWFuZ2RlbmdqaSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICfotKjph4/nrYnnuqcnXG4uLi5cblBhdGg6IGRqMi92aWV3cy5weVxuIyBjb2Rpbmc6dXRmLThcbmltcG9ydCBvcyxzeXNcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZSwgSHR0cFJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5cbmRlZiBpbmRleChyZXF1ZXN0KTpcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJHRVRcIiwgXCJQT1NUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IDIwMCwgXCJtc2dcIjogXCJzdWNjZXNzXCIsIFwiZGF0YVwiOiBbXX1cbiAgICAgICAgcHJpbnQoXCI9PT09PT09PT09PT09PT09PT5pbmRleFwiKVxuICAgICAgICAjIGFsbE1vZGVscyA9IGFwcHMuZ2V0X2FwcF9jb25maWcoJ21haW4nKS5nZXRfbW9kZWxzKClcbiAgICAgICAgIyBmb3IgbSBpbiBhbGxNb2RlbHM6XG4gICAgICAgICMgICAgIHByaW50KG0uX190YWJsZW5hbWVfXylcbiAgICAgICAgIyAgICAgcHJpbnQoZGlyKG0pKVxuICAgICAgICAjICAgICAjIGZvciBjb2wgaW4gbS5fbWV0YS5maWVsZHM6XG4gICAgICAgICMgICAgICMgICAgIHByaW50KFwiY29sIG5hbWU9PT09PT09PT09PT0+XCIsY29sLm5hbWUpXG4gICAgICAgICMgICAgICMgICAgIHByaW50KFwiY29sIHR5cGU9PT09PT09PT09PT0+XCIsY29sLmdldF9pbnRlcm5hbF90eXBlKCkpXG4gICAgICAgICMgcHJpbnQoYWxsTW9kZWxzKVxuXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiB0ZXN0KHJlcXVlc3QsIHAxKTpcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJHRVRcIiwgXCJQT1NUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IDIwMCwgXCJtc2dcIjogXCJzdWNjZXNzXCIsIFwiZGF0YVwiOiBbXX1cbiAgICAgICAgcHJpbnQoXCI9PT09PT09PT09PT09PT09PT5pbmRleCAgXCIsIHAxKVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcbi4uLlxuUGF0aDogbWFpbi9jb25maWdfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIGNvbmZpZyhCYXNlTW9kZWwpOlxuICAgICMgaWQ9bW9kZWxzLkJpZ0ludGVnZXJGaWVsZCh2ZXJib3NlX25hbWU9XCLoh6rlop5pZFwiKVxuICAgIG5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfplK7lkI0nKVxuICAgIHZhbHVlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6ZSu5YC8JylcbiAgICB1cmwgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfplK7lgLwnKVxuXG4gICAgX190YWJsZW5hbWVfXyA9ICdjb25maWcnXG5cbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBkYl90YWJsZSA9ICdjb25maWcnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+mFjee9ruihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYubmFtZVxuLi4uXG5QYXRoOiBtYWluL05vbmdodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBub25naHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9hcHBzLnB5XG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBBcHBDb25maWdcblxuXG5jbGFzcyBNYWluQ29uZmlnKEFwcENvbmZpZyk6XG4gICAgbmFtZSA9ICdtYWluJ1xuICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSByJ+WIt+ihqOaooeWdlydcbi4uLlxuUGF0aDogbWFpbi9DYW5nY2h1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IGNhbmdjaHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogZGoyL3NldHRpbmdzLnB5XG5cIlwiXCJcbkRqYW5nbyBzZXR0aW5ncyBmb3IgZGoyIHByb2plY3QuXG5cbkdlbmVyYXRlZCBieSAnZGphbmdvLWFkbWluIHN0YXJ0cHJvamVjdCcgdXNpbmcgRGphbmdvIDIuMC5cblxuRm9yIG1vcmUgaW5mb3JtYXRpb24gb24gdGhpcyBmaWxlLCBzZWVcbmh0dHBzOi8vZG9jcy5kamFuZ29wcm9qZWN0LmNvbS9lbi8yLjAvdG9waWNzL3NldHRpbmdzL1xuXG5Gb3IgdGhlIGZ1bGwgbGlzdCBvZiBzZXR0aW5ncyBhbmQgdGhlaXIgdmFsdWVzLCBzZWVcbmh0dHBzOi8vZG9jcy5kamFuZ29wcm9qZWN0LmNvbS9lbi8yLjAvcmVmL3NldHRpbmdzL1xuXCJcIlwiXG5pbXBvcnQgb3NcbmZyb20gdXRpbC5jb25maWdyZWFkIGltcG9ydCBjb25maWdfcmVhZFxuXG4jIEJ1aWxkIHBhdGhzIGluc2lkZSB0aGUgcHJvamVjdCBsaWtlIHRoaXM6IG9zLnBhdGguam9pbihCQVNFX0RJUiwgLi4uKVxuQkFTRV9ESVIgPSBvcy5wYXRoLmRpcm5hbWUob3MucGF0aC5kaXJuYW1lKG9zLnBhdGguYWJzcGF0aChfX2ZpbGVfXykpKVxuXG4jIFF1aWNrLXN0YXJ0IGRldmVsb3BtZW50IHNldHRpbmdzIC0gdW5zdWl0YWJsZSBmb3IgcHJvZHVjdGlvblxuIyBTZWUgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9ob3d0by9kZXBsb3ltZW50L2NoZWNrbGlzdC9cblxuIyBTRUNVUklUWSBXQVJOSU5HOiBrZWVwIHRoZSBzZWNyZXQga2V5IHVzZWQgaW4gcHJvZHVjdGlvbiBzZWNyZXQhXG5TRUNSRVRfS0VZID0gJ3c1eW4jMGduMnR0N3B2dSVodnd0MCFsdD0hJDYrZXFwNCVtOCl1M3UjZ2tubUBqbSlrJ1xuLi4uXG5DT1JTX0FMTE9XX0NSRURFTlRJQUxTID0gVHJ1ZVxuQ09SU19PUklHSU5fQUxMT1dfQUxMID0gVHJ1ZVxuQ09SU19BTExPV19IRUFERVJTID0gKCcqJylcblxuU0VTU0lPTl9FTkdJTkUgPSBcImRqYW5nby5jb250cmliLnNlc3Npb25zLmJhY2tlbmRzLmNhY2hlXCJcblNFU1NJT05fQ09PS0lFX05BTUUgID0gXCJzZXNzaW9uaWRcIlxuU0VTU0lPTl9DT09LSUVfUEFUSCAgPSBcIi9cIlxuU0VTU0lPTl9DT09LSUVfRE9NQUlOID0gTm9uZVxuU0VTU0lPTl9DT09LSUVfU0VDVVJFID0gRmFsc2VcblNFU1NJT05fQ09PS0lFX0hUVFBPTkxZID0gVHJ1ZVxuU0VTU0lPTl9DT09LSUVfQUdFID0gMTIwOTYwMFxuU0VTU0lPTl9FWFBJUkVfQVRfQlJPV1NFUl9DTE9TRSA9IEZhbHNlXG5TRVNTSU9OX1NBVkVfRVZFUllfUkVRVUVTVCA9IEZhbHNlXG4uLi5cblxuV1NHSV9BUFBMSUNBVElPTiA9ICdkajIud3NnaS5hcHBsaWNhdGlvbidcblxuRU1BSUxfQkFDS0VORCA9ICdkamFuZ28uY29yZS5tYWlsLmJhY2tlbmRzLnNtdHAuRW1haWxCYWNrZW5kJ1xuRU1BSUxfVVNFX1RMUyA9IEZhbHNlXG5FTUFJTF9VU0VfU1NMID0gVHJ1ZVxuRU1BSUxfSE9TVCA9ICdzbXRwLnFxLmNvbSdcbkVNQUlMX1BPUlQgPSA0NjVcbkVNQUlMX0hPU1RfVVNFUiA9ICd5Y2x3OUBxcS5jb20nXG5FTUFJTF9IT1NUX1BBU1NXT1JEID0gJ21oYnJrdWF5dmtrZ2JpamQnXG5cbiMgRGF0YWJhc2VcbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9yZWYvc2V0dGluZ3MvI2RhdGFiYXNlc1xuXG4jIERBVEFCQVNFUyA9IHtcbiMgICAgICdkZWZhdWx0Jzoge1xuIyAgICAgICAgICdFTkdJTkUnOiAnZGphbmdvLmRiLmJhY2tlbmRzLnNxbGl0ZTMnLFxuIyAgICAgICAgICdOQU1FJzogb3MucGF0aC5qb2luKEJBU0VfRElSLCAnZGIuc3FsaXRlMycpLFxuIyAgICAgfVxuIyB9XG5cbmRidHlwZSwgaG9zdCwgcG9ydCwgdXNlciwgcGFzc3dkLCBkYk5hbWUsIGNoYXJzZXQgPSBjb25maWdfcmVhZChcImNvbmZpZy5pbmlcIilcbmRiTmFtZT1kYk5hbWUucmVwbGFjZShcIiBcIixcIlwiKS5zdHJpcCgpXG5wcmludChkYnR5cGUsIGhvc3QsIHBvcnQsIHVzZXIsIHBhc3N3ZCwgZGJOYW1lLCBjaGFyc2V0KVxuXG4jIGlmIGRidHlwZSA9PSAnbXlzcWwnOlxuREFUQUJBU0VTID0ge1xuICAgICdkZWZhdWx0Jzoge1xuICAgICAgICAgICAjICdFTkdJTkUnOiAnZGphbmdvLmRiLmJhY2tlbmRzLnNxbGl0ZTMnLFxuICAgICAgICAgICAjICdOQU1FJzogb3MucGF0aC5qb2luKEJBU0VfRElSLCAnZGIuc3FsaXRlMycpLFxuICAgICAgICAnRU5HSU5FJzogJ2RqYW5nby5kYi5iYWNrZW5kcy5teXNxbCcsXG4gICAgICAgICdPUFRJT05TJzoge1xuICAgICAgICAgICAgIyAnc3FsX21vZGUnOiAndHJhZGl0aW9uYWwnLFxuICAgICAgICAgICAgIydpbml0X2NvbW1hbmQnOiBcIlNFVCBzcWxfbW9kZT0ndHJhZGl0aW9uYWwnXCIsICAjIFNUUklDVF9UUkFOU19UQUJMRVNcbiAgICAgICAgICAgICdjaGFyc2V0JzogJ3V0ZjhtYjQnLFxuICAgICAgICAgICAgIyAndXNlX3VuaWNvZGUnOiBUcnVlLFxuICAgICAgICB9LFxuICAgICAgICAnTkFNRSc6ICdHcmFkdWF0ZVByb2plY3QnLFxuICAgICAgICAnVVNFUic6ICdHcmFkdWF0ZVByb2plY3QnLFxuICAgICAgICAnUEFTU1dPUkQnOiAnR3JhZHVhdGVQcm9qZWN0JyxcbiAgICAgICAgJ0hPU1QnOiAnMTcyLjE2LjAuMTEwJyxcbiAgICAgICAgJ1BPUlQnOiAzMzA2LFxuICAgICAgICAnY2hhcnNldCc6ICd1dGY4bWI0JyxcbiAgICAgICAgJ1RFU1QnOiB7XG4gICAgICAgICAgICAnQ0hBUlNFVCc6ICd1dGY4bWI0JyxcbiAgICAgICAgICAgICdDT0xMQVRJT04nOiAndXRmOG1iNF9nZW5lcmFsX2NpJyxcbiAgICAgICAgfSxcbiAgICAgICAgJ0NPTk5fTUFYX0FHRSc6NjBcbiAgICB9LFxufVxuLi4uXG5cbiMgSW50ZXJuYXRpb25hbGl6YXRpb25cbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC90b3BpY3MvaTE4bi9cblxuTEFOR1VBR0VfQ09ERSA9ICd6aC1IYW5zJ1xuXG4jIFRJTUVfWk9ORSA9ICdVVEMnXG5USU1FX1pPTkUgPSAnQXNpYS9TaGFuZ2hhaSdcblxuVVNFX0kxOE4gPSBUcnVlXG5cblVTRV9MMTBOID0gVHJ1ZVxuXG4jIFVTRV9UWiA9IFRydWVcblVTRV9UWiA9IEZhbHNlXG5cbiMgU3RhdGljIGZpbGVzIChDU1MsIEphdmFTY3JpcHQsIEltYWdlcylcbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9ob3d0by9zdGF0aWMtZmlsZXMvXG5cblxuXG5TVEFUSUNfVVJMID0gJy9hc3NldHMvJ1xuU1RBVElDRklMRVNfRElSUyA9W1xub3MucGF0aC5qb2luKEJBU0VfRElSLCBcInRlbXBsYXRlcy9mcm9udC9hc3NldHNcIiksXG5dXG5cbiMgbWVkaWFcbk1FRElBX1VSTCA9IFwiL21lZGlhL1wiICAjIOiHquWumuS5iVxuTUVESUFfUk9PVCA9IG9zLnBhdGguam9pbihCQVNFX0RJUiwgJ21lZGlhJykgICMg6Ieq5a6a5LmJXG5pZiBvcy5wYXRoLmlzZGlyKE1FRElBX1JPT1QpID09IEZhbHNlOlxuICAgIG9zLm1rZGlyKE1FRElBX1JPT1QpXG5cblxuLi4uXG5QYXRoOiBtYWluL21vZGVsLnB5XG4jIGNvZGluZzp1dGYtOFxuXG5pbXBvcnQgY29weSwgcmUsIHRpbWVcbmltcG9ydCBsb2dnaW5nIGFzIGxvZ1xuaW1wb3J0IGRhdGV0aW1lXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgbW9kZWxzXG5mcm9tIGRqYW5nby5mb3Jtcy5tb2RlbHMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmZyb20gZGphbmdvLmNvcmUucGFnaW5hdG9yIGltcG9ydCBQYWdpbmF0b3JcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUHJvdGVjdGVkRXJyb3JcbmZyb20gdGhyZWFkbG9jYWxzLnRocmVhZGxvY2FscyBpbXBvcnQgZ2V0X2N1cnJlbnRfcmVxdWVzdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBTdW0sIE1heCwgTWluLCBBdmcsQ291bnRcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuXG4jIG1vZGVs5Z+656GA57G7XG5jbGFzcyBCYXNlTW9kZWwobW9kZWxzLk1vZGVsKTpcbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBhYnN0cmFjdCA9IFRydWVcblxuXG4gICAgZGVmIF9fUmV0cmlldmUoc2VsZiwgbW9kZWwpOlxuXG4gICAgICAgIGRhdGFzID0gbW9kZWwub2JqZWN0cy5hbGwoKVxuXG4gICAgICAgIHJldHVybiBzZWxmLnRvX2xpc3QoZGF0YXMsIGRhdGFzLG1vZGVsKVxuXG4gICAgZGVmIHJldHJpZXZlKHNlbGYsIG1vZGVsKTpcbiAgICAgICAgZGF0YXM9c2VsZi5fX1JldHJpZXZlKG1vZGVsLCBtb2RlbClcbiAgICAgICAgZm9yIGkgaW4gZGF0YXM6XG4gICAgICAgICAgICBhZGR0aW1lPWkuZ2V0KFwiYWRkdGltZVwiKVxuICAgICAgICAgICAgaWYgYWRkdGltZTpcbiAgICAgICAgICAgICAgICBhZGR0aW1lPXN0cihhZGR0aW1lKVs6MTldLnJlcGxhY2UoXCJUXCIsXCIgXCIpXG4gICAgICAgICAgICAgICAgaVtcImFkZHRpbWVcIl09YWRkdGltZVxuICAgICAgICByZXR1cm4gZGF0YXNcbi4uLlxuXG4gICAgICAgIGZpbGVkX2xpc3Q9W11cbiAgICAgICAgZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuICAgICAgICBtb2RlbG9iaiA9IGFwcHMuZ2V0X21vZGVsKCdtYWluJywgbW9kZWwuX190YWJsZW5hbWVfXylcbiAgICAgICAgZm9yIGZpZWxkIGluIG1vZGVsb2JqLl9tZXRhLmZpZWxkczpcbiAgICAgICAgICAgIGlmICAnRGF0ZVRpbWVGaWVsZCcgaW4gdHlwZShmaWVsZCkuX19uYW1lX18gOlxuICAgICAgICAgICAgICAgIGZpbGVkX2xpc3QuYXBwZW5kKGZpZWxkLm5hbWUpXG5cbiAgICAgICAgZm9yIGluZGV4LGkgaW4gZW51bWVyYXRlKG5ld0RhdGEpOlxuICAgICAgICAgICAgZm9yIGssdiBpbiBpLml0ZW1zKCk6XG4gICAgICAgICAgICAgICAgaWYgIGsgaW4gZmlsZWRfbGlzdCA6XG4gICAgICAgICAgICAgICAgICAgIG5ld0RhdGFbaW5kZXhdW2tdPXN0cih2KVs6MTldXG5cbiAgICAgICAgcmV0dXJuIG5ld0RhdGFhLCBwYWdlLCBwYWdlcywgdG90YWwsIGxpbWl0XG5cbiAgICBkZWYgcGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0PXt9LCBxPVEoKSk6XG4gICAgICAgIHJldHVybiBzZWxmLl9fUGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0LCBxKVxuXG4gICAgZGVmIF9fR2V0QnlDb2x1bW4oc2VsZiwgbW9kZWwsIGNvbHVtbk5hbWUsIG5ld19wYXJhbXMpOlxuICAgICAgICAjIGRhdGExPSBtb2RlbC5xdWVyeS5vcHRpb25zKGxvYWRfb25seShjb2x1bW4pKS5hbGwoKVxuICAgICAgICBkYXRhcyA9IG1vZGVsLm9iamVjdHMudmFsdWVzKGNvbHVtbk5hbWUpLmZpbHRlcigqKm5ld19wYXJhbXMpLmFsbCgpXG4gICAgICAgIGRhdGFfc2V0ID0gc2V0KClcbiAgICAgICAgZm9yIGkgaW4gZGF0YXM6XG4gICAgICAgICAgICBkYXRhX3NldC5hZGQoaS5nZXQoY29sdW1uTmFtZSkpXG4gICAgICAgIHJldHVybiBsaXN0KGRhdGFfc2V0KVxuLi4uXG5cbiAgICAgICAgY29sdW1uX2xpc3QgPSBbXVxuICAgICAgICBmb3IgY29sIGluIG1vZGVsLl9tZXRhLmZpZWxkczpcbiAgICAgICAgICAgIGNvbHVtbl9saXN0LmFwcGVuZChjb2wubmFtZSlcbiAgICAgICAgcGFyYW1zcz17fVxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcbiAgICAgICAgICAgIGlmIGsgaW4gY29sdW1uX2xpc3Q6XG4gICAgICAgICAgICAgICAgcGFyYW1zc1trXSA9IHZcbiAgICAgICAgcGFyYW1zc1tcImFkZHRpbWVcIl0gPSBkYXRldGltZS5kYXRldGltZS5ub3coKVxuICAgICAgICBtID0gbW9kZWwoKipwYXJhbXNzKVxuICAgICAgICB0cnk6XG4gICAgICAgICAgICByZXQgPSBtLnNhdmUoKVxuICAgICAgICAgICAgcmV0dXJuIG0uaWRcbiAgICAgICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOlxuICAgICAgICAgICAgcmV0dXJuIFwie306e31cIi5mb3JtYXQoRXhjZXB0aW9uLCBlKVxuXG4gICAgZGVmIGNyZWF0ZWJ5cmVxKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuICAgICAgICAnJydcbiAgICAgICAg5qC55o2u6K+35rGC5Y+C5pWw5Yib5bu65a+55bqU5qih5Z6L6K6w5b2VXG4gICAgICAgIDpwYXJhbSBtb2RlbDpcbiAgICAgICAgOnBhcmFtIHBhcmFtczpcbiAgICAgICAgOnJldHVybjpcbiAgICAgICAgJycnXG4gICAgICAgIHJldHVybiBzZWxmLl9fQ3JlYXRlQnlSZXEobW9kZWwsIG1vZGVsLCBwYXJhbXMpXG5cbiAgICBkZWYgX19HZXRCeUlkKHNlbGYsIG1vZGVsLCBpZCk6XG4gICAgICAgICcnJ1xuICAgICAgICDmoLnmja5pZOiOt+WPluaVsOaNruWFrOWFseaWueazlVxuICAgICAgICA6cGFyYW0gaWQ6XG4gICAgICAgIDpyZXR1cm46XG4gICAgICAgICcnJ1xuICAgICAgICBkYXRhID0gbW9kZWwub2JqZWN0cy5maWx0ZXIoaWQ9aWQpLmFsbCgpXG5cbiAgICAgICAgcmV0dXJuIHNlbGYudG9fbGlzdChtb2RlbCwgZGF0YSxtb2RlbClcblxuICAgIGRlZiBnZXRieWlkKHNlbGYsIG1vZGVsLCBpZCk6XG4gICAgICAgICcnJ1xuICAgICAgICDmoLnmja5pZOiOt+WPluaVsOaNrlxuICAgICAgICA6cGFyYW0gbW9kZWw6XG4gICAgICAgIDpwYXJhbSBpZDpcbiAgICAgICAgOnJldHVybjpcbiAgICAgICAgJycnXG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlJZChtb2RlbCwgbW9kZWwsIGlkKVxuXG4gICAgZGVmIF9fR2V0QnlQYXJhbXMoc2VsZiwgbW9kZWwsIHBhcmFtcyk6XG5cbiAgICAgICAgdHJ5OlxuICAgICAgICAgICAgX19sb2dpblVzZXJfXyA9IG1vZGVsLl9fbG9naW5Vc2VyX19cbiAgICAgICAgZXhjZXB0OlxuICAgICAgICAgICAgX19sb2dpblVzZXJfXyA9IE5vbmVcblxuICAgICAgICBpZiBfX2xvZ2luVXNlcl9fICE9IE5vbmUgYW5kIF9fbG9naW5Vc2VyX18hPSd1c2VybmFtZSc6XG4gICAgICAgICAgICBpZiBwYXJhbXMuZ2V0KCd1c2VybmFtZScpOlxuICAgICAgICAgICAgICAgIHBhcmFtc1ttb2RlbC5fX2xvZ2luVXNlcl9fXSA9IGNvcHkuZGVlcGNvcHkocGFyYW1zLmdldCgndXNlcm5hbWUnKSlcbiAgICAgICAgICAgICAgICBkZWwgcGFyYW1zWyd1c2VybmFtZSddXG4gICAgICAgIGlmIG1vZGVsLl9fdGFibGVuYW1lX18gIT0gJ3VzZXJzJzpcbiAgICAgICAgICAgIGlmIHBhcmFtcy5nZXQoJ3Bhc3N3b3JkJyk6XG4gICAgICAgICAgICAgICAgcGFyYW1zWydtaW1hJ10gPSBjb3B5LmRlZXBjb3B5KHBhcmFtcy5nZXQoJ3Bhc3N3b3JkJykpXG4gICAgICAgICAgICAgICAgIyBkZWwgcGFyYW1zWydwYXNzd29yZCddXG5cbiAgICAgICAgIyDliY3nq6/kvKDkuobml6DnlKjlj4LmlbDlkozkvKDplJnlj4LmlbDlkI3vvIzlnKjov5nph4zkv67mlLlcbiAgICAgICAgcGFyYW1zcyA9IHt9XG4gICAgICAgIGNvbHVtbkxpc3QgPSBzZWxmLmdldGFsbGNvbHVtbihtb2RlbCwgbW9kZWwpXG4gICAgICAgIGZvciBrLCB2IGluIHBhcmFtcy5pdGVtcygpOlxuICAgICAgICAgICAgaWYgayBpbiBjb2x1bW5MaXN0OlxuICAgICAgICAgICAgICAgIHBhcmFtc3Nba10gPSB2XG5cbiAgICAgICAgZGF0YXNfID0gbW9kZWwub2JqZWN0cy5maWx0ZXIoKipwYXJhbXNzKS5hbGwoKVxuXG4gICAgICAgIHJldHVybiBzZWxmLnRvX2xpc3QoZGF0YXNfLCBkYXRhc18sbW9kZWwpXG5cbiAgICBkZWYgZ2V0YnlwYXJhbXMoc2VsZiwgbW9kZWwsIHBhcmFtcyk6XG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlQYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMpXG4uLi5cblBhdGg6IG1haW4vdXNlcnNfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIHVzZXJzKEJhc2VNb2RlbCk6XG4gICAgIyBpZD1tb2RlbHMuQmlnSW50ZWdlckZpZWxkKHZlcmJvc2VfbmFtZT1cIuiHquWinmlkXCIpXG4gICAgdXNlcm5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfotKblj7cnKVxuICAgIHBhc3N3b3JkID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn5a+G56CBJylcbiAgICByb2xlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6KeS6ImyJylcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIF9faXNBZG1pbl9fID0gJ+aYrydcbiAgICBpbWFnZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J+WktOWDjycpXG5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ3VzZXJzJ1xuXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAndXNlcnMnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+euoeeQhuWRmOihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYudXNlcm5hbWVcbi4uLlxuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxQ3k0MXlwUFFZYTRMS3hqRW10OG14MSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjoyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyMzo0Ny41NDRaIiwicmVzcG9uc2VfdGV4dCI6IkxldCBtZSBleGFtaW5lIHNvbWUgb2YgdGhlIHZpZXcgZmlsZXMgdG8gdW5kZXJzdGFuZCBob3cgdGhleSdyZSBzdHJ1Y3R1cmVkOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjRhYWUzMzdkLTZmZjctNDM0YS04MmU1LTRiN2E3NWI2ODUwOCIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFhd1V0TFZSeEZDcXFxRFJjbkZvYWEiLCJ0b29sX25hbWUiOiJjb2RlYmFzZS1yZXRyaWV2YWwiLCJpbnB1dF9qc29uIjoie1wiaW5mb3JtYXRpb25fcmVxdWVzdFwiOiBcIlNob3cgbWUgdGhlIHN0cnVjdHVyZSBvZiB0aGUgdXRpbGl0eSBmaWxlcyBsaWtlIGF1dGgucHksIGNvbW1vbi5weSwgYW5kIGNvZGVzLnB5IHRvIHVuZGVyc3RhbmQgdGhlIGNvbW1vbiBmdW5jdGlvbmFsaXR5LlwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IkxldCBtZSBhbHNvIGNoZWNrIHRoZSB1dGlsaXR5IGZpbGVzIHRvIHVuZGVyc3RhbmQgdGhlIGNvbW1vbiBmdW5jdGlvbmFsaXR5OlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogbWFpbi91c2Vyc192LnB5XG4jIGNvZGluZzp1dGYtOFxuXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcblxuZnJvbSAudXNlcnNfbW9kZWwgaW1wb3J0IHVzZXJzXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGhvc3QscG9ydCx1c2VyLHBhc3N3ZCxkYk5hbWVcblxuZGVmIHVzZXJzX2xvZ2luKHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHsnY29kZSc6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGV9XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIHJlcV9kaWN0LmdldCgncm9sZScpIT1Ob25lOlxuICAgICAgICAgICAgZGVsIHJlcV9kaWN0Wydyb2xlJ11cbiAgICAgICAgXG4gICAgICAgIGRhdGFzID0gdXNlcnMuZ2V0YnlwYXJhbXModXNlcnMsIHVzZXJzLCByZXFfZGljdClcbiAgICAgICAgaWYgbm90IGRhdGFzOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBwYXNzd29yZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gbWVzLnBhc3N3b3JkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG4gICAgICAgIHJlcV9kaWN0WydpZCddID0gZGF0YXNbMF0uZ2V0KCdpZCcpXG4gICAgICAgIHJldHVybiBBdXRoLmF1dGhlbnRpY2F0ZShBdXRoLCB1c2VycywgcmVxX2RpY3QpXG5cblxuZGVmIHVzZXJzX3JlZ2lzdGVyKHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHsnY29kZSc6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGV9XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG5cbiAgICAgICAgaWRPckVyciA9IHVzZXJzLmNyZWF0ZWJ5cmVxKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXG4gICAgICAgIGlmIGlkT3JFcnIgaXMgRXhjZXB0aW9uOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBpZE9yRXJyXG4gICAgICAgIGVsc2U6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGlkT3JFcnJcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3Nlc3Npb24ocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSxcIm1zZ1wiOm1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIHJlcV9kaWN0ID0ge1wiaWRcIjogcmVxdWVzdC5zZXNzaW9uLmdldCgncGFyYW1zJykuZ2V0KFwiaWRcIil9XG4gICAgICAgIG1zZ1snZGF0YSddID0gdXNlcnMuZ2V0YnlwYXJhbXModXNlcnMsIHVzZXJzLCByZXFfZGljdClbMF1cblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfbG9nb3V0KHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcbiAgICAgICAgICAgIFwibXNnXCI6IFwi6YCA5Ye65oiQ5YqfXCIsXG4gICAgICAgICAgICBcImNvZGVcIjogMFxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3BhZ2UocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLFxuICAgICAgICAgICAgICAgXCJkYXRhXCI6IHtcImN1cnJQYWdlXCI6IDEsIFwidG90YWxQYWdlXCI6IDEsIFwidG90YWxcIjogMSwgXCJwYWdlU2l6ZVwiOiAxMCwgXCJsaXN0XCI6IFtdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgdGFibGVuYW1lID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInRhYmxlbmFtZVwiKVxuICAgICAgICB0cnk6XG4gICAgICAgICAgICBfX2hhc01lc3NhZ2VfXyA9IHVzZXJzLl9faGFzTWVzc2FnZV9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9faGFzTWVzc2FnZV9fID0gTm9uZVxuICAgICAgICBpZiBfX2hhc01lc3NhZ2VfXyBhbmQgX19oYXNNZXNzYWdlX18gIT0gXCLlkKZcIjpcblxuICAgICAgICAgICAgaWYgdGFibGVuYW1lICE9IFwidXNlcnNcIjpcbiAgICAgICAgICAgICAgICByZXFfZGljdFtcInVzZXJpZFwiXSA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJwYXJhbXNcIikuZ2V0KFwiaWRcIilcbiAgICAgICAgaWYgdGFibGVuYW1lID09IFwidXNlcnNcIjpcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydsaXN0J10sIG1zZ1snZGF0YSddWydjdXJyUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWxQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbCddLCBcXFxuICAgICAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSB1c2Vycy5wYWdlKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXG4gICAgICAgIGVsc2U6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXVsnbGlzdCddLCBtc2dbJ2RhdGEnXVsnY3VyclBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWwnXSwgXFxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydwYWdlU2l6ZSddID0gW10sMSwwLDAsMTBcblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfaW5mbyhyZXF1ZXN0LCBpZF8pOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIGRhdGEgPSB1c2Vycy5nZXRieWlkKHVzZXJzLCB1c2VycywgaW50KGlkXykpXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGRhdGFbMF1cbiAgICAgICAgIyDmtY/op4jngrnlh7vmrKHmlbBcbiAgICAgICAgdHJ5OlxuICAgICAgICAgICAgX19icm93c2VDbGlja19fID0gdXNlcnMuX19icm93c2VDbGlja19fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fYnJvd3NlQ2xpY2tfXyA9IE5vbmVcblxuICAgICAgICBpZiBfX2Jyb3dzZUNsaWNrX18gYW5kIFwiY2xpY2tudW1cIiBpbiB1c2Vycy5nZXRhbGxjb2x1bW4odXNlcnMsIHVzZXJzKTpcbiAgICAgICAgICAgIGNsaWNrX2RpY3QgPSB7XCJpZFwiOiBpbnQoaWRfKSwgXCJjbGlja251bVwiOiBzdHIoaW50KGRhdGFbMF0uZ2V0KFwiY2xpY2tudW1cIiwgMCkpICsgMSl9XG4gICAgICAgICAgICByZXQgPSB1c2Vycy51cGRhdGVieXBhcmFtcyh1c2VycywgdXNlcnMsIGNsaWNrX2RpY3QpXG4gICAgICAgICAgICBpZiByZXQgIT0gTm9uZTpcbiAgICAgICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxuICAgICAgICAgICAgICAgIG1zZ1snbXNnJ10gPSByZXRcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3NhdmUocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgaWYgdXNlcnMub2JqZWN0cy5maWx0ZXIodXNlcm5hbWUgPSByZXFfZGljdFsndXNlcm5hbWUnXSkuY291bnQoKT4wOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBcIui0puaIt+W3suWtmOWcqFwiXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZywgZW5jb2Rlcj1DdXN0b21Kc29uRW5jb2RlcilcbiAgICAgICAgcmVxX2RpY3RbJ3JvbGUnXSA9ICfnrqHnkIblkZgnXG4gICAgICAgIGlkT3JFcnIgPSB1c2Vycy5jcmVhdGVieXJlcSh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVxuICAgICAgICBpZiBpZE9yRXJyIGlzIEV4Y2VwdGlvbjpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gaWRPckVyclxuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgbXNnWydkYXRhJ10gPSBpZE9yRXJyXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiB1c2Vyc191cGRhdGUocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIHJlcV9kaWN0LmdldChcIm1pbWFcIikgYW5kIHJlcV9kaWN0LmdldChcInBhc3N3b3JkXCIpOlxuICAgICAgICAgICAgaWYgXCJtaW1hXCIgbm90IGluIHVzZXJzLmdldGFsbGNvbHVtbih1c2Vycyx1c2Vycyk6XG4gICAgICAgICAgICAgICAgZGVsIHJlcV9kaWN0W1wibWltYVwiXVxuICAgICAgICAgICAgaWYgXCJwYXNzd29yZFwiIG5vdCBpbiB1c2Vycy5nZXRhbGxjb2x1bW4odXNlcnMsdXNlcnMpOlxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcInBhc3N3b3JkXCJdXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIGRlbCByZXFfZGljdFtcImNsaWNrbnVtXCJdXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIHBhc3NcbiAgICAgICAgZXJyb3IgPSB1c2Vycy51cGRhdGVieXBhcmFtcyh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVxuICAgICAgICBpZiBlcnJvciAhPSBOb25lOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBlcnJvclxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfZGVsZXRlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuXG4gICAgICAgIGVycm9yID0gdXNlcnMuZGVsZXRlcyh1c2VycyxcbiAgICAgICAgICAgIHVzZXJzLFxuICAgICAgICAgICAgcmVxX2RpY3QuZ2V0KFwiaWRzXCIpXG4gICAgICAgIClcbiAgICAgICAgaWYgZXJyb3IgIT0gTm9uZTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gZXJyb3JcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG4uLi5cblBhdGg6IG1haW4vdXJscy5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IG9zXG5mcm9tIGRqYW5nby51cmxzIGltcG9ydCBwYXRoXG5mcm9tIG1haW4gaW1wb3J0IHVzZXJzX3YsIGNvbmZpZ192LCBzY2hlbWFfdlxuIyBmcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgZGJOYW1lIGFzIHNjaGVtYU5hbWVcbiMgdXJs6KeE5YiZ5YiX6KGoXG51cmxwYXR0ZXJucyA9IFtcbiAgICBwYXRoKHIndXNlcnMvcmVnaXN0ZXInLCB1c2Vyc192LnVzZXJzX3JlZ2lzdGVyKSxcbiAgICBwYXRoKHIndXNlcnMvbG9naW4nLCB1c2Vyc192LnVzZXJzX2xvZ2luKSxcbiAgICBwYXRoKHIndXNlcnMvbG9nb3V0JywgdXNlcnNfdi51c2Vyc19sb2dvdXQpLFxuICAgIHBhdGgocid1c2Vycy9zZXNzaW9uJywgdXNlcnNfdi51c2Vyc19zZXNzaW9uKSxcbiAgICBwYXRoKHIndXNlcnMvcGFnZScsIHVzZXJzX3YudXNlcnNfcGFnZSksXG4gICAgcGF0aChyJ3VzZXJzL3NhdmUnLCB1c2Vyc192LnVzZXJzX3NhdmUpLFxuICAgIHBhdGgocid1c2Vycy9pbmZvLzxpZF8+JywgdXNlcnNfdi51c2Vyc19pbmZvKSxcbiAgICBwYXRoKHIndXNlcnMvdXBkYXRlJywgdXNlcnNfdi51c2Vyc191cGRhdGUpLFxuICAgIHBhdGgocid1c2Vycy9kZWxldGUnLCB1c2Vyc192LnVzZXJzX2RlbGV0ZSksXG5cbiAgICBwYXRoKHInY29uZmlnL3BhZ2UnLCBjb25maWdfdi5jb25maWdfcGFnZSksXG4gICAgcGF0aChyJ2NvbmZpZy9saXN0JywgY29uZmlnX3YuY29uZmlnX2xpc3QpLFxuICAgIHBhdGgocidjb25maWcvc2F2ZScsIGNvbmZpZ192LmNvbmZpZ19zYXZlKSxcbiAgICBwYXRoKHInY29uZmlnL2FkZCcsIGNvbmZpZ192LmNvbmZpZ19hZGQpLFxuICAgIHBhdGgocidjb25maWcvaW5mby88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19pbmZvKSxcbiAgICBwYXRoKHInY29uZmlnL2RldGFpbC88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19kZXRhaWwpLFxuICAgIHBhdGgocidjb25maWcvdXBkYXRlJywgY29uZmlnX3YuY29uZmlnX3VwZGF0ZSksXG4gICAgcGF0aChyJ2NvbmZpZy9kZWxldGUnLCBjb25maWdfdi5jb25maWdfZGVsZXRlKSxcblxuXVxuIyBtYWluIGFwcOeahOi3r+W+hFxubWFpbkRpciA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJtYWluXCIpXG5cbiMg6L+H5ruk5paH5Lu255qE5YiX6KGoXG5leGNsdWRlTGlzdCA9IFtcbiAgICBcInNjaGVtYV92LnB5XCIsXG5cbiAgICBcInVzZXJzX3YucHlcIixcbiAgICBcImNvbmZpZ192LnB5XCIsXG5dXG5cbiMg5b6q546v5b2T5YmN55uu5b2V5LiL55qEcHnmlofku7Zcblxudmlld190dXBsZSA9IHNldCgpXG5mb3IgaSBpbiBvcy5saXN0ZGlyKG1haW5EaXIpOlxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpWy01Ol0gPT0gXCJfdi5weVwiOlxuICAgICAgICB2aWV3TmFtZSA9IGlbOi0zXSAgIyDljrvmjokucHnlkI7nvIDlrZfnrKbkuLJcbiAgICAgICAgdmlld190dXBsZS5hZGQoXCJmcm9tIG1haW4gaW1wb3J0IHt9XCIuZm9ybWF0KHZpZXdOYW1lKSlcbi4uLlxuUGF0aDogbWFpbi9jb25maWdfdi5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IGxvZ2dpbmdcblxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5cbmZyb20gLmNvbmZpZ19tb2RlbCBpbXBvcnQgY29uZmlnXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbCBpbXBvcnQgbWVzc2FnZSBhcyBtZXNcblxuXG5kZWYgY29uZmlnX3BhZ2UocmVxdWVzdCk6XG4gICAgJycnXG4gICAg6I635Y+W5Y+C5pWw5L+h5oGvXG4gICAgOnJldHVybjpcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsXG4gICAgICAgICAgICAgICBcImRhdGFcIjoge1wiY3VyclBhZ2VcIjogMSwgXCJ0b3RhbFBhZ2VcIjogMSwgXCJ0b3RhbFwiOiAxLCBcInBhZ2VTaXplXCI6IDEwLCBcImxpc3RcIjogW119fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoJ3JlcV9kaWN0JylcbiAgICAgICAgbXNnWydkYXRhJ11bJ2xpc3QnXSwgbXNnWydkYXRhJ11bJ2N1cnJQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbFBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsJ10sIFxcXG4gICAgICAgIG1zZ1snZGF0YSddWydwYWdlU2l6ZSddID0gY29uZmlnLnBhZ2UoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgY29uZmlnX2xpc3QocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLFxuICAgICAgICAgICAgICAgXCJkYXRhXCI6IHtcImN1cnJQYWdlXCI6IDEsIFwidG90YWxQYWdlXCI6IDEsIFwidG90YWxcIjogMSwgXCJwYWdlU2l6ZVwiOiAxMCwgXCJsaXN0XCI6IFtdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcblxuICAgICAgICBtc2dbJ2RhdGEnXVsnbGlzdCddLCBtc2dbJ2RhdGEnXVsnY3VyclBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWwnXSwgXFxcbiAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSBjb25maWcucGFnZShjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ19pbmZvKHJlcXVlc3QsIGlkXyk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgZGF0YSA9IGNvbmZpZy5nZXRieWlkKGNvbmZpZywgY29uZmlnLCBpbnQoaWRfKSlcbiAgICAgICAgaWYgbGVuKGRhdGEpID4gMDpcbiAgICAgICAgICAgIG1zZ1snZGF0YSddID0gZGF0YVswXVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgY29uZmlnX2RldGFpbChyZXF1ZXN0LCBpZF8pOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIGRhdGEgPSBjb25maWcuZ2V0YnlpZChjb25maWcsIGNvbmZpZywgaW50KGlkXykpXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGRhdGFbMF1cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ19zYXZlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWIm+W7uuWPguaVsOS/oeaBr1xuICAgIDpyZXR1cm46XG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXG4gICAgICAgIHBhcmFtMSA9IGNvbmZpZy5nZXRieXBhcmFtcyhjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG4gICAgICAgIGlmIHBhcmFtMTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gaWRfZXhpc3RfY29kZVxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IG1lcy5pZF9leGlzdF9jb2RlXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuICAgICAgICBlcnJvciA9IGNvbmZpZy5jcmVhdGVieXJlcShjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG4gICAgICAgIGxvZ2dpbmcud2FybmluZyhcInNhdmVfY29uZmlnLnJlcz09PT09PT09PT57fVwiLmZvcm1hdChlcnJvcikpXG4gICAgICAgIGlmIGVycm9yICE9IE5vbmU6XG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IGVycm9yXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiBjb25maWdfYWRkKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuXG4gICAgICAgIGVycm9yID0gY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcbiAgICAgICAgaWYgZXJyb3IgIT0gTm9uZTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gZXJyb3JcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ191cGRhdGUocmVxdWVzdCk6XG4gICAgJycnXG4gICAg5pu05paw5Y+C5pWw5L+h5oGvXG4gICAgOnJldHVybjpcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsIFwiZGF0YVwiOiB7fX1cblxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoJ3JlcV9kaWN0JylcblxuICAgICAgICBjb25maWcudXBkYXRlYnlwYXJhbXMoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxuXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiBjb25maWdfZGVsZXRlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWIoOmZpOWPguaVsOS/oeaBr1xuICAgIDpyZXR1cm46XG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXG4gICAgICAgIGNvbmZpZy5kZWxldGVzKGNvbmZpZyxcbiAgICAgICAgICAgIGNvbmZpZyxcbiAgICAgICAgICAgIHJlcV9kaWN0LmdldChcImlkc1wiKVxuICAgICAgICApXG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG4uLi5cblBhdGg6IG1haW4vdXNlcnNfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIHVzZXJzKEJhc2VNb2RlbCk6XG4gICAgIyBpZD1tb2RlbHMuQmlnSW50ZWdlckZpZWxkKHZlcmJvc2VfbmFtZT1cIuiHquWinmlkXCIpXG4gICAgdXNlcm5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfotKblj7cnKVxuICAgIHBhc3N3b3JkID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn5a+G56CBJylcbiAgICByb2xlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6KeS6ImyJylcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIF9faXNBZG1pbl9fID0gJ+aYrydcbiAgICBpbWFnZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J+WktOWDjycpXG5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ3VzZXJzJ1xuXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAndXNlcnMnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+euoeeQhuWRmOihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYudXNlcm5hbWVcbi4uLlxuUGF0aDogbWFpbi9tb2RlbHMucHlcbi4uLlxuICAgIF9fZm9yZUVuZExpc3RfXz0n5ZCmJyPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgX19pc0FkbWluX189J+WQpicj6KGo5bGe5oCnaXNBZG1pbj3igJ3mmK/igJ0s5Yi35Ye65p2l55qE55So5oi36KGo5Lmf5piv566h55CG5ZGY77yM5Y2zcGFnZeWSjGxpc3Tlj6/ku6Xmn6XnnIvmiYDmnInkurrnmoTogIPor5XorrDlvZUo5ZCM5pe25bqU55So5LqO5YW25LuW6KGoKVxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J+WIm+W7uuaXtumXtCcpXG4gICAgeW9uZ2h1bWluZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSx1bmlxdWU9VHJ1ZSwgdmVyYm9zZV9uYW1lPSfnlKjmiLflkI0nIClcbiAgICBtaW1hPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5a+G56CBJyApXG4gICAgeGluZ21pbmc9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflp5PlkI0nIClcbiAgICB4aW5nYmllPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSwgbnVsbD1UcnVlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5oCn5YirJyApXG4gICAgdG91eGlhbmc9bW9kZWxzLlRleHRGaWVsZCAgICggIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WktOWDjycgKVxuICAgIG5pYW5saW5nPW1vZGVscy5JbnRlZ2VyRmllbGQgICggIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+W5tOm+hCcgKVxuICAgIHlvdXhpYW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n6YKu566xJyApXG4gICAgc2hvdWppPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5omL5py6JyApXG4gICAgJycnXG4gICAgeW9uZ2h1bWluZz1WQVJDSEFSXG4gICAgbWltYT1WQVJDSEFSXG4gICAgeGluZ21pbmc9VkFSQ0hBUlxuICAgIHhpbmdiaWU9VkFSQ0hBUlxuICAgIHRvdXhpYW5nPVRleHRcbiAgICBuaWFubGluZz1JbnRlZ2VyXG4gICAgeW91eGlhbmc9VkFSQ0hBUlxuICAgIHNob3VqaT1WQVJDSEFSXG4gICAgJycnXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAneW9uZ2h1J1xuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ+eUqOaItydcbmNsYXNzIG5vbmdodShCYXNlTW9kZWwpOlxuICAgIF9fZG9jX18gPSB1Jycnbm9uZ2h1JycnXG4gICAgX190YWJsZW5hbWVfXyA9ICdub25naHUnXG5cbiAgICBfX2xvZ2luVXNlcl9fPSd6aGFuZ2hhbydcbi4uLlxuICAgIF9fZm9yZUVuZExpc3RfXz0n5ZCmJyPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgX19pc0FkbWluX189J+WQpicj6KGo5bGe5oCnaXNBZG1pbj3igJ3mmK/igJ0s5Yi35Ye65p2l55qE55So5oi36KGo5Lmf5piv566h55CG5ZGY77yM5Y2zcGFnZeWSjGxpc3Tlj6/ku6Xmn6XnnIvmiYDmnInkurrnmoTogIPor5XorrDlvZUo5ZCM5pe25bqU55So5LqO5YW25LuW6KGoKVxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J+WIm+W7uuaXtumXtCcpXG4gICAgemhhbmdoYW89bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0n6LSm5Y+3JyApXG4gICAgbWltYT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WvhueggScgKVxuICAgIG5vbmdodXhpbmdtaW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5Yac5oi35aeT5ZCNJyApXG4gICAgdG91eGlhbmc9bW9kZWxzLlRleHRGaWVsZCAgICggbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WktOWDjycgKVxuICAgIHhpbmdiaWU9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSfmgKfliKsnIClcbiAgICBuaWFubGluZz1tb2RlbHMuSW50ZWdlckZpZWxkICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflubTpvoQnIClcbiAgICB5b3V4aWFuZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+mCrueusScgKVxuICAgIGRpYW5odWE9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSfnlLXor50nIClcbiAgICAnJydcbiAgICB6aGFuZ2hhbz1WQVJDSEFSXG4gICAgbWltYT1WQVJDSEFSXG4gICAgbm9uZ2h1eGluZ21pbmc9VkFSQ0hBUlxuICAgIHRvdXhpYW5nPVRleHRcbiAgICB4aW5nYmllPVZBUkNIQVJcbiAgICBuaWFubGluZz1JbnRlZ2VyXG4gICAgeW91eGlhbmc9VkFSQ0hBUlxuICAgIGRpYW5odWE9VkFSQ0hBUlxuICAgICcnJ1xuICAgIGNsYXNzIE1ldGE6XG4gICAgICAgIGRiX3RhYmxlID0gJ25vbmdodSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICflhpzmiLcnXG5jbGFzcyBndW95dWFueGlueGkoQmFzZU1vZGVsKTpcbiAgICBfX2RvY19fID0gdScnJ2d1b3l1YW54aW54aScnJ1xuICAgIF9fdGFibGVuYW1lX18gPSAnZ3VveXVhbnhpbnhpJ1xuXG5cblxuICAgIF9fYXV0aFRhYmxlc19fPXsnemhhbmdoYW8nOidub25naHUnLH1cbiAgICBfX2F1dGhQZW9wbGVfXz0n5ZCmJyPnlKjmiLfooajvvIzooajlsZ7mgKdsb2dpblVzZXJDb2x1bW7lr7nlupTnmoTlgLzlsLHmmK/nlKjmiLflkI3lrZfmrrXvvIxtaW1h5bCx5piv5a+G56CB5a2X5q61XG4gICAgX19zZnNoX189J+WQpicj6KGoc2ZzaCjmmK/lkKblrqHmoLjvvIzigJ3mmK/igJ3miJbigJ3lkKbigJ0p5a2X5q615ZKMc2ZoZijlrqHmoLjlm57lpI0p5a2X5q6177yM5ZCO5Y+w5YiX6KGoKHBhZ2Up55qE5pON5L2c5Lit6KaB5aSa5LiA5Liq4oCd5a6h5qC44oCd5oyJ6ZKu77yM54K55Ye74oCd5a6h5qC44oCd5by55Ye65LiA5Liq6aG16Z2i77yM5YyF5ZCr4oCd5piv5ZCm5a6h5qC44oCd5ZKM4oCd5a6h5qC45Zue5aSN4oCd77yM54K55Ye756Gu5a6a6LCD55SodXBkYXRl5o6l5Y+j77yM5L+u5pS5c2ZzaOWSjHNmaGbkuKTkuKrlrZfmrrXjgIJcbiAgICBfX2F1dGhTZXBhcmF0ZV9fPSflkKYnI+WQjuWPsOWIl+ihqOadg+mZkFxuICAgIF9fdGh1bWJzVXBfXz0n5ZCmJyPooajlsZ7mgKd0aHVtYnNVcFvmmK8v5ZCmXe+8jOaWsOWinnRodW1ic3VwbnVt6LWe5ZKMY3JhemlseW51bei4qeWtl+autVxuICAgIF9faW50ZWxSZWNvbV9fPSflkKYnI+aZuuiDveaOqOiNkOWKn+iDvSjooajlsZ7mgKfvvJpbaW50ZWxSZWNvbe+8iOaYry/lkKbvvIldLOaWsOWinmNsaWNrdGltZVvliY3nq6/kuI3mmL7npLror6XlrZfmrrVd5a2X5q6177yI6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnmm7TmlrDvvInvvIzmjIljbGlja3RpbWXmjpLluo/mn6Xor6IpXG4gICAgX19icm93c2VDbGlja19fPSflkKYnI+ihqOWxnuaAp1ticm93c2VDbGljazrmmK8v5ZCmXe+8jOeCueWHu+Wtl+aute+8iGNsaWNrbnVt77yJ77yM6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnlkI7nq6/oh6rliqgrMe+8ieOAgeaKleelqOWKn+iDve+8iOihqOWxnuaAp1t2b3RlOuaYry/lkKZd77yM5oqV56Wo5a2X5q6177yIdm90ZW51be+8iSzosIPnlKh2b3Rl5o6l5Y+j5ZCO56uvdm90ZW51bSsxXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXz0n5ZCmJyPliY3lj7DliJfooajmnYPpmZBmb3JlRW5kTGlzdEF1dGhb5pivL+WQpl3vvJvlvZNmb3JlRW5kTGlzdEF1dGg95piv77yM5Yi355qE6KGo5paw5aKe55So5oi35a2X5q61dXNlcmlk77yM5YmN5Y+wbGlzdOWIl+ihqOaOpeWPo+S7heiDveafpeeci+iHquW3seeahOiusOW9leWSjGFkZOaOpeWPo+WQjuWPsOi1i+WAvHVzZXJpZOeahOWAvFxuLi4uXG5cblxuXG4gICAgX19hdXRoVGFibGVzX189eyd6aGFuZ2hhbyc6J25vbmdodScsfVxuICAgIF9fYXV0aFBlb3BsZV9fPSflkKYnI+eUqOaIt+ihqO+8jOihqOWxnuaAp2xvZ2luVXNlckNvbHVtbuWvueW6lOeahOWAvOWwseaYr+eUqOaIt+WQjeWtl+aute+8jG1pbWHlsLHmmK/lr4bnoIHlrZfmrrVcbiAgICBfX3Nmc2hfXz0n5ZCmJyPooahzZnNoKOaYr+WQpuWuoeaguO+8jOKAneaYr+KAneaIluKAneWQpuKAnSnlrZfmrrXlkoxzZmhmKOWuoeaguOWbnuWkjSnlrZfmrrXvvIzlkI7lj7DliJfooagocGFnZSnnmoTmk43kvZzkuK3opoHlpJrkuIDkuKrigJ3lrqHmoLjigJ3mjInpkq7vvIzngrnlh7vigJ3lrqHmoLjigJ3lvLnlh7rkuIDkuKrpobXpnaLvvIzljIXlkKvigJ3mmK/lkKblrqHmoLjigJ3lkozigJ3lrqHmoLjlm57lpI3igJ3vvIzngrnlh7vnoa7lrprosIPnlKh1cGRhdGXmjqXlj6PvvIzkv67mlLlzZnNo5ZKMc2ZoZuS4pOS4quWtl+auteOAglxuICAgIF9fYXV0aFNlcGFyYXRlX189J+WQpicj5ZCO5Y+w5YiX6KGo5p2D6ZmQXG4gICAgX190aHVtYnNVcF9fPSflkKYnI+ihqOWxnuaAp3RodW1ic1VwW+aYry/lkKZd77yM5paw5aKedGh1bWJzdXBudW3otZ7lkoxjcmF6aWx5bnVt6Lip5a2X5q61XG4gICAgX19pbnRlbFJlY29tX189J+WQpicj5pm66IO95o6o6I2Q5Yqf6IO9KOihqOWxnuaAp++8mltpbnRlbFJlY29t77yI5pivL+WQpu+8iV0s5paw5aKeY2xpY2t0aW1lW+WJjeerr+S4jeaYvuekuuivpeWtl+autV3lrZfmrrXvvIjosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeabtOaWsO+8ie+8jOaMiWNsaWNrdGltZeaOkuW6j+afpeivoilcbiAgICBfX2Jyb3dzZUNsaWNrX189J+WQpicj6KGo5bGe5oCnW2Jyb3dzZUNsaWNrOuaYry/lkKZd77yM54K55Ye75a2X5q6177yIY2xpY2tudW3vvInvvIzosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeWQjuerr+iHquWKqCsx77yJ44CB5oqV56Wo5Yqf6IO977yI6KGo5bGe5oCnW3ZvdGU65pivL+WQpl3vvIzmipXnpajlrZfmrrXvvIh2b3RlbnVt77yJLOiwg+eUqHZvdGXmjqXlj6PlkI7nq692b3RlbnVtKzFcbiAgICBfX2ZvcmVFbmRMaXN0QXV0aF9fPSflkKYnI+WJjeWPsOWIl+ihqOadg+mZkGZvcmVFbmRMaXN0QXV0aFvmmK8v5ZCmXe+8m+W9k2ZvcmVFbmRMaXN0QXV0aD3mmK/vvIzliLfnmoTooajmlrDlop7nlKjmiLflrZfmrrV1c2VyaWTvvIzliY3lj7BsaXN05YiX6KGo5o6l5Y+j5LuF6IO95p+l55yL6Ieq5bex55qE6K6w5b2V5ZKMYWRk5o6l5Y+j5ZCO5Y+w6LWL5YC8dXNlcmlk55qE5YC8XG4uLi5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ2d1b3BpbnhpbnhpJ1xuXG5cblxuICAgIF9fYXV0aFRhYmxlc19fPXsnemhhbmdoYW8nOidub25naHUnLH1cbiAgICBfX2F1dGhQZW9wbGVfXz0n5ZCmJyPnlKjmiLfooajvvIzooajlsZ7mgKdsb2dpblVzZXJDb2x1bW7lr7nlupTnmoTlgLzlsLHmmK/nlKjmiLflkI3lrZfmrrXvvIxtaW1h5bCx5piv5a+G56CB5a2X5q61XG4gICAgX19zZnNoX189J+WQpicj6KGoc2ZzaCjmmK/lkKblrqHmoLjvvIzigJ3mmK/igJ3miJbigJ3lkKbigJ0p5a2X5q615ZKMc2ZoZijlrqHmoLjlm57lpI0p5a2X5q6177yM5ZCO5Y+w5YiX6KGoKHBhZ2Up55qE5pON5L2c5Lit6KaB5aSa5LiA5Liq4oCd5a6h5qC44oCd5oyJ6ZKu77yM54K55Ye74oCd5a6h5qC44oCd5by55Ye65LiA5Liq6aG16Z2i77yM5YyF5ZCr4oCd5piv5ZCm5a6h5qC44oCd5ZKM4oCd5a6h5qC45Zue5aSN4oCd77yM54K55Ye756Gu5a6a6LCD55SodXBkYXRl5o6l5Y+j77yM5L+u5pS5c2ZzaOWSjHNmaGbkuKTkuKrlrZfmrrXjgIJcbiAgICBfX2F1dGhTZXBhcmF0ZV9fPSflkKYnI+WQjuWPsOWIl+ihqOadg+mZkFxuICAgIF9fdGh1bWJzVXBfXz0n5ZCmJyPooajlsZ7mgKd0aHVtYnNVcFvmmK8v5ZCmXe+8jOaWsOWinnRodW1ic3VwbnVt6LWe5ZKMY3JhemlseW51bei4qeWtl+autVxuICAgIF9faW50ZWxSZWNvbV9fPSflkKYnI+aZuuiDveaOqOiNkOWKn+iDvSjooajlsZ7mgKfvvJpbaW50ZWxSZWNvbe+8iOaYry/lkKbvvIldLOaWsOWinmNsaWNrdGltZVvliY3nq6/kuI3mmL7npLror6XlrZfmrrVd5a2X5q6177yI6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnmm7TmlrDvvInvvIzmjIljbGlja3RpbWXmjpLluo/mn6Xor6IpXG4gICAgX19icm93c2VDbGlja19fPSflkKYnI+ihqOWxnuaAp1ticm93c2VDbGljazrmmK8v5ZCmXe+8jOeCueWHu+Wtl+aute+8iGNsaWNrbnVt77yJ77yM6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnlkI7nq6/oh6rliqgrMe+8ieOAgeaKleelqOWKn+iDve+8iOihqOWxnuaAp1t2b3RlOuaYry/lkKZd77yM5oqV56Wo5a2X5q6177yIdm90ZW51be+8iSzosIPnlKh2b3Rl5o6l5Y+j5ZCO56uvdm90ZW51bSsxXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXz0n5ZCmJyPliY3lj7DliJfooajmnYPpmZBmb3JlRW5kTGlzdEF1dGhb5pivL+WQpl3vvJvlvZNmb3JlRW5kTGlzdEF1dGg95piv77yM5Yi355qE6KGo5paw5aKe55So5oi35a2X5q61dXNlcmlk77yM5YmN5Y+wbGlzdOWIl+ihqOaOpeWPo+S7heiDveafpeeci+iHquW3seeahOiusOW9leWSjGFkZOaOpeWPo+WQjuWPsOi1i+WAvHVzZXJpZOeahOWAvFxuLi4uXG5QYXRoOiBtYWluL21vZGVsLnB5XG4uLi5cblxuICAgIGRlZiBfX0dldEJ5UGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fbG9naW5Vc2VyX18gPSBtb2RlbC5fX2xvZ2luVXNlcl9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fbG9naW5Vc2VyX18gPSBOb25lXG5cbiAgICAgICAgaWYgX19sb2dpblVzZXJfXyAhPSBOb25lIGFuZCBfX2xvZ2luVXNlcl9fIT0ndXNlcm5hbWUnOlxuICAgICAgICAgICAgaWYgcGFyYW1zLmdldCgndXNlcm5hbWUnKTpcbiAgICAgICAgICAgICAgICBwYXJhbXNbbW9kZWwuX19sb2dpblVzZXJfX10gPSBjb3B5LmRlZXBjb3B5KHBhcmFtcy5nZXQoJ3VzZXJuYW1lJykpXG4gICAgICAgICAgICAgICAgZGVsIHBhcmFtc1sndXNlcm5hbWUnXVxuICAgICAgICBpZiBtb2RlbC5fX3RhYmxlbmFtZV9fICE9ICd1c2Vycyc6XG4gICAgICAgICAgICBpZiBwYXJhbXMuZ2V0KCdwYXNzd29yZCcpOlxuICAgICAgICAgICAgICAgIHBhcmFtc1snbWltYSddID0gY29weS5kZWVwY29weShwYXJhbXMuZ2V0KCdwYXNzd29yZCcpKVxuICAgICAgICAgICAgICAgICMgZGVsIHBhcmFtc1sncGFzc3dvcmQnXVxuXG4gICAgICAgICMg5YmN56uv5Lyg5LqG5peg55So5Y+C5pWw5ZKM5Lyg6ZSZ5Y+C5pWw5ZCN77yM5Zyo6L+Z6YeM5L+u5pS5XG4gICAgICAgIHBhcmFtc3MgPSB7fVxuICAgICAgICBjb2x1bW5MaXN0ID0gc2VsZi5nZXRhbGxjb2x1bW4obW9kZWwsIG1vZGVsKVxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcbiAgICAgICAgICAgIGlmIGsgaW4gY29sdW1uTGlzdDpcbiAgICAgICAgICAgICAgICBwYXJhbXNzW2tdID0gdlxuXG4gICAgICAgIGRhdGFzXyA9IG1vZGVsLm9iamVjdHMuZmlsdGVyKCoqcGFyYW1zcykuYWxsKClcblxuICAgICAgICByZXR1cm4gc2VsZi50b19saXN0KGRhdGFzXywgZGF0YXNfLG1vZGVsKVxuXG4gICAgZGVmIGdldGJ5cGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuICAgICAgICByZXR1cm4gc2VsZi5fX0dldEJ5UGFyYW1zKG1vZGVsLCBtb2RlbCwgcGFyYW1zKVxuLi4uXG5QYXRoOiBtYWluL3NjaGVtYV92LnB5XG4uLi5cblxuICAgICAgICBpZiBmaWxlOlxuICAgICAgICAgICAgZmlsZW5hbWUgPSBmaWxlLm5hbWVcbiAgICAgICAgICAgIGZpbGVzdWZmaXggPSBmaWxlbmFtZS5zcGxpdChcIi5cIilbLTFdXG4gICAgICAgICAgICBmaWxlX25hbWUgPSBcInt9Lnt9XCIuZm9ybWF0KGludChmbG9hdCh0aW1lLnRpbWUoKSkgKiAxMDAwKSwgZmlsZXN1ZmZpeClcbiAgICAgICAgICAgIGlmIHR5cGUgIT0gTm9uZSBhbmQgJ190ZW1wbGF0ZScgaW4gdHlwZTpcbiAgICAgICAgICAgICAgICBmaWxlX25hbWU9XCJ7fS57fVwiLmZvcm1hdCh0eXBlLGZpbGVzdWZmaXgpXG4gICAgICAgICAgICBmaWxlUGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJ0ZW1wbGF0ZXMvdXBsb2FkXCIsIGZpbGVfbmFtZSlcbiAgICAgICAgICAgIHByaW50KFwiZmlsZVBhdGg9PT09PT09PT09PT5cIiwgZmlsZVBhdGgpXG5cbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlUGF0aCwgJ3diKycpIGFzIGRlc3RpbmF0aW9uOlxuICAgICAgICAgICAgICAgIGZvciBjaHVuayBpbiBmaWxlLmNodW5rcygpOlxuICAgICAgICAgICAgICAgICAgICBkZXN0aW5hdGlvbi53cml0ZShjaHVuaylcbiAgICAgICAgICAgIG1zZ1tcImZpbGVcIl0gPSBmaWxlX25hbWVcbiAgICAgICAgICAgICMg5Yik5pat5piv5ZCm6ZyA6KaB5L+d5a2Y5Li65Lq66IS46K+G5Yir5Z+656GA54Wn54mHXG4gICAgICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuICAgICAgICAgICAgdHlwZTEgPSByZXFfZGljdC5nZXQoXCJ0eXBlXCIsIDApXG4gICAgICAgICAgICBwcmludChcInR5cGUxPT09PT09PT5cIix0eXBlMSlcbiAgICAgICAgICAgIHR5cGUxID0gc3RyKHR5cGUxKVxuICAgICAgICAgICAgaWYgdHlwZTEgPT0gJzEnOlxuICAgICAgICAgICAgICAgIHBhcmFtcyA9IHtcIm5hbWVcIjpcImZhY2VGaWxlXCIsXCJ2YWx1ZVwiOiBmaWxlX25hbWV9XG4gICAgICAgICAgICAgICAgY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCBwYXJhbXMpXG4gICAgICAgICAgICAjIEhERlMgdXBsb2FkIHJlbW92ZWRcblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcbi4uLlxuUGF0aDogbWFpbi9Ob25naHVfdi5weVxuLi4uXG5cbmRlZiBub25naHVfYXV0b1NvcnQocmVxdWVzdCk6XG4gICAgJycnXG4gICAg77yO5pm66IO95o6o6I2Q5Yqf6IO9KOihqOWxnuaAp++8mltpbnRlbFJlY29t77yI5pivL+WQpu+8iV0s5paw5aKeY2xpY2t0aW1lW+WJjeerr+S4jeaYvuekuuivpeWtl+autV3lrZfmrrXvvIjosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeabtOaWsO+8ie+8jOaMiWNsaWNrdGltZeaOkuW6j+afpeivoilcbuS4u+imgeS/oeaBr+WIl+ihqO+8iOWmguWVhuWTgeWIl+ihqO+8jOaWsOmXu+WIl+ihqO+8ieS4reS9v+eUqO+8jOaYvuekuuacgOi/keeCueWHu+eahOaIluacgOaWsOa3u+WKoOeahDXmnaHorrDlvZXlsLHooYxcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsICBcImRhdGFcIjp7XCJjdXJyUGFnZVwiOjEsXCJ0b3RhbFBhZ2VcIjoxLFwidG90YWxcIjoxLFwicGFnZVNpemVcIjoxMCxcImxpc3RcIjpbXX19XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIFwiY2xpY2tudW1cIiAgaW4gbm9uZ2h1LmdldGFsbGNvbHVtbihub25naHUsbm9uZ2h1KTpcbiAgICAgICAgICAgIHJlcV9kaWN0Wydzb3J0J109J2NsaWNrbnVtJ1xuICAgICAgICBlbGlmIFwiYnJvd3NlZHVyYXRpb25cIiAgaW4gbm9uZ2h1LmdldGFsbGNvbHVtbihub25naHUsbm9uZ2h1KTpcbiAgICAgICAgICAgIHJlcV9kaWN0Wydzb3J0J109J2Jyb3dzZWR1cmF0aW9uJ1xuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgcmVxX2RpY3RbJ3NvcnQnXT0nY2xpY2t0aW1lJ1xuICAgICAgICByZXFfZGljdFsnb3JkZXInXT0nZGVzYydcbiAgICAgICAgbXNnWydkYXRhJ11bJ2xpc3QnXSwgbXNnWydkYXRhJ11bJ2N1cnJQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbFBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsJ10sIFxcXG4uLi5cblxuZGVmIG5vbmdodV9saXN0KHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWJjeWPsOWIhumhtVxuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgIFwiZGF0YVwiOntcImN1cnJQYWdlXCI6MSxcInRvdGFsUGFnZVwiOjEsXCJ0b3RhbFwiOjEsXCJwYWdlU2l6ZVwiOjEwLFwibGlzdFwiOltdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgI+iOt+WPluWFqOmDqOWIl+WQjVxuICAgICAgICBjb2x1bW5zPSAgbm9uZ2h1LmdldGFsbGNvbHVtbiggbm9uZ2h1LCBub25naHUpXG4gICAgICAgIGlmIFwidmlwcmVhZFwiIGluIHJlcV9kaWN0IGFuZCBcInZpcHJlYWRcIiBub3QgaW4gY29sdW1uczpcbiAgICAgICAgICBkZWwgcmVxX2RpY3RbXCJ2aXByZWFkXCJdXG4gICAgICAgICPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RfXz1ub25naHUuX19mb3JlRW5kTGlzdF9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RfXz1Ob25lXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RBdXRoX189bm9uZ2h1Ll9fZm9yZUVuZExpc3RBdXRoX19cbiAgICAgICAgZXhjZS4uLiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFhSmt1SHlaYmVvbXpzNVBaM2VpdmsifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MiwiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MjM6NTcuODI0WiIsInJlc3BvbnNlX3RleHQiOiJMZXQgbWUgYWxzbyBjaGVjayB0aGUgdXRpbGl0eSBmaWxlcyB0byB1bmRlcnN0YW5kIHRoZSBjb21tb24gZnVuY3Rpb25hbGl0eTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiIwZjUxYWMyMC05NzM3LTRmYjAtOTkzYi0wMWE4YTExYjk1MzQiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFVcTdFV2VlcDFORzRGRnNVMkZDd0pBIiwidG9vbF9uYW1lIjoibGF1bmNoLXByb2Nlc3MiLCJpbnB1dF9qc29uIjoie1wiY29tbWFuZFwiOiBcIm1rZGlyIC1wIG5ldy97ZGoyLG1haW4sdXRpbCx4bWlkZGxld2FyZSx0ZW1wbGF0ZXMve2Zyb250L2Fzc2V0cyx1cGxvYWR9fVwiLCBcIndhaXRcIjogdHJ1ZSwgXCJtYXhfd2FpdF9zZWNvbmRzXCI6IDYwfSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6Ik5vdyBJIGhhdmUgYSBnb29kIHVuZGVyc3RhbmRpbmcgb2YgdGhlIGNvZGViYXNlLiBMZXQgbWUgY3JlYXRlIGEgcGxhbiBmb3IgcmVmYWN0b3JpbmcgdGhlIHByb2plY3QgYW5kIHNhdmluZyBpdCBpbiBhIG5ldyBkaXJlY3RvcnkuXG5cbiMjIFJlZmFjdG9yaW5nIFBsYW5cblxuQmFzZWQgb24gbXkgYW5hbHlzaXMgb2YgdGhlIGNvZGViYXNlLCBJJ2xsIGNyZWF0ZSBhIGNvbXBsZXRlbHkgcmVmYWN0b3JlZCB2ZXJzaW9uIG9mIHRoZSBwcm9qZWN0IHdpdGggaW1wcm92ZWQgc3RydWN0dXJlLCBvcmdhbml6YXRpb24sIGFuZCBjb2RlIHF1YWxpdHkuIEhlcmUncyBteSBkZXRhaWxlZCBwbGFuOlxuXG4xLiBDcmVhdGUgYSBuZXcgZGlyZWN0b3J5IHN0cnVjdHVyZVxuMi4gUmVmYWN0b3IgdGhlIG1vZGVscyB3aXRoIGJldHRlciBvcmdhbml6YXRpb25cbjMuIFJlZmFjdG9yIHRoZSB2aWV3cyB3aXRoIGEgbW9yZSBjb25zaXN0ZW50IGFwcHJvYWNoXG40LiBJbXByb3ZlIHRoZSB1dGlsaXR5IGZ1bmN0aW9uc1xuNS4gRW5oYW5jZSB0aGUgbWlkZGxld2FyZVxuNi4gVXBkYXRlIHRoZSBVUkwgcm91dGluZ1xuNy4gTWFpbnRhaW4gdGhlIHNhbWUgZnVuY3Rpb25hbGl0eSB3aGlsZSBpbXByb3ZpbmcgY29kZSBxdWFsaXR5XG5cbkxldCdzIHN0YXJ0IGJ5IGNyZWF0aW5nIHRoZSBuZXcgZGlyZWN0b3J5IHN0cnVjdHVyZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHV0aWwvYXV0aC5weVxuIyBjb2Rpbmc6dXRmLThcbiMgYXV0aG9yOmlsYVxuaW1wb3J0IGJhc2U2NCwgY29weVxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5cbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsIGltcG9ydCBtZXNzYWdlIGFzIG1lc1xuXG5cbmNsYXNzIEF1dGgob2JqZWN0KTpcbiAgICBkZWYgYXV0aGVudGljYXRlKHNlbGYsIG1vZGVsLCByZXFfZGljdCk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDnlKjmiLfnmbvlvZXvvIznmbvlvZXmiJDlip/ov5Tlm550b2tlbu+8m+eZu+W9leWksei0pei/lOWbnuWksei0peWOn+WboFxuICAgICAgICA6cGFyYW0gdXNlcm5hbWU66LSm5Y+3XG4gICAgICAgIDpwYXJhbSBwYXNzd29yZDrlr4bnoIFcbiAgICAgICAgOnJldHVybjoganNvblxuICAgICAgICBcIlwiXCJcbiAgICAgICAgbXNnID0geydjb2RlJzogbm9ybWFsX2NvZGUsICdtc2cnOiBtZXMubm9ybWFsX2NvZGUsICdkYXRhJzoge319XG4gICAgICAgIHRhYmxlbmFtZSA9IG1vZGVsLl9fdGFibGVuYW1lX19cbiAgICAgICAgZW5jb2RlX2RpY3QgPSB7XCJ0YWJsZW5hbWVcIjogdGFibGVuYW1lLCBcInBhcmFtc1wiOiByZXFfZGljdH1cblxuICAgICAgICBlbmNvZGVfc3RyID0gYmFzZTY0LmI2NGVuY29kZShzdHIoZW5jb2RlX2RpY3QpLmVuY29kZShcInV0Zi04XCIpKVxuICAgICAgICBtc2dbJ2RhdGEnXVtcImlkXCJdID0gcmVxX2RpY3QuZ2V0KFwiaWRcIilcbiAgICAgICAgbXNnW1wiaWRcIl0gPSByZXFfZGljdC5nZXQoXCJpZFwiKVxuICAgICAgICBtc2dbJ3Rva2VuJ10gPSBlbmNvZGVfc3RyLmRlY29kZSgndXRmLTgnKVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuICAgIGRlZiBnZXRfdG9rZW4oc2VsZiwgbW9kZWwsIHJlcV9kaWN0KTpcbiAgICAgICAgdGFibGVuYW1lPW1vZGVsLl9fdGFibGVuYW1lX19cbiAgICAgICAgZW5jb2RlX2RpY3QgPSB7XCJ0YWJsZW5hbWVcIjp0YWJsZW5hbWUsIFwicGFyYW1zXCI6IHJlcV9kaWN0fVxuXG4gICAgICAgIGVuY29kZV9zdHIgPSBiYXNlNjQuYjY0ZW5jb2RlKHN0cihlbmNvZGVfZGljdCkuZW5jb2RlKFwidXRmOFwiKSlcbiAgICAgICAgcmV0dXJuIGVuY29kZV9zdHIuZGVjb2RlKCd1dGYtOCcpXG5cbiAgICBkZWYgaWRlbnRpZnkoc2VsZiwgcmVxdWVzdCk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDnlKjmiLfpibTmnYNcbiAgICAgICAgOnBhcmFtIHJlcXVlc3Q65pys5qyh6K+35rGC5a+56LGhXG4gICAgICAgIDpyZXR1cm46IGxpc3RcbiAgICAgICAgXCJcIlwiXG5cbiAgICAgICAgbXNnID0geydjb2RlJzogbm9ybWFsX2NvZGUsICdtc2cnOiBtZXMubm9ybWFsX2NvZGUsICdkYXRhJzoge319XG4gICAgICAgICMgZGphbmdv55qEaGVhZGVy6KKr5aSE55CG6L+H5LqGXG4gICAgICAgIHRva2VuID0gcmVxdWVzdC5NRVRBLmdldCgnSFRUUF9UT0tFTicpXG5cbiAgICAgICAgaWYgdG9rZW4gIGFuZCB0b2tlbiAhPVwibnVsbFwiOlxuXG4gICAgICAgICAgICBhdXRoX3Rva2VuID0gY29weS5kZWVwY29weSh0b2tlbilcblxuICAgICAgICAgICAgZGVjb2RlX3N0ciA9IGJhc2U2NC5iNjRkZWNvZGUoYXV0aF90b2tlbikuZGVjb2RlKFwidXRmOFwiKVxuICAgICAgICAgICAgZGVjb2RlX3N0cj1kZWNvZGVfc3RyLnJlcGxhY2UoJ1wibnVsbFwiJywnXCJcIicpLnJlcGxhY2UoJ251bGwnLCdcIlwiJylcbiAgICAgICAgICAgIGRlY29kZV9kaWN0ID0gZXZhbChkZWNvZGVfc3RyKVxuXG4gICAgICAgICAgICB0YWJsZW5hbWUyID0gZGVjb2RlX2RpY3QuZ2V0KFwidGFibGVuYW1lXCIpXG5cbiAgICAgICAgICAgIHBhcmFtczIgPSBkZWNvZGVfZGljdC5nZXQoXCJwYXJhbXNcIix7fSlcblxuICAgICAgICAgICAgZGF0YXM9Tm9uZVxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxuICAgICAgICAgICAgZm9yIG1vZGVsIGluIGFsbE1vZGVsczpcbiAgICAgICAgICAgICAgICBpZiBtb2RlbC5fX3RhYmxlbmFtZV9fID09IHRhYmxlbmFtZTI6XG4gICAgICAgICAgICAgICAgICAgIGRhdGFzID0gbW9kZWwuZ2V0YnlwYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMyKVxuXG4gICAgICAgICAgICBpZiBub3QgZGF0YXM6XG4gICAgICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcbiAgICAgICAgICAgICAgICBtc2dbJ21zZyddID0gJ+aJvuS4jeWIsOivpeeUqOaIt+S/oeaBrydcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBtc2dcbiAgICAgICAgICAgIGVsc2U6XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5zZXNzaW9uWyd0YWJsZW5hbWUnXSA9IHRhYmxlbmFtZTJcbiAgICAgICAgICAgICAgICByZXF1ZXN0LnNlc3Npb25bJ3BhcmFtcyddID0gcGFyYW1zMlxuICAgICAgICAgICAgICAgIG1zZ1snbXNnJ10gPSAn6Lqr5Lu96aqM6K+B6YCa6L+H44CCJ1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9IG1zZ1xuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSAnaGVhZGVyc+acquWMheWQq+iupOivgeS/oeaBr+OAgidcbiAgICAgICAgICAgIHJlc3VsdCA9IG1zZ1xuICAgICAgICByZXR1cm4gcmVzdWx0XG4uLi5cblBhdGg6IHV0aWwvY29kZXMucHlcbiMgY29kaW5nOnV0Zi04XG5ub3JtYWxfY29kZSA9IDAgICMg5q2j5bi4XG5yZWRpcmVjdF9jb2RlID0gMzAxICAjIOi3s+i9rFxudGVtcG9yYXJ5X3JlZGlyZWN0X2NvZGUgPSAzMDIgICMg5pqC5pe26Lez6L2sXG5cbmVtcHR5X3BhcmFtX2NvZGUgPSAxMDAwMSAgIyDor7fmsYLlj4LmlbDkuLrnqbrmiJbplJnor69cbnZhbGlkYXRlX3BhcmFtX2NvZGUgPSAxMDAwMiAgIyDor7fmsYLlj4LmlbDkuI3op4TojINcbmlzX25vdF9qc29uX2NvZGUgPSAxMDAwMyAgIyDmlbDmja7moLzlvI/kuI3mmK9qc29uXG5cbm90aGVyX2NvZGUgPSAxMDAyMCAgIyDlhbblroPplJnor69cbmNydWRfZXJyb3JfY29kZSA9IDEwMDIxICAjIOaVsOaNruW6k+aTjeS9nOWksei0pVxuaGVhZGVyX2Vycm9yX2NvZGUgPSAxMDAyMyAgIyDlpLTpg6jplJnor69cbmNhcHRjaGFfZXJyb3JfY29kZSA9IDEwMDI0ICAjIOmqjOivgeeggemUmeivr1xuaWRfZXhpc3RfY29kZSA9IDEwMDI1ICAjIGlk5oiW55So5oi35ZCN5bey5a2Y5ZyoXG5pZF9ub3RleGlzdF9jb2RlID0gMTAwMjYgICMgaWTmiJbnlKjmiLflkI3kuI3lrZjlnKhcbnVzZXJuYW1lX2Vycm9yX2NvZGUgPSAxMDAyNyAgIyDnlKjmiLflkI3plJnor69cbnBhc3N3b3JkX2Vycm9yX2NvZGUgPSAxMDAyOCAgIyDlr4bnoIHplJnor69cbmZpbGVfbm90ZXhpc3RfY29kZSA9IDEwMDI5ICAjIOS4iuS8oOaWh+S7tuS4jeWtmOWcqFxuXG50b2tlbl9lcnJvcl9jb2RlID0gMjAwMDEgICMgdG9rZW7plJnor69cbnRva2VuX2V4cGlyZWRfY29kZSA9IDIwMDAyICAjIHRva2Vu6ZSZ6K+vXG5ub25fYXV0aG9yaXplZF9jb2RlID0gMjAwMDMgICMg5peg5p2D6ZmQXG5cbnN5c3RlbV9lcnJvcl9jb2RlID0gNDAwMDEgICMg57O757uf57qn6ZSZ6K+vXG5yZXF1ZXN0X2V4cGlyZWRfY29kZSA9IDQwMDAyICAjIOivt+axguW3sui/h+acn1xucmVwZWF0ZWRfcmVxdWVzdF9jb2RlID0gNDAwMDMgICMg6YeN5aSN6K+35rGCXG4uLi5cblBhdGg6IHV0aWwvY29tbW9uLnB5XG4jIGNvZGluZzp1dGYtOFxuaW1wb3J0IGhhc2hsaWJcbmltcG9ydCBiYXNlNjRcbmZyb20gQ3J5cHRvLkNpcGhlciBpbXBvcnQgREVTLEFFU1xuZnJvbSBDcnlwdG8uVXRpbC5QYWRkaW5nIGltcG9ydCBwYWQsdW5wYWRcblxuY2xhc3MgQ29tbW9uKG9iamVjdCk6XG5cbiAgICBkZWYgbWQ1KHNlbGYsIHB3ZCk6XG4gICAgICAgIG1kNSA9IGhhc2hsaWIubWQ1KClcbiAgICAgICAgbWQ1LnVwZGF0ZShwd2QuZW5jb2RlKCkpXG4gICAgICAgIHJldHVybiBtZDUuaGV4ZGlnZXN0KClcblxuXG5cbi4uLlxuUGF0aDogdXRpbC9tZXNzYWdlLnB5XG4jIGNvZGluZzp1dGYtOFxubm9ybWFsX2NvZGUgPSBcIuato+W4uFwiXG5yZWRpcmVjdF9jb2RlID0gXCLot7PovaxcIlxudGVtcG9yYXJ5X3JlZGlyZWN0X2NvZGUgPSBcIuaaguaXtui3s+i9rFwiXG5cbmVtcHR5X3BhcmFtX2NvZGUgPSBcIuivt+axguWPguaVsOS4uuepuuaIlumUmeivr1wiXG52YWxpZGF0ZV9wYXJhbV9jb2RlID0gXCLor7fmsYLlj4LmlbDkuI3op4TojINcIlxuaXNfbm90X2pzb25fY29kZSA9IFwi5pWw5o2u5qC85byP5LiN5pivanNvblwiXG5cbm90aGVyX2NvZGUgPSBcIuWFtuWug+mUmeivr1wiXG5jcnVkX2Vycm9yX2NvZGUgPSBcIuaVsOaNruW6k+aTjeS9nOWksei0pVwiXG5oZWFkZXJfZXJyb3JfY29kZSA9IFwi5aS06YOo6ZSZ6K+vXCJcbmNhcHRjaGFfZXJyb3JfY29kZSA9IFwi6aqM6K+B56CB6ZSZ6K+vXCJcbmlkX2V4aXN0X2NvZGUgPSBcImlk5oiW6K6w5b2V5bey5a2Y5ZyoXCJcbmlkX25vdGV4aXN0X2NvZGUgPSBcImlk5oiW6K6w5b2V5LiN5a2Y5ZyoXCJcbmZpbGVfbm90ZXhpc3RfY29kZSA9IFwi5LiK5Lyg5paH5Lu25LiN5a2Y5ZyoXCJcbnVzZXJuYW1lX2Vycm9yX2NvZGUgPSBcIueUqOaIt+WQjemUmeivr1wiXG5wYXNzd29yZF9lcnJvcl9jb2RlID0gXCLotKblj7fmiJbogIXlr4bnoIHplJnor69cIlxuXG50b2tlbl9lcnJvcl9jb2RlID0gXCJ0b2tlbumUmeivr1wiXG50b2tlbl9leHBpcmVkX2NvZGUgPSBcInRva2Vu6ZSZ6K+vXCJcbm5vbl9hdXRob3JpemVkX2NvZGUgPSBcIuaXoOadg+mZkFwiXG5cbnN5c3RlbV9lcnJvcl9jb2RlID0gXCLns7vnu5/nuqfplJnor69cIlxucmVxdWVzdF9leHBpcmVkX2NvZGUgPSBcIuivt+axguW3sui/h+acn1wiXG5yZXBlYXRlZF9yZXF1ZXN0X2NvZGUgPSBcIumHjeWkjeivt+axglwiXG4uLi5cblBhdGg6IG1haW4vR3VveXVhbnpodWFuZ3RhaV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBndW95dWFuemh1YW5ndGFpXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vV3VsaXVfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgd3VsaXVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9HdW95dWFueGlueGlfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgZ3VveXVhbnhpbnhpXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vQ2FuZ2NodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBjYW5nY2h1XG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vWW9uZ2h1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IHlvbmdodVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiBtYWluL05vbmdodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBub25naHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9HdW9waW54aW54aV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBndW9waW54aW54aVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiB1dGlsL0N1c3RvbUpTT05FbmNvZGVyLnB5XG5mcm9tIGRhdGV0aW1lIGltcG9ydCBkYXRldGltZSwgZGF0ZVxuaW1wb3J0IGpzb25cbmZyb20gZGVjaW1hbCBpbXBvcnQgRGVjaW1hbFxuXG5jbGFzcyBDdXN0b21Kc29uRW5jb2Rlcihqc29uLkpTT05FbmNvZGVyKTpcbiAgICBkZWYgZGVmYXVsdChzZWxmLCBvYmopOlxuICAgICAgICBpZiBpc2luc3RhbmNlKG9iaiwgZGF0ZXRpbWUpOlxuICAgICAgICAgICAgcmV0dXJuIG9iai5zdHJmdGltZSgnJVktJW0tJWQgJUg6JU06JVMnKVxuICAgICAgICBpZiBpc2luc3RhbmNlKG9iaiwgZGF0ZSk6XG4gICAgICAgICAgICByZXR1cm4gb2JqLnN0cmZ0aW1lKCclWS0lbS0lZCcpXG4gICAgICAgIGlmIGlzaW5zdGFuY2Uob2JqLCBEZWNpbWFsKTpcbiAgICAgICAgICAgICMg5aaC5p6c5piv5pW05pWw77yM6L+U5Zue5pW05pWwXG4gICAgICAgICAgICBpZiBvYmogJSAxID09IDA6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGludChvYmopXG4gICAgICAgICAgICAjIOWQpuWImei/lOWbnua1rueCueaVsFxuICAgICAgICAgICAgcmV0dXJuIGZsb2F0KG9iailcbiAgICAgICAgcmV0dXJuIHN1cGVyKCkuZGVmYXVsdChvYmopLi4uXG5QYXRoOiB4bWlkZGxld2FyZS94YXV0aC5weVxuI2NvZGluZzp1dGYtOFxuXG5mcm9tIGRqYW5nby51dGlscy5kZXByZWNhdGlvbiBpbXBvcnQgTWlkZGxld2FyZU1peGluXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcblxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5cblxuY2xhc3MgWGF1dGgoTWlkZGxld2FyZU1peGluKTpcbiAgICBkZWYgcHJvY2Vzc19yZXF1ZXN0KHNlbGYscmVxdWVzdCk6XG4gICAgICAgIGZ1bGxQYXRoID0gcmVxdWVzdC5nZXRfZnVsbF9wYXRoKClcbiAgICAgICAgcHJpbnQoXCJmdWxsUGF0aD09PT09PT09PT09PT09PT5cIiwgZnVsbFBhdGgpXG4gICAgICAgIGlmIHJlcXVlc3QuTUVUQS5nZXQoJ0hUVFBfVVBHUkFERScpPT0nd2Vic29ja2V0JzpcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICBpZiByZXF1ZXN0Lm1ldGhvZCA9PSAnR0VUJzpcblxuICAgICAgICAgICAgZmlsdGVyTGlzdD1bXG4gICAgICAgICAgICAgICAgXCIvaW5kZXhcIixcbiAgICAgICAgICAgICAgICBcIi9mb2xsb3dcIixcbiAgICAgICAgICAgICAgICBcIi9mYXZpY29uLmljb1wiLFxuICAgICAgICAgICAgICAgIFwiL2xvZ2luXCIsXG4gICAgICAgICAgICAgICAgXCIvcmVnaXN0ZXJcIixcbiAgICAgICAgICAgICAgICBcIi9ub3RpZnlcIixcbiAgICAgICAgICAgICAgICBcIi9maWxlXCIsXG4gICAgICAgICAgICAgICAgJy5qcycsXG4gICAgICAgICAgICAgICAgXCIuY3NzXCIsXG4gICAgICAgICAgICAgICAgXCIuanBnXCIsXG4gICAgICAgICAgICAgICAgXCIuanBlZ1wiLFxuICAgICAgICAgICAgICAgIFwiLnBuZ1wiLFxuICAgICAgICAgICAgICAgIFwiLmdpZlwiLFxuICAgICAgICAgICAgICAgIFwiLm1wNFwiLFxuICAgICAgICAgICAgICAgICcubXAzJyxcbiAgICAgICAgICAgICAgICBcIi50dGZcIixcbiAgICAgICAgICAgICAgICBcIi53b3RmXCIsXG4gICAgICAgICAgICAgICAgXCIud29mZlwiLFxuICAgICAgICAgICAgICAgIFwiLndvZmYyXCIsXG4gICAgICAgICAgICAgICAgXCIub3RmXCIsXG4gICAgICAgICAgICAgICAgXCIuZW90XCIsXG4gICAgICAgICAgICAgICAgXCIuc3ZnXCIsXG4gICAgICAgICAgICAgICAgXCIuY3N2XCIsXG4gICAgICAgICAgICAgICAgXCIud2VicFwiLFxuICAgICAgICAgICAgICAgIFwiLnhsc1wiLFxuICAgICAgICAgICAgICAgIFwiLnhsc3hcIixcbiAgICAgICAgICAgICAgICBcIi5kb2NcIixcbiAgICAgICAgICAgICAgICBcIi5kb2N4XCIsXG4gICAgICAgICAgICAgICAgXCIucHB0XCIsXG4gICAgICAgICAgICAgICAgXCIucHB0eFwiLFxuICAgICAgICAgICAgICAgIFwiLmh0bWxcIixcbiAgICAgICAgICAgICAgICBcIi5odG1cIixcbiAgICAgICAgICAgICAgICBcImRldGFpbFwiLFxuICAgICAgICAgICAgICAgIFwiL2ZvcnVtL2ZsaXN0XCIsXG4gICAgICAgICAgICAgICAgXCIvZm9ydW0vbGlzdFwiLFxuICAgICAgICAgICAgICAgIFwiL2FkbWluXCIsXG4gICAgICAgICAgICAgICAgXCIvc2VjdXJpdHlcIixcbiAgICAgICAgICAgICAgICBcIi9hdXRvU29ydFwiLFxuICAgICAgICAgICAgICAgIFwiL2NvbmZpZy9saXN0XCIsXG4gICAgICAgICAgICAgICAgXCIvbmV3cy9saXN0XCIsXG4gICAgICAgICAgICAgICAgXCIveGFkbWluXCIsXG4gICAgICAgICAgICAgICAgXCIvZmlsZS9kb3dubG9hZFwiLFxuICAgICAgICAgICAgICAgICBcIi97fS9yZW1pbmQvXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgICAgXCIve30vb3B0aW9uL1wiLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICBcInJlc2V0UGFzc1wiLFxuICAgICAgICAgICAgICAgIFwidXBkYXRlQnJvd3NlRHVyYXRpb25cIixcbiAgICAgICAgICAgIF1cblxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxuICAgICAgICAgICAgIyBwcmludChcImFsbE1vZGVsczpcIixhbGxNb2RlbHMuKVxuICAgICAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxuICAgICAgICAgICAgICAgIHRyeTpcbiAgICAgICAgICAgICAgICAgICAgZm9yZUVuZExpc3Q9bS5fX2ZvcmVFbmRMaXN0X19cbiAgICAgICAgICAgICAgICBleGNlcHQ6XG4gICAgICAgICAgICAgICAgICAgIGZvcmVFbmRMaXN0PU5vbmVcbiAgICAgICAgICAgICAgICBpZiAgZm9yZUVuZExpc3QgaXMgTm9uZSBvciBmb3JlRW5kTGlzdCAhPSBcIuWJjeimgeeZu1wiOlxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcIi97fS9zZW5kZW1haWxcIi5mb3JtYXQobS5fX3RhYmxlbmFtZV9fKSlcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyTGlzdC5hcHBlbmQoXCIve30vc2VuZHNtc1wiLmZvcm1hdChtLl9fdGFibGVuYW1lX18pKVxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcIi97fS9saXN0XCIuZm9ybWF0KG0uX190YWJsZW5hbWVfXykpXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlckxpc3QuYXBwZW5kKFwiL3t9L2RldGFpbFwiLmZvcm1hdChtLl9fdGFibGVuYW1lX18pKVxuXG4gICAgICAgICAgICBhdXRoID0gVHJ1ZVxuXG4gICAgICAgICAgICBpZiBmdWxsUGF0aD09Jy8nOlxuICAgICAgICAgICAgICAgIHBhc3NcbiAgICAgICAgICAgIGVsc2U6XG4gICAgICAgICAgICAgICAgZm9yIGkgaW4gZmlsdGVyTGlzdDpcbiAgICAgICAgICAgICAgICAgICAgaWYgaSBpbiBmdWxsUGF0aDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGF1dGg9RmFsc2VcbiAgICAgICAgICAgICAgICBpZiBhdXRoPT1UcnVlOlxuICAgICAgICAgICAgICAgICAgICByZXN1bHQgPSBBdXRoLmlkZW50aWZ5KEF1dGgsIHJlcXVlc3QpXG5cbiAgICAgICAgICAgICAgICAgICAgaWYgcmVzdWx0LmdldCgnY29kZScpICE9IG5vcm1hbF9jb2RlOlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShyZXN1bHQpXG4gICAgICAgIGVsaWYgcmVxdWVzdC5tZXRob2QgPT0gJ1BPU1QnOlxuICAgICAgICAgICAgcG9zdF9saXN0ID0gW1xuICAgICAgICAgICAgICAgICcve30vZGVmYXVsdHVzZXIvcmVnaXN0ZXInLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICAnL3t9L2RlZmF1bHR1c2VyL2xvZ2luJy5mb3JtYXQoc2NoZW1hTmFtZSksXG4gICAgICAgICAgICAgICAgJy97fS91c2Vycy9yZWdpc3RlcicuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgICcve30vdXNlcnMvbG9naW4nLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICBcIi97fS9leGFtdXNlcnMvbG9naW5cIi5mb3JtYXQoc2NoZW1hTmFtZSksXG4gICAgICAgICAgICAgICAgXCIve30vZXhhbXVzZXJzL3JlZ2lzdGVyXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgIFwiL3t9L2ZpbGUvdXBsb2FkXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgIFwiL3VwZGF0ZVwiXG4gICAgICAgICAgICBdICAjIOWFjeiupOivgWxpc3RcbiAgICAgICAgICAgIGlmIGZ1bGxQYXRoIG5vdCBpbiBwb3N0X2xpc3QgYW5kIFwicmVnaXN0ZXJcIiBub3QgaW4gZnVsbFBhdGggYW5kIFwibG9naW5cIiBub3QgaW4gZnVsbFBhdGggYW5kIFwiZmFjZUxvZ2luXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcInVwZGF0ZVwiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCJ1cGxvYWRcIiBub3QgaW4gZnVsbFBhdGg6ICAjIOazqOWGjOaXtuS4jeajgOa1i3Rva2Vu44CCXG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gQXV0aC5pZGVudGlmeShBdXRoLCByZXF1ZXN0KVxuXG4gICAgICAgICAgICAgICAgaWYgcmVzdWx0LmdldCgnY29kZScpICE9IG5vcm1hbF9jb2RlOlxuICAgICAgICAgICAgICAgICAgICBwcmludCgnand0IGF1dGggZmFpbCcpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UocmVzdWx0KVxuLi4uXG5QYXRoOiBtYWluL1poaWxpYW5nZGVuZ2ppX3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IHpoaWxpYW5nZGVuZ2ppXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IHhtaWRkbGV3YXJlL3hwYXJhbS5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGpzb24sY29weVxuZnJvbSBkamFuZ28udXRpbHMuZGVwcmVjYXRpb24gaW1wb3J0IE1pZGRsZXdhcmVNaXhpblxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5cbmNsYXNzIFhwYXJhbShNaWRkbGV3YXJlTWl4aW4pOlxuICAgIGRlZiBwcm9jZXNzX3JlcXVlc3Qoc2VsZixyZXF1ZXN0KTpcbiAgICAgICAgZnVsbFBhdGg9cmVxdWVzdC5nZXRfZnVsbF9wYXRoKClcbiAgICAgICAgcHJpbnQoXCJmdWxsUGF0aD09PT09PT09PT09PT09PT5cIixmdWxsUGF0aClcbiAgICAgICAgaWYgcmVxdWVzdC5NRVRBLmdldCgnSFRUUF9VUEdSQURFJyk9PSd3ZWJzb2NrZXQnOlxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIGlmIFwiL2pzL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvY3NzL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvaW1nL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvZm9udHMvXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcIi9mcm9udC9cIiBub3QgaW4gZnVsbFBhdGg6XG4gICAgICAgICAgICByZXFfZGljdD17fVxuXG4gICAgICAgICAgICAj6I635Y+WcXVlcnnnmoTlj4LmlbBcbiAgICAgICAgICAgIGZvciBrLHYgaW4gcmVxdWVzdC5HRVQuaXRlbXMoKTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFtrXT12XG5cbiAgICAgICAgICAgIGlmIHJlcXVlc3QubWV0aG9kPT1cIlBPU1RcIjpcbiAgICAgICAgICAgICAgICBjb250ZW50VHlwZT1yZXF1ZXN0Lk1FVEEuZ2V0KCdDT05URU5UX1RZUEUnLFwiXCIpXG4gICAgICAgICAgICAgICAgIyBwcmludChcImNvbnRlbnRUeXBlPT09PT09PT09PT09PT09PT09PlwiLGNvbnRlbnRUeXBlKVxuICAgICAgICAgICAgICAgIGlmIFwianNvblwiIGluIGNvbnRlbnRUeXBlOlxuICAgICAgICAgICAgICAgICAgICAj6Kej5p6QYXBwbGljYXRpb24vanNvbueahOWPguaVsCzlpoLmnpzop6PmnpDlpLHotKUs5o+Q5YmN6L+U5Zue5oql6ZSZMjAyMDA5MjVcbiAgICAgICAgICAgICAgICAgICAgdHJ5OlxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YV8gPSBqc29uLmxvYWRzKHJlcXVlc3QuYm9keSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIHR5cGUoZGF0YV8pID09IHR5cGUoWzFdKTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXFfZGljdFsnaWRzJ10gPSBkYXRhX1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgaywgdiBpbiBkYXRhXy5pdGVtcygpOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXFfZGljdFtrXSA9IHZcbiAgICAgICAgICAgICAgICAgICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOlxuICAgICAgICAgICAgICAgICAgICAgICAgIyDojrflj5Zmb3JtZGF0YeeahOWPguaVsFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIGssIHYgaW4gcmVxdWVzdC5QT1NULml0ZW1zKCk6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxX2RpY3Rba10gPSB2XG4gICAgICAgICAgICAgICAgZWxzZTpcbiAgICAgICAgICAgICAgICAgICAgIyDojrflj5Zmb3JtZGF0YeeahOWPguaVsFxuICAgICAgICAgICAgICAgICAgICBmb3IgaywgdiBpbiByZXF1ZXN0LlBPU1QuaXRlbXMoKTpcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcV9kaWN0W2tdID0gdlxuICAgICAgICAgICAgI+WkhOeQhuWPguaVsO+8jOWOu+aOieaXoOeUqOeahOWSjOmUmeivr+eahOWPguaVsFxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFwiY3JlYXRlZFwiKSE9Tm9uZTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFsnYWRkdGltZSddPWNvcHkuZGVlcGNvcHkocmVxX2RpY3QuZ2V0KFwiY3JlYXRlZFwiKSlcbiAgICAgICAgICAgICAgICBkZWwgcmVxX2RpY3RbXCJjcmVhdGVkXCJdXG4gICAgICAgICAgICBpZiByZXFfZGljdC5nZXQoXCJ0XCIpICE9IE5vbmU6XG4gICAgICAgICAgICAgICAgZGVsIHJlcV9kaWN0W1widFwiXVxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFwiMVwiKSE9Tm9uZTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFsndHlwZSddPWNvcHkuZGVlcGNvcHkocmVxX2RpY3QuZ2V0KFwiMVwiKSlcbi4uLlxuUGF0aDogdXRpbC9iYWlkdWJjZV9hcGkucHlcbi4uLlxuXG4gICAgZGVmIGdldF9maWxlX2NvbnRlbnRfYXNfYmFzZTY0KHNlbGYscGF0aCwgdXJsZW5jb2RlZD1GYWxzZSk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDojrflj5bmlofku7ZiYXNlNjTnvJbnoIFcbiAgICAgICAgOnBhcmFtIHBhdGg6IOaWh+S7tui3r+W+hFxuICAgICAgICA6cGFyYW0gdXJsZW5jb2RlZDog5piv5ZCm5a+557uT5p6c6L+b6KGMdXJsZW5jb2RlZFxuICAgICAgICA6cmV0dXJuOiBiYXNlNjTnvJbnoIHkv6Hmga9cbiAgICAgICAgXCJcIlwiXG4gICAgICAgIHdpdGggb3BlbihwYXRoLCBcInJiXCIpIGFzIGY6XG4gICAgICAgICAgICBjb250ZW50ID0gYmFzZTY0LmI2NGVuY29kZShmLnJlYWQoKSkuZGVjb2RlKFwidXRmOFwiKVxuICAgICAgICAgICAgaWYgdXJsZW5jb2RlZDpcbiAgICAgICAgICAgICAgICBjb250ZW50ID0gdXJsbGliLnBhcnNlLnF1b3RlX3BsdXMoY29udGVudClcbiAgICAgICAgcmV0dXJuIGNvbnRlbnRcblxuaWYgX19uYW1lX189PSdfX21haW5fXyc6XG4gICAgY2xpZW50X2lkID0gJ3gyMHhPanRPc0F0YlFobTJXQnVpZnVRdycgICMgYWtcbiAgICBjbGllbnRfc2VjcmV0ID0gJ083eU1wMmRtT25DdFF0Qm9rVXQxZ042aGdGQ2NMTGNwJyAgIyBza1xuXG4gICAgIyDmnKzlnLDlm77niYflnLDlnYDvvIzmoLnmja7oh6rlt7HnmoTlm77niYfov5vooYzkv67mlLlcbiAgICBpbWFnZTEgPSAnbnVkZTEuanBnJ1xuICAgIGltYWdlMiA9ICdudWRlMi5qcGcnXG5cbiAgICBiZGI9QmFpRHVCY2UoKVxuICAgIGJkYi5iZF9jaGVjazJwaWMoaW1hZ2UxLCBpbWFnZTIpLi4uXG5QYXRoOiBkajIvc2V0dGluZ3MucHlcbi4uLlxuXG5NSURETEVXQVJFID0gW1xuICAgICdkamFuZ28ubWlkZGxld2FyZS5zZWN1cml0eS5TZWN1cml0eU1pZGRsZXdhcmUnLFxuICAgICdkamFuZ28uY29udHJpYi5zZXNzaW9ucy5taWRkbGV3YXJlLlNlc3Npb25NaWRkbGV3YXJlJyxcbiAgICAnZGphbmdvLm1pZGRsZXdhcmUuY29tbW9uLkNvbW1vbk1pZGRsZXdhcmUnLFxuICAgICMgJ2RqYW5nby5taWRkbGV3YXJlLmNzcmYuQ3NyZlZpZXdNaWRkbGV3YXJlJyxcbiAgICAnZGphbmdvLmNvbnRyaWIuYXV0aC5taWRkbGV3YXJlLkF1dGhlbnRpY2F0aW9uTWlkZGxld2FyZScsXG4gICAgJ2RqYW5nby5jb250cmliLm1lc3NhZ2VzLm1pZGRsZXdhcmUuTWVzc2FnZU1pZGRsZXdhcmUnLFxuICAgICdkamFuZ28ubWlkZGxld2FyZS5jbGlja2phY2tpbmcuWEZyYW1lT3B0aW9uc01pZGRsZXdhcmUnLFxuICAgICd0aHJlYWRsb2NhbHMubWlkZGxld2FyZS5UaHJlYWRMb2NhbE1pZGRsZXdhcmUnLFxuICAgIFwieG1pZGRsZXdhcmUueHBhcmFtLlhwYXJhbVwiLFxuICAgIFwieG1pZGRsZXdhcmUueGF1dGguWGF1dGhcIixcbiAgICAnY29yc2hlYWRlcnMubWlkZGxld2FyZS5Db3JzTWlkZGxld2FyZScsXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLmNvbW1vbi5Db21tb25NaWRkbGV3YXJlJyxcbl1cbi4uLlxuUGF0aDogdXRpbC9jb25maWdyZWFkLnB5XG4jY29kaW5nOnV0Zi04XG5mcm9tIGNvbmZpZ3BhcnNlciBpbXBvcnQgQ29uZmlnUGFyc2VyXG5cbmRlZiBjb25maWdfcmVhZChmaWxlUGF0aDpzdHIpOlxuICAgIGNmZz1Db25maWdQYXJzZXIoKVxuICAgIGNmZy5yZWFkKGZpbGVQYXRoLCBlbmNvZGluZz1cInV0Zi04LXNpZ1wiKVxuICAgIGlmIFwic3FsXCIgaW4gY2ZnLnNlY3Rpb25zKCk6XG4gICAgICAgIGRiVHlwZT1jZmcuZ2V0KCdzcWwnLCd0eXBlJylcbiAgICAgICAgaG9zdD1jZmcuZ2V0KCdzcWwnLCdob3N0JylcbiAgICAgICAgcG9ydD1jZmcuZ2V0aW50KCdzcWwnLCdwb3J0JylcbiAgICAgICAgdXNlcj1jZmcuZ2V0KCdzcWwnLCd1c2VyJylcbiAgICAgICAgcGFzc3dkPWNmZy5nZXQoJ3NxbCcsJ3Bhc3N3ZCcpXG4gICAgICAgIGRiTmFtZT1jZmcuZ2V0KCdzcWwnLCdkYicpXG4gICAgICAgIGNoYXJzZXQ9Y2ZnLmdldCgnc3FsJywnY2hhcnNldCcpXG4gICAgICAgIHJldHVybiBkYlR5cGUsaG9zdCxwb3J0LHVzZXIscGFzc3dkLGRiTmFtZSxjaGFyc2V0XG4gICAgZWxzZTpcbiAgICAgICAgcmV0dXJuIE5vbmUsTm9uZSxOb25lLE5vbmUsTm9uZSxOb25lLE5vbmUuLi5cblBhdGg6IHhtaWRkbGV3YXJlL19faW5pdF9fLnB5XG4jY29kaW5nOnV0Zi04Li4uXG5QYXRoOiBtYWluL1J1a3Vfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgcnVrdVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiBtYWluL0NodWt1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtLi4uIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxMWF3VXRMVlJ4RkNxcXFEUmNuRm9hYSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjoyLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyNDowOC44MzRaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdyBJIGhhdmUgYSBnb29kIHVuZGVyc3RhbmRpbmcgb2YgdGhlIGNvZGViYXNlLiBMZXQgbWUgY3JlYXRlIGEgcGxhbiBmb3IgcmVmYWN0b3JpbmcgdGhlIHByb2plY3QgYW5kIHNhdmluZyBpdCBpbiBhIG5ldyBkaXJlY3RvcnkuXG5cbiMjIFJlZmFjdG9yaW5nIFBsYW5cblxuQmFzZWQgb24gbXkgYW5hbHlzaXMgb2YgdGhlIGNvZGViYXNlLCBJJ2xsIGNyZWF0ZSBhIGNvbXBsZXRlbHkgcmVmYWN0b3JlZCB2ZXJzaW9uIG9mIHRoZSBwcm9qZWN0IHdpdGggaW1wcm92ZWQgc3RydWN0dXJlLCBvcmdhbml6YXRpb24sIGFuZCBjb2RlIHF1YWxpdHkuIEhlcmUncyBteSBkZXRhaWxlZCBwbGFuOlxuXG4xLiBDcmVhdGUgYSBuZXcgZGlyZWN0b3J5IHN0cnVjdHVyZVxuMi4gUmVmYWN0b3IgdGhlIG1vZGVscyB3aXRoIGJldHRlciBvcmdhbml6YXRpb25cbjMuIFJlZmFjdG9yIHRoZSB2aWV3cyB3aXRoIGEgbW9yZSBjb25zaXN0ZW50IGFwcHJvYWNoXG40LiBJbXByb3ZlIHRoZSB1dGlsaXR5IGZ1bmN0aW9uc1xuNS4gRW5oYW5jZSB0aGUgbWlkZGxld2FyZVxuNi4gVXBkYXRlIHRoZSBVUkwgcm91dGluZ1xuNy4gTWFpbnRhaW4gdGhlIHNhbWUgZnVuY3Rpb25hbGl0eSB3aGlsZSBpbXByb3ZpbmcgY29kZSBxdWFsaXR5XG5cbkxldCdzIHN0YXJ0IGJ5IGNyZWF0aW5nIHRoZSBuZXcgZGlyZWN0b3J5IHN0cnVjdHVyZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJkNTk1ZDVhNi01NzVhLTRkYTAtYmU2ZS0wY2M4NjAzMzY2MWEiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFXOTJwV3FmdDlzNDllRWY2VVd3dDRCIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9kajIvX19pbml0X18ucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6Ik5vdywgbGV0J3MgY3JlYXRlIHRoZSBuZWNlc3NhcnkgZmlsZXMgZm9yIG91ciByZWZhY3RvcmVkIHByb2plY3QuIEknbGwgc3RhcnQgd2l0aCB0aGUgY29yZSBzdHJ1Y3R1cmUgZmlsZXM6XG5cbjEuIEZpcnN0LCBsZXQncyBjcmVhdGUgdGhlIGBfX2luaXRfXy5weWAgZmlsZXMgZm9yIGVhY2ggcGFja2FnZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuPHJldHVybi1jb2RlPlxuMFxuPC9yZXR1cm4tY29kZT5cbjxzdGRvdXQ+XG5cbjwvc3Rkb3V0PlxuPHN0ZGVycj5cblxuPC9zdGRlcnI+IiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVXE3RVdlZXAxTkc0RkZzVTJGQ3dKQSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyNjo1OC43NDNaIiwicmVzcG9uc2VfdGV4dCI6Ik5vdywgbGV0J3MgY3JlYXRlIHRoZSBuZWNlc3NhcnkgZmlsZXMgZm9yIG91ciByZWZhY3RvcmVkIHByb2plY3QuIEknbGwgc3RhcnQgd2l0aCB0aGUgY29yZSBzdHJ1Y3R1cmUgZmlsZXM6XG5cbjEuIEZpcnN0LCBsZXQncyBjcmVhdGUgdGhlIGBfX2luaXRfXy5weWAgZmlsZXMgZm9yIGVhY2ggcGFja2FnZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJjYzgxMTE5MC05YmJkLTRhZmMtYTUyYi1mOWY0NmMyOTY0NjkiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDE5c1RneGZwYWRqcUJNdTNIZDJNQ1FQIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYWluL19faW5pdF9fLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFXOTJwV3FmdDlzNDllRWY2VVd3dDRCIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjI3OjA1LjEzNloiLCJyZXNwb25zZV90ZXh0IjoiIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjJhNTMwNTQ5LTQyY2UtNDZjZS1hZTU3LTExYTk4MDkyMDU4NyIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFhWUpoZFRhQmtjWms0YWFNTkdQMVgiLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L3V0aWwvX19pbml0X18ucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTlzVGd4ZnBhZGpxQk11M0hkMk1DUVAifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6Mjc6MTAuMTg4WiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiNmI5YmMxYWEtMDg4MC00MjNmLTk1NjEtNTNlYzk2ZDI0ZDk0IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxS3JDV1NrdW9ScjhrdHl0ZXl4aVh0NiIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcveG1pZGRsZXdhcmUvX19pbml0X18ucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFhWUpoZFRhQmtjWms0YWFNTkdQMVgifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6Mjc6MjAuODkyWiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiZDVlMWY2YTYtZDA0YS00MjY4LWIyNDAtNzE2NzZkNWNiMzRkIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVHZTVGpRRkZOOVdvazJZR0dLWFdjNiIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvZGoyL3NldHRpbmdzLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiXFxcIlxcXCJcXFwiXFxuRGphbmdvIHNldHRpbmdzIGZvciBkajIgcHJvamVjdC5cXG5cXG5SZWZhY3RvcmVkIHZlcnNpb24gb2YgdGhlIG9yaWdpbmFsIHNldHRpbmdzLnB5IGZpbGUuXFxuXFxcIlxcXCJcXFwiXFxuaW1wb3J0IG9zXFxuZnJvbSB1dGlsLmNvbmZpZ3JlYWQgaW1wb3J0IGNvbmZpZ19yZWFkXFxuXFxuIyBCdWlsZCBwYXRocyBpbnNpZGUgdGhlIHByb2plY3QgbGlrZSB0aGlzOiBvcy5wYXRoLmpvaW4oQkFTRV9ESVIsIC4uLilcXG5CQVNFX0RJUiA9IG9zLnBhdGguZGlybmFtZShvcy5wYXRoLmRpcm5hbWUob3MucGF0aC5hYnNwYXRoKF9fZmlsZV9fKSkpXFxuXFxuIyBRdWljay1zdGFydCBkZXZlbG9wbWVudCBzZXR0aW5ncyAtIHVuc3VpdGFibGUgZm9yIHByb2R1Y3Rpb25cXG4jIFNlZSBodHRwczovL2RvY3MuZGphbmdvcHJvamVjdC5jb20vZW4vMi4wL2hvd3RvL2RlcGxveW1lbnQvY2hlY2tsaXN0L1xcblxcbiMgU0VDVVJJVFkgV0FSTklORzoga2VlcCB0aGUgc2VjcmV0IGtleSB1c2VkIGluIHByb2R1Y3Rpb24gc2VjcmV0IVxcblNFQ1JFVF9LRVkgPSAndzV5biMwZ24ydHQ3cHZ1JWh2d3QwIWx0PSEkNitlcXA0JW04KXUzdSNna25tQGptKWsnXFxuXFxuIyBTRUNVUklUWSBXQVJOSU5HOiBkb24ndCBydW4gd2l0aCBkZWJ1ZyB0dXJuZWQgb24gaW4gcHJvZHVjdGlvbiFcXG5ERUJVRyA9IFRydWVcXG5cXG5BTExPV0VEX0hPU1RTID0gWycqJ11cXG5cXG4jIEFwcGxpY2F0aW9uIGRlZmluaXRpb25cXG5JTlNUQUxMRURfQVBQUyA9IFtcXG4gICAgJ2RqYW5nby5jb250cmliLmFkbWluJyxcXG4gICAgJ2RqYW5nby5jb250cmliLmF1dGgnLFxcbiAgICAnZGphbmdvLmNvbnRyaWIuY29udGVudHR5cGVzJyxcXG4gICAgJ2RqYW5nby5jb250cmliLnNlc3Npb25zJyxcXG4gICAgJ2RqYW5nby5jb250cmliLm1lc3NhZ2VzJyxcXG4gICAgJ2RqYW5nby5jb250cmliLnN0YXRpY2ZpbGVzJyxcXG4gICAgJ21haW4uYXBwcy5NYWluQ29uZmlnJyxcXG4gICAgJ2NvcnNoZWFkZXJzJyxcXG5dXFxuXFxuTUlERExFV0FSRSA9IFtcXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLnNlY3VyaXR5LlNlY3VyaXR5TWlkZGxld2FyZScsXFxuICAgICdkamFuZ28uY29udHJpYi5zZXNzaW9ucy5taWRkbGV3YXJlLlNlc3Npb25NaWRkbGV3YXJlJyxcXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLmNvbW1vbi5Db21tb25NaWRkbGV3YXJlJyxcXG4gICAgIyAnZGphbmdvLm1pZGRsZXdhcmUuY3NyZi5Dc3JmVmlld01pZGRsZXdhcmUnLFxcbiAgICAnZGphbmdvLmNvbnRyaWIuYXV0aC5taWRkbGV3YXJlLkF1dGhlbnRpY2F0aW9uTWlkZGxld2FyZScsXFxuICAgICdkamFuZ28uY29udHJpYi5tZXNzYWdlcy5taWRkbGV3YXJlLk1lc3NhZ2VNaWRkbGV3YXJlJyxcXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLmNsaWNramFja2luZy5YRnJhbWVPcHRpb25zTWlkZGxld2FyZScsXFxuICAgICd0aHJlYWRsb2NhbHMubWlkZGxld2FyZS5UaHJlYWRMb2NhbE1pZGRsZXdhcmUnLFxcbiAgICAneG1pZGRsZXdhcmUueHBhcmFtLlhwYXJhbScsXFxuICAgICd4bWlkZGxld2FyZS54YXV0aC5YYXV0aCcsXFxuICAgICdjb3JzaGVhZGVycy5taWRkbGV3YXJlLkNvcnNNaWRkbGV3YXJlJyxcXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLmNvbW1vbi5Db21tb25NaWRkbGV3YXJlJyxcXG5dXFxuXFxuUk9PVF9VUkxDT05GID0gJ2RqMi51cmxzJ1xcblxcblRFTVBMQVRFUyA9IFtcXG4gICAge1xcbiAgICAgICAgJ0JBQ0tFTkQnOiAnZGphbmdvLnRlbXBsYXRlLmJhY2tlbmRzLmRqYW5nby5EamFuZ29UZW1wbGF0ZXMnLFxcbiAgICAgICAgJ0RJUlMnOiBbb3MucGF0aC5qb2luKEJBU0VfRElSLCAndGVtcGxhdGVzJyldLFxcbiAgICAgICAgJ0FQUF9ESVJTJzogVHJ1ZSxcXG4gICAgICAgICdPUFRJT05TJzoge1xcbiAgICAgICAgICAgICdjb250ZXh0X3Byb2Nlc3NvcnMnOiBbXFxuICAgICAgICAgICAgICAgICdkamFuZ28udGVtcGxhdGUuY29udGV4dF9wcm9jZXNzb3JzLmRlYnVnJyxcXG4gICAgICAgICAgICAgICAgJ2RqYW5nby50ZW1wbGF0ZS5jb250ZXh0X3Byb2Nlc3NvcnMucmVxdWVzdCcsXFxuICAgICAgICAgICAgICAgICdkamFuZ28uY29udHJpYi5hdXRoLmNvbnRleHRfcHJvY2Vzc29ycy5hdXRoJyxcXG4gICAgICAgICAgICAgICAgJ2RqYW5nby5jb250cmliLm1lc3NhZ2VzLmNvbnRleHRfcHJvY2Vzc29ycy5tZXNzYWdlcycsXFxuICAgICAgICAgICAgXSxcXG4gICAgICAgIH0sXFxuICAgIH0sXFxuXVxcblxcbkNPUlNfQUxMT1dfQ1JFREVOVElBTFMgPSBUcnVlXFxuQ09SU19PUklHSU5fQUxMT1dfQUxMID0gVHJ1ZVxcbkNPUlNfQUxMT1dfSEVBREVSUyA9ICgnKicpXFxuXFxuU0VTU0lPTl9FTkdJTkUgPSBcXFwiZGphbmdvLmNvbnRyaWIuc2Vzc2lvbnMuYmFja2VuZHMuY2FjaGVcXFwiXFxuU0VTU0lPTl9DT09LSUVfTkFNRSA9IFxcXCJzZXNzaW9uaWRcXFwiXFxuU0VTU0lPTl9DT09LSUVfUEFUSCA9IFxcXCIvXFxcIlxcblNFU1NJT05fQ09PS0lFX0RPTUFJTiA9IE5vbmVcXG5TRVNTSU9OX0NPT0tJRV9TRUNVUkUgPSBGYWxzZVxcblNFU1NJT05fQ09PS0lFX0hUVFBPTkxZID0gVHJ1ZVxcblNFU1NJT05fQ09PS0lFX0FHRSA9IDEyMDk2MDBcXG5TRVNTSU9OX0VYUElSRV9BVF9CUk9XU0VSX0NMT1NFID0gRmFsc2VcXG5TRVNTSU9OX1NBVkVfRVZFUllfUkVRVUVTVCA9IEZhbHNlXFxuXFxuV1NHSV9BUFBMSUNBVElPTiA9ICdkajIud3NnaS5hcHBsaWNhdGlvbidcXG5cXG5FTUFJTF9CQUNLRU5EID0gJ2RqYW5nby5jb3JlLm1haWwuYmFja2VuZHMuc210cC5FbWFpbEJhY2tlbmQnXFxuRU1BSUxfVVNFX1RMUyA9IEZhbHNlXFxuRU1BSUxfVVNFX1NTTCA9IFRydWVcXG5FTUFJTF9IT1NUID0gJ3NtdHAucXEuY29tJ1xcbkVNQUlMX1BPUlQgPSA0NjVcXG5FTUFJTF9IT1NUX1VTRVIgPSAneWNsdzlAcXEuY29tJ1xcbkVNQUlMX0hPU1RfUEFTU1dPUkQgPSAnbWhicmt1YXl2a2tnYmlqZCdcXG5cXG4jIERhdGFiYXNlIGNvbmZpZ3VyYXRpb25cXG5kYnR5cGUsIGhvc3QsIHBvcnQsIHVzZXIsIHBhc3N3ZCwgZGJOYW1lLCBjaGFyc2V0ID0gY29uZmlnX3JlYWQoXFxcImNvbmZpZy5pbmlcXFwiKVxcbmRiTmFtZSA9IGRiTmFtZS5yZXBsYWNlKFxcXCIgXFxcIiwgXFxcIlxcXCIpLnN0cmlwKClcXG5wcmludChkYnR5cGUsIGhvc3QsIHBvcnQsIHVzZXIsIHBhc3N3ZCwgZGJOYW1lLCBjaGFyc2V0KVxcblxcbkRBVEFCQVNFUyA9IHtcXG4gICAgJ2RlZmF1bHQnOiB7XFxuICAgICAgICAnRU5HSU5FJzogJ2RqYW5nby5kYi5iYWNrZW5kcy5teXNxbCcsXFxuICAgICAgICAnT1BUSU9OUyc6IHtcXG4gICAgICAgICAgICAnY2hhcnNldCc6ICd1dGY4bWI0JyxcXG4gICAgICAgIH0sXFxuICAgICAgICAnTkFNRSc6ICdHcmFkdWF0ZVByb2plY3QnLFxcbiAgICAgICAgJ1VTRVInOiAnR3JhZHVhdGVQcm9qZWN0JyxcXG4gICAgICAgICdQQVNTV09SRCc6ICdHcmFkdWF0ZVByb2plY3QnLFxcbiAgICAgICAgJ0hPU1QnOiAnMTcyLjE2LjAuMTEwJyxcXG4gICAgICAgICdQT1JUJzogMzMwNixcXG4gICAgICAgICdjaGFyc2V0JzogJ3V0ZjhtYjQnLFxcbiAgICAgICAgJ1RFU1QnOiB7XFxuICAgICAgICAgICAgJ0NIQVJTRVQnOiAndXRmOG1iNCcsXFxuICAgICAgICAgICAgJ0NPTExBVElPTic6ICd1dGY4bWI0X2dlbmVyYWxfY2knLFxcbiAgICAgICAgfSxcXG4gICAgICAgICdDT05OX01BWF9BR0UnOiA2MFxcbiAgICB9LFxcbn1cXG5cXG4jIEludGVybmF0aW9uYWxpemF0aW9uXFxuTEFOR1VBR0VfQ09ERSA9ICd6aC1IYW5zJ1xcblRJTUVfWk9ORSA9ICdBc2lhL1NoYW5naGFpJ1xcblVTRV9JMThOID0gVHJ1ZVxcblVTRV9MMTBOID0gVHJ1ZVxcblVTRV9UWiA9IEZhbHNlXFxuXFxuIyBTdGF0aWMgZmlsZXMgKENTUywgSmF2YVNjcmlwdCwgSW1hZ2VzKVxcblNUQVRJQ19VUkwgPSAnL2Fzc2V0cy8nXFxuU1RBVElDRklMRVNfRElSUyA9IFtcXG4gICAgb3MucGF0aC5qb2luKEJBU0VfRElSLCBcXFwidGVtcGxhdGVzL2Zyb250L2Fzc2V0c1xcXCIpLFxcbl1cXG5cXG4jIE1lZGlhIGZpbGVzXFxuTUVESUFfVVJMID0gXFxcIi9tZWRpYS9cXFwiXFxuTUVESUFfUk9PVCA9IG9zLnBhdGguam9pbihCQVNFX0RJUiwgJ21lZGlhJylcXG5pZiBub3Qgb3MucGF0aC5pc2RpcihNRURJQV9ST09UKTpcXG4gICAgb3MubWtkaXIoTUVESUFfUk9PVClcXG5cXG4jIFNjaGVtYSBuYW1lIGZvciB0aGUgYXBwbGljYXRpb25cXG5zY2hlbWFOYW1lID0gXFxcImRqYW5nb2syZ2c2MzczXFxcIlwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IjIuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIHNldHRpbmdzLnB5IGZpbGU6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFLckNXU2t1b1JyOGt0eXRleXhpWHQ2In19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjI3OjMxLjUwOVoiLCJyZXNwb25zZV90ZXh0IjoiMi4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgc2V0dGluZ3MucHkgZmlsZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI3NmVkMDY0MS01ZWY4LTQyNDItODVjOS0yZGUzYmFhNDY3YjUiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFDNXdGVVBndktGdHhIa0RVY3dZdGZpIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9kajIvd3NnaS5weVwiLCBcImZpbGVfY29udGVudFwiOiBcIlxcXCJcXFwiXFxcIlxcbldTR0kgY29uZmlnIGZvciBkajIgcHJvamVjdC5cXG5cXG5JdCBleHBvc2VzIHRoZSBXU0dJIGNhbGxhYmxlIGFzIGEgbW9kdWxlLWxldmVsIHZhcmlhYmxlIG5hbWVkIGBgYXBwbGljYXRpb25gYC5cXG5cXG5Gb3IgbW9yZSBpbmZvcm1hdGlvbiBvbiB0aGlzIGZpbGUsIHNlZVxcbmh0dHBzOi8vZG9jcy5kamFuZ29wcm9qZWN0LmNvbS9lbi8yLjAvaG93dG8vZGVwbG95bWVudC93c2dpL1xcblxcXCJcXFwiXFxcIlxcblxcbmltcG9ydCBvc1xcblxcbmZyb20gZGphbmdvLmNvcmUud3NnaSBpbXBvcnQgZ2V0X3dzZ2lfYXBwbGljYXRpb25cXG5cXG5vcy5lbnZpcm9uLnNldGRlZmF1bHQoXFxcIkRKQU5HT19TRVRUSU5HU19NT0RVTEVcXFwiLCBcXFwiZGoyLnNldHRpbmdzXFxcIilcXG5cXG5hcHBsaWNhdGlvbiA9IGdldF93c2dpX2FwcGxpY2F0aW9uKClcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIzLiBMZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgd3NnaS5weSBmaWxlOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVHZTVGpRRkZOOVdvazJZR0dLWFdjNiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyNzo1NS4yNzFaIiwicmVzcG9uc2VfdGV4dCI6IjMuIExldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB3c2dpLnB5IGZpbGU6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMzRjMzA4YjEtOWM5Yy00ZGU4LWFiYzItOTBhYTAyMjMxZjA3IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVkc1b1FNR1pyaFhKa1dFWDRCaGhuaiIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvZGoyL3ZpZXdzLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5pbXBvcnQgb3NcXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2UsIEh0dHBSZXNwb25zZVxcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcXG5cXG5kZWYgaW5kZXgocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBJbmRleCB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiAyMDAsIFxcXCJtc2dcXFwiOiBcXFwic3VjY2Vzc1xcXCIsIFxcXCJkYXRhXFxcIjogW119XFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5kZWYgdGVzdChyZXF1ZXN0LCBwMSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBUZXN0IHZpZXcgZnVuY3Rpb24gd2l0aCBwYXJhbWV0ZXJcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IDIwMCwgXFxcIm1zZ1xcXCI6IFxcXCJzdWNjZXNzXFxcIiwgXFxcImRhdGFcXFwiOiBbXX1cXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcbmRlZiBudWxsKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgTnVsbCB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiAyMDAsIFxcXCJtc2dcXFwiOiBcXFwic3VjY2Vzc1xcXCIsIFxcXCJkYXRhXFxcIjogW119XFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5kZWYgYWRtaW5fbGliMihyZXF1ZXN0LCBwMSwgcDIpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQWRtaW4gbGliMiB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvYWRtaW4vbGliXFxcIiwgcDEsIHAyKVxcbiAgICAgICAgaWYgb3MucGF0aC5pc2ZpbGUoZmlsZV9wYXRoKTpcXG4gICAgICAgICAgICB3aXRoIG9wZW4oZmlsZV9wYXRoLCAncmInKSBhcyBmOlxcbiAgICAgICAgICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKGYucmVhZCgpKVxcbiAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShcXFwiRmlsZSBub3QgZm91bmRcXFwiLCBzdGF0dXM9NDA0KVxcblxcbmRlZiBhZG1pbl9saWIzKHJlcXVlc3QsIHAxLCBwMiwgcDMpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQWRtaW4gbGliMyB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvYWRtaW4vbGliXFxcIiwgcDEsIHAyLCBwMylcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgYWRtaW5fbGliNChyZXF1ZXN0LCBwMSwgcDIsIHAzLCBwNCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBBZG1pbiBsaWI0IHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9hZG1pbi9saWJcXFwiLCBwMSwgcDIsIHAzLCBwNClcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgYWRtaW5fcGFnZShyZXF1ZXN0LCBwMSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBBZG1pbiBwYWdlIHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9hZG1pbi9wYWdlXFxcIiwgcDEpXFxuICAgICAgICBpZiBvcy5wYXRoLmlzZmlsZShmaWxlX3BhdGgpOlxcbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlX3BhdGgsICdyYicpIGFzIGY6XFxuICAgICAgICAgICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoZi5yZWFkKCkpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXFxuXFxuZGVmIGFkbWluX3BhZ2UyKHJlcXVlc3QsIHAxLCBwMik6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBBZG1pbiBwYWdlMiB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvYWRtaW4vcGFnZVxcXCIsIHAxLCBwMilcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgYWRtaW5fcGFnZXMocmVxdWVzdCwgcDEpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQWRtaW4gcGFnZXMgdmlldyBmdW5jdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJHRVRcXFwiLCBcXFwiUE9TVFxcXCJdOlxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL2FkbWluL3BhZ2VzXFxcIiwgcDEpXFxuICAgICAgICBpZiBvcy5wYXRoLmlzZmlsZShmaWxlX3BhdGgpOlxcbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlX3BhdGgsICdyYicpIGFzIGY6XFxuICAgICAgICAgICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoZi5yZWFkKCkpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXFxuXFxuZGVmIGFkbWluX3BhZ2VzMihyZXF1ZXN0LCBwMSwgcDIpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQWRtaW4gcGFnZXMyIHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9hZG1pbi9wYWdlc1xcXCIsIHAxLCBwMilcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgbGF5dWkxKHJlcXVlc3QsIHAxKTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIExheXVpMSB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvYWRtaW4vbGF5dWlcXFwiLCBwMSlcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgbGF5dWkyKHJlcXVlc3QsIHAxLCBwMik6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBMYXl1aTIgdmlldyBmdW5jdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJHRVRcXFwiLCBcXFwiUE9TVFxcXCJdOlxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL2FkbWluL2xheXVpXFxcIiwgcDEsIHAyKVxcbiAgICAgICAgaWYgb3MucGF0aC5pc2ZpbGUoZmlsZV9wYXRoKTpcXG4gICAgICAgICAgICB3aXRoIG9wZW4oZmlsZV9wYXRoLCAncmInKSBhcyBmOlxcbiAgICAgICAgICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKGYucmVhZCgpKVxcbiAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShcXFwiRmlsZSBub3QgZm91bmRcXFwiLCBzdGF0dXM9NDA0KVxcblxcbmRlZiBsYXl1aTMocmVxdWVzdCwgcDEsIHAyLCBwMyk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBMYXl1aTMgdmlldyBmdW5jdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJHRVRcXFwiLCBcXFwiUE9TVFxcXCJdOlxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL2FkbWluL2xheXVpXFxcIiwgcDEsIHAyLCBwMylcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgbGF5dWk0KHJlcXVlc3QsIHAxLCBwMiwgcDMsIHA0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIExheXVpNCB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvYWRtaW4vbGF5dWlcXFwiLCBwMSwgcDIsIHAzLCBwNClcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgZnJvbnRfcGFnZXMocmVxdWVzdCwgcDEpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgRnJvbnQgcGFnZXMgdmlldyBmdW5jdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJHRVRcXFwiLCBcXFwiUE9TVFxcXCJdOlxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL2Zyb250L3BhZ2VzXFxcIiwgcDEpXFxuICAgICAgICBpZiBvcy5wYXRoLmlzZmlsZShmaWxlX3BhdGgpOlxcbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlX3BhdGgsICdyYicpIGFzIGY6XFxuICAgICAgICAgICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoZi5yZWFkKCkpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXFxuXFxuZGVmIGZyb250X3BhZ2VzMihyZXF1ZXN0LCBwMSwgcDIpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgRnJvbnQgcGFnZXMyIHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9mcm9udC9wYWdlc1xcXCIsIHAxLCBwMilcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSlcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgZnJvbnRfbW9kdWxlcyhyZXF1ZXN0LCBwMSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBGcm9udCBtb2R1bGVzIHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9mcm9udC9tb2R1bGVzXFxcIiwgcDEpXFxuICAgICAgICBpZiBvcy5wYXRoLmlzZmlsZShmaWxlX3BhdGgpOlxcbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlX3BhdGgsICdyYicpIGFzIGY6XFxuICAgICAgICAgICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoZi5yZWFkKCkpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXFxuXFxuZGVmIGNzczEocmVxdWVzdCwgcDEpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQ1NTIHZpZXcgZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiR0VUXFxcIiwgXFxcIlBPU1RcXFwiXTpcXG4gICAgICAgIGZpbGVfcGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXFxcInRlbXBsYXRlcy9mcm9udC9jc3NcXFwiLCBwMSlcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSwgY29udGVudF90eXBlPVxcXCJ0ZXh0L2Nzc1xcXCIpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXFxuXFxuZGVmIGpzMShyZXF1ZXN0LCBwMSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBKUyB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvZnJvbnQvanNcXFwiLCBwMSlcXG4gICAgICAgIGlmIG9zLnBhdGguaXNmaWxlKGZpbGVfcGF0aCk6XFxuICAgICAgICAgICAgd2l0aCBvcGVuKGZpbGVfcGF0aCwgJ3JiJykgYXMgZjpcXG4gICAgICAgICAgICAgICAgcmV0dXJuIEh0dHBSZXNwb25zZShmLnJlYWQoKSwgY29udGVudF90eXBlPVxcXCJhcHBsaWNhdGlvbi9qYXZhc2NyaXB0XFxcIilcXG4gICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoXFxcIkZpbGUgbm90IGZvdW5kXFxcIiwgc3RhdHVzPTQwNClcXG5cXG5kZWYgaW1nMShyZXF1ZXN0LCBwMSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBJbWFnZSB2aWV3IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIkdFVFxcXCIsIFxcXCJQT1NUXFxcIl06XFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvZnJvbnQvaW1nXFxcIiwgcDEpXFxuICAgICAgICBpZiBvcy5wYXRoLmlzZmlsZShmaWxlX3BhdGgpOlxcbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlX3BhdGgsICdyYicpIGFzIGY6XFxuICAgICAgICAgICAgICAgIHJldHVybiBIdHRwUmVzcG9uc2UoZi5yZWFkKCkpXFxuICAgICAgICByZXR1cm4gSHR0cFJlc3BvbnNlKFxcXCJGaWxlIG5vdCBmb3VuZFxcXCIsIHN0YXR1cz00MDQpXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiNC4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgdmlld3MucHkgZmlsZSBmb3IgdGhlIGRqMiBhcHA6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFDNXdGVVBndktGdHhIa0RVY3dZdGZpIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjI4OjAzLjA5MloiLCJyZXNwb25zZV90ZXh0IjoiNC4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgdmlld3MucHkgZmlsZSBmb3IgdGhlIGRqMiBhcHA6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiZGE5ODYyZGQtYjFlMS00YWE2LTk3NGMtYTE0YjJjZWI3ZmZjIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxM2hidGZ5ZDZTWktYZGZzclJCSDl4NCIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvZGoyL3VybHMucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFxcbmZyb20gZGphbmdvLmNvbnRyaWIgaW1wb3J0IGFkbWluXFxuZnJvbSBkamFuZ28udXJscyBpbXBvcnQgcGF0aCwgaW5jbHVkZSwgcmVfcGF0aFxcbmZyb20gZGphbmdvLnZpZXdzLmdlbmVyaWMgaW1wb3J0IFRlbXBsYXRlVmlld1xcblxcbmZyb20gLiBpbXBvcnQgdmlld3NcXG5mcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgc2NoZW1hTmFtZVxcblxcbnVybHBhdHRlcm5zID0gW1xcbiAgICBwYXRoKCd4YWRtaW4vJywgYWRtaW4uc2l0ZS51cmxzKSxcXG4gICAgcGF0aChyJ2luZGV4LycsIHZpZXdzLmluZGV4KSxcXG4gICAgcGF0aCgne30vJy5mb3JtYXQoc2NoZW1hTmFtZSksIGluY2x1ZGUoJ21haW4udXJscycpKSwgICMgSW1wb3J0IHNjaGVtYU5hbWVcXG4gICAgXFxuICAgICMgQWRtaW4gc3RhdGljIGZpbGVzXFxuICAgIHJlX3BhdGgocidhZG1pbi9saWIvKD9QPHAxPi4qKS8oP1A8cDI+LiopJCcsIHZpZXdzLmFkbWluX2xpYjIpLFxcbiAgICByZV9wYXRoKHInYWRtaW4vbGliLyg/UDxwMT4uKikvKD9QPHAyPi4qKS8oP1A8cDM+LiopJCcsIHZpZXdzLmFkbWluX2xpYjMpLFxcbiAgICByZV9wYXRoKHInYWRtaW4vbGliLyg/UDxwMT4uKikvKD9QPHAyPi4qKS8oP1A8cDM+LiopLyg/UDxwND4uKikkJywgdmlld3MuYWRtaW5fbGliNCksXFxuICAgIHJlX3BhdGgocidhZG1pbi9wYWdlLyg/UDxwMT4uKikkJywgdmlld3MuYWRtaW5fcGFnZSksXFxuICAgIHJlX3BhdGgocidhZG1pbi9wYWdlLyg/UDxwMT4uKikvKD9QPHAyPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlMiksXFxuICAgIHJlX3BhdGgocidhZG1pbi9wYWdlcy8oP1A8cDE+LiopJCcsIHZpZXdzLmFkbWluX3BhZ2VzKSxcXG4gICAgcmVfcGF0aChyJ2FkbWluL3BhZ2VzLyg/UDxwMT4uKikvKD9QPHAyPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlczIpLFxcbiAgICBcXG4gICAgIyBMYXl1aSBzdGF0aWMgZmlsZXNcXG4gICAgcmVfcGF0aChyJ2xheXVpLyg/UDxwMT4uKikkJywgdmlld3MubGF5dWkxKSxcXG4gICAgcmVfcGF0aChyJ2xheXVpLyg/UDxwMT4uKikvKD9QPHAyPi4qKSQnLCB2aWV3cy5sYXl1aTIpLFxcbiAgICByZV9wYXRoKHInbGF5dWkvKD9QPHAxPi4qKS8oP1A8cDI+LiopLyg/UDxwMz4uKikkJywgdmlld3MubGF5dWkzKSxcXG4gICAgcmVfcGF0aChyJ2xheXVpLyg/UDxwMT4uKikvKD9QPHAyPi4qKS8oP1A8cDM+LiopLyg/UDxwND4uKikkJywgdmlld3MubGF5dWk0KSxcXG4gICAgXFxuICAgICMgRnJvbnQtZW5kIHN0YXRpYyBmaWxlc1xcbiAgICByZV9wYXRoKHIncGFnZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5mcm9udF9wYWdlcyksXFxuICAgIHJlX3BhdGgocidwYWdlcy8oP1A8cDE+LiopLyg/UDxwMj4uKikkJywgdmlld3MuZnJvbnRfcGFnZXMyKSxcXG4gICAgcmVfcGF0aChyJ21vZHVsZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5mcm9udF9tb2R1bGVzKSxcXG4gICAgcmVfcGF0aChyJ2Nzcy8oP1A8cDE+LiopJCcsIHZpZXdzLmNzczEpLFxcbiAgICByZV9wYXRoKHInanMvKD9QPHAxPi4qKSQnLCB2aWV3cy5qczEpLFxcbiAgICByZV9wYXRoKHInaW1nLyg/UDxwMT4uKikkJywgdmlld3MuaW1nMSksXFxuICAgIFxcbiAgICAjIFRlc3QgYW5kIG51bGwgcm91dGVzXFxuICAgIHBhdGgocid0ZXN0LzxzdHI6cDE+LycsIHZpZXdzLnRlc3QpLFxcbiAgICBwYXRoKHInbnVsbCcsIHZpZXdzLm51bGwpLFxcbl1cIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiI1LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB1cmxzLnB5IGZpbGUgZm9yIHRoZSBkajIgYXBwOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVkc1b1FNR1pyaFhKa1dFWDRCaGhuaiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyODozMy4yMDNaIiwicmVzcG9uc2VfdGV4dCI6IjUuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIHVybHMucHkgZmlsZSBmb3IgdGhlIGRqMiBhcHA6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiZWQ0YjU2MzctNjA5ZS00ZWZiLWFhYzUtOTk4MDVhM2EyYTZlIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxSlNMeGd6RDNGN01CdXNkWWptUFFqMiIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvbWFpbi9hcHBzLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBBcHBDb25maWdcXG5cXG5cXG5jbGFzcyBNYWluQ29uZmlnKEFwcENvbmZpZyk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBNYWluIGFwcGxpY2F0aW9uIGNvbmZpZ3VyYXRpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIG5hbWUgPSAnbWFpbidcXG4gICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9IHInXFx1NTIzN1xcdTg4NjhcXHU2YTIxXFx1NTc1NydcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiI2LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCBhcHBzLnB5IGZpbGUgZm9yIHRoZSBtYWluIGFwcDpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTNoYnRmeWQ2U1pLWGRmc3JSQkg5eDQifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6Mjg6NDcuNTIyWiIsInJlc3BvbnNlX3RleHQiOiI2LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCBhcHBzLnB5IGZpbGUgZm9yIHRoZSBtYWluIGFwcDpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI5NDA1MmYzZi03ZGI3LTRhNzgtYTk3MC0xNzZkNmQ1NzljYTYiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFHVnB4Y0RjMllaSmVYNDJKdnRRWjdCIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYWluL2FkbWluLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5mcm9tIGRqYW5nby5jb250cmliIGltcG9ydCBhZG1pblxcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcXG5cXG5mcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgc2NoZW1hTmFtZVxcbmZyb20gbWFpbi51c2Vyc19tb2RlbCBpbXBvcnQgdXNlcnNcXG5mcm9tIG1haW4uY29uZmlnX21vZGVsIGltcG9ydCBjb25maWdcXG5cXG50cnk6XFxuICAgIGZyb20gbWFpbi5tb2RlbHMgaW1wb3J0ICpcXG5leGNlcHQ6XFxuICAgIHBhc3NcXG5cXG4jIENoYW5nZSBhZG1pbiBzaXRlIHRpdGxlXFxuYWRtaW4uc2l0ZS5zaXRlX3RpdGxlID0gc2NoZW1hTmFtZSAgIyBTZXQgcGFnZSB0aXRsZVxcbmFkbWluLnNpdGUuc2l0ZV9oZWFkZXIgPSBzY2hlbWFOYW1lICAjIFNldCB3ZWJzaXRlIGhlYWRlclxcbmFkbWluLnNpdGUuaW5kZXhfdGl0bGUgPSBzY2hlbWFOYW1lICAjIFNldCBpbmRleCB0aXRsZVxcblxcbiMgUmVnaXN0ZXIgYWxsIG1vZGVscyBmcm9tIHRoZSBtYWluIGFwcFxcbmFsbE1vZGVscyA9IGFwcHMuZ2V0X2FwcF9jb25maWcoJ21haW4nKS5nZXRfbW9kZWxzKClcXG5cXG5mb3IgbW9kZWwgaW4gYWxsTW9kZWxzOlxcbiAgICBjbGFzcyBNb2RlbEFkbWluKGFkbWluLk1vZGVsQWRtaW4pOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBEeW5hbWljIE1vZGVsQWRtaW4gY2xhc3MgZm9yIGFsbCBtb2RlbHNcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgbGlzdF9kaXNwbGF5ID0gW11cXG4gICAgICAgIGZvciBjb2wgaW4gbW9kZWwuX21ldGEuZmllbGRzOlxcbiAgICAgICAgICAgIGxpc3RfZGlzcGxheS5hcHBlbmQoY29sLm5hbWUpXFxuXFxuICAgICAgICBzZWFyY2hfZmllbGRzID0gbGlzdF9kaXNwbGF5XFxuXFxuICAgIGFkbWluLnNpdGUucmVnaXN0ZXIobW9kZWwsIE1vZGVsQWRtaW4pXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiNy4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgYWRtaW4ucHkgZmlsZSBmb3IgdGhlIG1haW4gYXBwOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxSlNMeGd6RDNGN01CdXNkWWptUFFqMiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyODo1My4xOThaIiwicmVzcG9uc2VfdGV4dCI6IjcuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIGFkbWluLnB5IGZpbGUgZm9yIHRoZSBtYWluIGFwcDpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJmZGU5NDc1Ni05MjcyLTRiNGMtOGU2NS01MGU5NzlmZDBjMjkiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFFS0VieUJKTHRKcmRpQzR0VHpTRW92IiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYWluL21vZGVsLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5cXG5pbXBvcnQgY29weVxcbmltcG9ydCBsb2dnaW5nXFxuaW1wb3J0IGRhdGV0aW1lXFxuZnJvbSBkamFuZ28uZGIgaW1wb3J0IG1vZGVsc1xcbmZyb20gZGphbmdvLmZvcm1zLm1vZGVscyBpbXBvcnQgbW9kZWxfdG9fZGljdFxcbmZyb20gZGphbmdvLmNvcmUucGFnaW5hdG9yIGltcG9ydCBQYWdpbmF0b3JcXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFByb3RlY3RlZEVycm9yXFxuZnJvbSB0aHJlYWRsb2NhbHMudGhyZWFkbG9jYWxzIGltcG9ydCBnZXRfY3VycmVudF9yZXF1ZXN0XFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBTdW0sIE1heCwgTWluLCBBdmcsIENvdW50XFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXFxuXFxuXFxuY2xhc3MgQmFzZU1vZGVsKG1vZGVscy5Nb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBCYXNlIG1vZGVsIGNsYXNzIGZvciBhbGwgbW9kZWxzIGluIHRoZSBhcHBsaWNhdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGFic3RyYWN0ID0gVHJ1ZVxcblxcbiAgICBkZWYgX19SZXRyaWV2ZShzZWxmLCBtb2RlbCk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIFJldHJpZXZlIGFsbCByZWNvcmRzIGZyb20gdGhlIG1vZGVsXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIGRhdGFzID0gbW9kZWwub2JqZWN0cy5hbGwoKVxcbiAgICAgICAgcmV0dXJuIHNlbGYudG9fbGlzdChkYXRhcywgZGF0YXMsIG1vZGVsKVxcblxcbiAgICBkZWYgcmV0cmlldmUoc2VsZiwgbW9kZWwpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBSZXRyaWV2ZSBhbGwgcmVjb3JkcyBmcm9tIHRoZSBtb2RlbCBhbmQgZm9ybWF0IGRhdGV0aW1lIGZpZWxkc1xcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBkYXRhcyA9IHNlbGYuX19SZXRyaWV2ZShtb2RlbCwgbW9kZWwpXFxuICAgICAgICBmb3IgaSBpbiBkYXRhczpcXG4gICAgICAgICAgICBhZGR0aW1lID0gaS5nZXQoXFxcImFkZHRpbWVcXFwiKVxcbiAgICAgICAgICAgIGlmIGFkZHRpbWU6XFxuICAgICAgICAgICAgICAgIGFkZHRpbWUgPSBzdHIoYWRkdGltZSlbOjE5XS5yZXBsYWNlKFxcXCJUXFxcIiwgXFxcIiBcXFwiKVxcbiAgICAgICAgICAgICAgICBpW1xcXCJhZGR0aW1lXFxcIl0gPSBhZGR0aW1lXFxuICAgICAgICByZXR1cm4gZGF0YXNcXG5cXG4gICAgZGVmIHRvX2xpc3Qoc2VsZiwgZGF0YXMsIGRhdGFzXywgbW9kZWwpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBDb252ZXJ0IFF1ZXJ5U2V0IHRvIGxpc3Qgb2YgZGljdGlvbmFyaWVzXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHJlc3VsdCA9IFtdXFxuICAgICAgICBmb3IgZGF0YSBpbiBkYXRhc186XFxuICAgICAgICAgICAgaWYgaXNpbnN0YW5jZShkYXRhLCBkaWN0KTpcXG4gICAgICAgICAgICAgICAgcmVzdWx0LmFwcGVuZChkYXRhKVxcbiAgICAgICAgICAgIGVsc2U6XFxuICAgICAgICAgICAgICAgIHJlc3VsdC5hcHBlbmQobW9kZWxfdG9fZGljdChkYXRhKSlcXG4gICAgICAgIHJldHVybiByZXN1bHRcXG5cXG4gICAgZGVmIGdldGFsbGNvbHVtbihzZWxmLCBtb2RlbCk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIEdldCBhbGwgY29sdW1uIG5hbWVzIG9mIHRoZSBtb2RlbFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBjb2x1bW5fbGlzdCA9IFtdXFxuICAgICAgICBmb3IgY29sIGluIG1vZGVsLl9tZXRhLmZpZWxkczpcXG4gICAgICAgICAgICBjb2x1bW5fbGlzdC5hcHBlbmQoY29sLm5hbWUpXFxuICAgICAgICByZXR1cm4gY29sdW1uX2xpc3RcXG5cXG4gICAgZGVmIF9fUGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0PXt9LCBxPVEoKSk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIFBhZ2luYXRlIHJlY29yZHMgZnJvbSB0aGUgbW9kZWxcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgaWYgbm90IHBhcmFtczpcXG4gICAgICAgICAgICBwYXJhbXMgPSB7fVxcblxcbiAgICAgICAgIyBHZXQgcGFnZSBwYXJhbWV0ZXJzXFxuICAgICAgICBwYWdlID0gaW50KHBhcmFtcy5nZXQoJ3BhZ2UnLCAxKSlcXG4gICAgICAgIGxpbWl0ID0gaW50KHBhcmFtcy5nZXQoJ2xpbWl0JywgMTApKVxcbiAgICAgICAgc29ydCA9IHBhcmFtcy5nZXQoJ3NvcnQnLCAnaWQnKVxcbiAgICAgICAgXFxuICAgICAgICAjIEhhbmRsZSBvcmRlcmluZ1xcbiAgICAgICAgb3JkZXIgPSBwYXJhbXMuZ2V0KCdvcmRlcicsICdhc2MnKVxcbiAgICAgICAgaWYgc29ydCBhbmQgc29ydCAhPSAnbnVsbCc6XFxuICAgICAgICAgICAgaWYgb3JkZXIgPT0gJ2Rlc2MnOlxcbiAgICAgICAgICAgICAgICBzb3J0ID0gJy0nICsgc29ydFxcbiAgICAgICAgZWxzZTpcXG4gICAgICAgICAgICBzb3J0ID0gJ2lkJ1xcblxcbiAgICAgICAgIyBGaWx0ZXIgcGFyYW1ldGVyc1xcbiAgICAgICAgbmV3X3BhcmFtcyA9IHt9XFxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcXG4gICAgICAgICAgICBpZiBrIG5vdCBpbiBbJ3BhZ2UnLCAnbGltaXQnLCAnc29ydCcsICdvcmRlcicsICdpZHMnLCAndG9rZW4nLCAncmFuZG9tJywgJ25ldycsICd1c2VyaWQnLCAndXNlcm5hbWUnXTpcXG4gICAgICAgICAgICAgICAgbmV3X3BhcmFtc1trXSA9IHZcXG5cXG4gICAgICAgICMgQXBwbHkgZmlsdGVyc1xcbiAgICAgICAgZGF0YSA9IG1vZGVsLm9iamVjdHMuZmlsdGVyKCoqbmV3X3BhcmFtcykuZmlsdGVyKHEpLm9yZGVyX2J5KHNvcnQpXFxuICAgICAgICBcXG4gICAgICAgICMgUGFnaW5hdGUgcmVzdWx0c1xcbiAgICAgICAgcGFnaW5hdG9yID0gUGFnaW5hdG9yKGRhdGEsIGxpbWl0KVxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBwYWdlIGRhdGFcXG4gICAgICAgIHRyeTpcXG4gICAgICAgICAgICBkYXRhX3BhZ2UgPSBwYWdpbmF0b3IucGFnZShwYWdlKVxcbiAgICAgICAgZXhjZXB0OlxcbiAgICAgICAgICAgIGRhdGFfcGFnZSA9IHBhZ2luYXRvci5wYWdlKHBhZ2luYXRvci5udW1fcGFnZXMpXFxuXFxuICAgICAgICAjIENvbnZlcnQgdG8gbGlzdFxcbiAgICAgICAgbmV3RGF0YSA9IHNlbGYudG9fbGlzdChkYXRhLCBkYXRhX3BhZ2Uub2JqZWN0X2xpc3QsIG1vZGVsKVxcbiAgICAgICAgXFxuICAgICAgICAjIEZvcm1hdCBkYXRldGltZSBmaWVsZHNcXG4gICAgICAgIGZpbGVkX2xpc3QgPSBbXVxcbiAgICAgICAgZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xcbiAgICAgICAgbW9kZWxvYmogPSBhcHBzLmdldF9tb2RlbCgnbWFpbicsIG1vZGVsLl9fdGFibGVuYW1lX18pXFxuICAgICAgICBmb3IgZmllbGQgaW4gbW9kZWxvYmouX21ldGEuZmllbGRzOlxcbiAgICAgICAgICAgIGlmICdEYXRlVGltZUZpZWxkJyBpbiB0eXBlKGZpZWxkKS5fX25hbWVfXzpcXG4gICAgICAgICAgICAgICAgZmlsZWRfbGlzdC5hcHBlbmQoZmllbGQubmFtZSlcXG5cXG4gICAgICAgIGZvciBpbmRleCwgaSBpbiBlbnVtZXJhdGUobmV3RGF0YSk6XFxuICAgICAgICAgICAgZm9yIGssIHYgaW4gaS5pdGVtcygpOlxcbiAgICAgICAgICAgICAgICBpZiBrIGluIGZpbGVkX2xpc3Q6XFxuICAgICAgICAgICAgICAgICAgICBuZXdEYXRhW2luZGV4XVtrXSA9IHN0cih2KVs6MTldXFxuXFxuICAgICAgICByZXR1cm4gbmV3RGF0YSwgcGFnZSwgcGFnaW5hdG9yLm51bV9wYWdlcywgcGFnaW5hdG9yLmNvdW50LCBsaW1pdFxcblxcbiAgICBkZWYgcGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0PXt9LCBxPVEoKSk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIFBhZ2luYXRlIHJlY29yZHMgZnJvbSB0aGUgbW9kZWxcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgcmV0dXJuIHNlbGYuX19QYWdlKHNlbGYsIG1vZGVsLCBwYXJhbXMsIHJlcXVlc3QsIHEpXFxuXFxuICAgIGRlZiBfX0dldEJ5Q29sdW1uKHNlbGYsIG1vZGVsLCBjb2x1bW5OYW1lLCBuZXdfcGFyYW1zKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgR2V0IGRpc3RpbmN0IHZhbHVlcyBvZiBhIGNvbHVtbiBmcm9tIHRoZSBtb2RlbFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBkYXRhcyA9IG1vZGVsLm9iamVjdHMudmFsdWVzKGNvbHVtbk5hbWUpLmZpbHRlcigqKm5ld19wYXJhbXMpLmFsbCgpXFxuICAgICAgICBkYXRhX3NldCA9IHNldCgpXFxuICAgICAgICBmb3IgaSBpbiBkYXRhczpcXG4gICAgICAgICAgICBkYXRhX3NldC5hZGQoaS5nZXQoY29sdW1uTmFtZSkpXFxuICAgICAgICByZXR1cm4gbGlzdChkYXRhX3NldClcXG5cXG4gICAgZGVmIGdldGJ5Y29sdW1uKHNlbGYsIG1vZGVsLCBjb2x1bW5OYW1lLCBwYXJhbXM9e30pOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBHZXQgZGlzdGluY3QgdmFsdWVzIG9mIGEgY29sdW1uIGZyb20gdGhlIG1vZGVsXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlDb2x1bW4obW9kZWwsIG1vZGVsLCBjb2x1bW5OYW1lLCBwYXJhbXMpXFxuXFxuICAgIGRlZiBfX0NyZWF0ZUJ5UmVxKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBDcmVhdGUgYSBuZXcgcmVjb3JkIGluIHRoZSBtb2RlbFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBjb2x1bW5fbGlzdCA9IFtdXFxuICAgICAgICBmb3IgY29sIGluIG1vZGVsLl9tZXRhLmZpZWxkczpcXG4gICAgICAgICAgICBjb2x1bW5fbGlzdC5hcHBlbmQoY29sLm5hbWUpXFxuICAgICAgICBcXG4gICAgICAgIHBhcmFtc3MgPSB7fVxcbiAgICAgICAgZm9yIGssIHYgaW4gcGFyYW1zLml0ZW1zKCk6XFxuICAgICAgICAgICAgaWYgayBpbiBjb2x1bW5fbGlzdDpcXG4gICAgICAgICAgICAgICAgcGFyYW1zc1trXSA9IHZcXG4gICAgICAgIFxcbiAgICAgICAgcGFyYW1zc1tcXFwiYWRkdGltZVxcXCJdID0gZGF0ZXRpbWUuZGF0ZXRpbWUubm93KClcXG4gICAgICAgIG0gPSBtb2RlbCgqKnBhcmFtc3MpXFxuICAgICAgICBcXG4gICAgICAgIHRyeTpcXG4gICAgICAgICAgICByZXQgPSBtLnNhdmUoKVxcbiAgICAgICAgICAgIHJldHVybiBtLmlkXFxuICAgICAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6XFxuICAgICAgICAgICAgcmV0dXJuIFxcXCJ7fTp7fVxcXCIuZm9ybWF0KEV4Y2VwdGlvbiwgZSlcXG5cXG4gICAgZGVmIGNyZWF0ZWJ5cmVxKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBDcmVhdGUgYSBuZXcgcmVjb3JkIGluIHRoZSBtb2RlbFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICByZXR1cm4gc2VsZi5fX0NyZWF0ZUJ5UmVxKG1vZGVsLCBtb2RlbCwgcGFyYW1zKVxcblxcbiAgICBkZWYgX19HZXRCeUlkKHNlbGYsIG1vZGVsLCBpZCk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIEdldCBhIHJlY29yZCBieSBpZFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBkYXRhID0gbW9kZWwub2JqZWN0cy5maWx0ZXIoaWQ9aWQpLmFsbCgpXFxuICAgICAgICByZXR1cm4gc2VsZi50b19saXN0KG1vZGVsLCBkYXRhLCBtb2RlbClcXG5cXG4gICAgZGVmIGdldGJ5aWQoc2VsZiwgbW9kZWwsIGlkKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgR2V0IGEgcmVjb3JkIGJ5IGlkXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlJZChtb2RlbCwgbW9kZWwsIGlkKVxcblxcbiAgICBkZWYgX19HZXRCeVBhcmFtcyhzZWxmLCBtb2RlbCwgcGFyYW1zKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgR2V0IHJlY29yZHMgYnkgcGFyYW1ldGVyc1xcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICB0cnk6XFxuICAgICAgICAgICAgX19sb2dpblVzZXJfXyA9IG1vZGVsLl9fbG9naW5Vc2VyX19cXG4gICAgICAgIGV4Y2VwdDpcXG4gICAgICAgICAgICBfX2xvZ2luVXNlcl9fID0gTm9uZVxcblxcbiAgICAgICAgaWYgX19sb2dpblVzZXJfXyBpcyBub3QgTm9uZSBhbmQgX19sb2dpblVzZXJfXyAhPSAndXNlcm5hbWUnOlxcbiAgICAgICAgICAgIGlmIHBhcmFtcy5nZXQoJ3VzZXJuYW1lJyk6XFxuICAgICAgICAgICAgICAgIHBhcmFtc1ttb2RlbC5fX2xvZ2luVXNlcl9fXSA9IGNvcHkuZGVlcGNvcHkocGFyYW1zLmdldCgndXNlcm5hbWUnKSlcXG4gICAgICAgICAgICAgICAgZGVsIHBhcmFtc1sndXNlcm5hbWUnXVxcbiAgICAgICAgXFxuICAgICAgICBpZiBtb2RlbC5fX3RhYmxlbmFtZV9fICE9ICd1c2Vycyc6XFxuICAgICAgICAgICAgaWYgcGFyYW1zLmdldCgncGFzc3dvcmQnKTpcXG4gICAgICAgICAgICAgICAgcGFyYW1zWydtaW1hJ10gPSBjb3B5LmRlZXBjb3B5KHBhcmFtcy5nZXQoJ3Bhc3N3b3JkJykpXFxuXFxuICAgICAgICAjIEZpbHRlciBwYXJhbWV0ZXJzXFxuICAgICAgICBwYXJhbXNzID0ge31cXG4gICAgICAgIGNvbHVtbkxpc3QgPSBzZWxmLmdldGFsbGNvbHVtbihtb2RlbCwgbW9kZWwpXFxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcXG4gICAgICAgICAgICBpZiBrIGluIGNvbHVtbkxpc3Q6XFxuICAgICAgICAgICAgICAgIHBhcmFtc3Nba10gPSB2XFxuXFxuICAgICAgICBkYXRhc18gPSBtb2RlbC5vYmplY3RzLmZpbHRlcigqKnBhcmFtc3MpLmFsbCgpXFxuICAgICAgICByZXR1cm4gc2VsZi50b19saXN0KGRhdGFzXywgZGF0YXNfLCBtb2RlbClcXG5cXG4gICAgZGVmIGdldGJ5cGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBHZXQgcmVjb3JkcyBieSBwYXJhbWV0ZXJzXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlQYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMpXFxuXFxuICAgIGRlZiBfX1VwZGF0ZUJ5UGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBVcGRhdGUgYSByZWNvcmQgYnkgcGFyYW1ldGVyc1xcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICB0cnk6XFxuICAgICAgICAgICAgaWQgPSBpbnQocGFyYW1zWydpZCddKVxcbiAgICAgICAgZXhjZXB0OlxcbiAgICAgICAgICAgIHJldHVybiBcXFwiTm8gaWRcXFwiXFxuXFxuICAgICAgICAjIEdldCB0aGUgcmVjb3JkXFxuICAgICAgICBkYXRhID0gbW9kZWwub2JqZWN0cy5nZXQoaWQ9aWQpXFxuICAgICAgICBpZiBub3QgZGF0YTpcXG4gICAgICAgICAgICByZXR1cm4gXFxcIk5vIGRhdGFcXFwiXFxuXFxuICAgICAgICAjIFVwZGF0ZSBmaWVsZHNcXG4gICAgICAgIGZvciBrLCB2IGluIHBhcmFtcy5pdGVtcygpOlxcbiAgICAgICAgICAgIGlmIGsgIT0gJ2lkJzpcXG4gICAgICAgICAgICAgICAgc2V0YXR0cihkYXRhLCBrLCB2KVxcbiAgICAgICAgXFxuICAgICAgICB0cnk6XFxuICAgICAgICAgICAgZGF0YS5zYXZlKClcXG4gICAgICAgICAgICByZXR1cm4gTm9uZVxcbiAgICAgICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOlxcbiAgICAgICAgICAgIHJldHVybiBcXFwie306e31cXFwiLmZvcm1hdChFeGNlcHRpb24sIGUpXFxuXFxuICAgIGRlZiB1cGRhdGVieXBhcmFtcyhzZWxmLCBtb2RlbCwgcGFyYW1zKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgVXBkYXRlIGEgcmVjb3JkIGJ5IHBhcmFtZXRlcnNcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgcmV0dXJuIHNlbGYuX19VcGRhdGVCeVBhcmFtcyhtb2RlbCwgbW9kZWwsIHBhcmFtcylcXG5cXG4gICAgZGVmIF9fRGVsZXRlcyhzZWxmLCBtb2RlbCwgaWRzKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgRGVsZXRlIHJlY29yZHMgYnkgaWRzXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIGlmIG5vdCBpZHM6XFxuICAgICAgICAgICAgcmV0dXJuIFxcXCJObyBpZFxcXCJcXG5cXG4gICAgICAgIGlmIGlzaW5zdGFuY2UoaWRzLCBzdHIpOlxcbiAgICAgICAgICAgIGlkcyA9IGlkcy5zcGxpdCgnLCcpXFxuICAgICAgICBcXG4gICAgICAgIHRyeTpcXG4gICAgICAgICAgICBtb2RlbC5vYmplY3RzLmZpbHRlcihpZF9faW49aWRzKS5kZWxldGUoKVxcbiAgICAgICAgICAgIHJldHVybiBOb25lXFxuICAgICAgICBleGNlcHQgUHJvdGVjdGVkRXJyb3IgYXMgZTpcXG4gICAgICAgICAgICByZXR1cm4gXFxcInt9Ont9XFxcIi5mb3JtYXQoUHJvdGVjdGVkRXJyb3IsIGUpXFxuICAgICAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6XFxuICAgICAgICAgICAgcmV0dXJuIFxcXCJ7fTp7fVxcXCIuZm9ybWF0KEV4Y2VwdGlvbiwgZSlcXG5cXG4gICAgZGVmIGRlbGV0ZXMoc2VsZiwgbW9kZWwsIGlkcyk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIERlbGV0ZSByZWNvcmRzIGJ5IGlkc1xcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICByZXR1cm4gc2VsZi5fX0RlbGV0ZXMobW9kZWwsIG1vZGVsLCBpZHMpXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiOC4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgYmFzZSBtb2RlbCBmaWxlOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxR1ZweGNEYzJZWkplWDQySnZ0UVo3QiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyOTowMi4zMTdaIiwicmVzcG9uc2VfdGV4dCI6IjguIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIGJhc2UgbW9kZWwgZmlsZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI4ZWIxZDA5Zi05MDZiLTQ1N2QtOGE4Zi02NzIxOTM5M2E1YjciLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDE2a3dlNFBvU0QyWEFFQkdaNnE5cG5vIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYWluL3VzZXJzX21vZGVsLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5cXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgbW9kZWxzXFxuXFxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxcblxcblxcbmNsYXNzIHVzZXJzKEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBVc2VycyBtb2RlbCBmb3IgYWRtaW5pc3RyYXRvcnNcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIHVzZXJuYW1lID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUnXFx1OGQyNlxcdTUzZjcnKVxcbiAgICBwYXNzd29yZCA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J1xcdTViYzZcXHU3ODAxJylcXG4gICAgcm9sZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J1xcdTg5ZDJcXHU4MjcyJylcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIGltYWdlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUnXFx1NTkzNFxcdTUwY2YnKVxcbiAgICBcXG4gICAgX19pc0FkbWluX18gPSAnXFx1NjYyZidcXG4gICAgX190YWJsZW5hbWVfXyA9ICd1c2VycydcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ3VzZXJzJ1xcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9IHUnXFx1N2JhMVxcdTc0MDZcXHU1NDU4XFx1ODg2OCdcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiI5LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB1c2Vyc19tb2RlbC5weSBmaWxlOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRUtFYnlCSkx0SnJkaUM0dFR6U0VvdiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjoyOTozNy4yMzFaIiwicmVzcG9uc2VfdGV4dCI6IjkuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIHVzZXJzX21vZGVsLnB5IGZpbGU6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiOGM0NjUxMjMtMDkyZi00MTZiLWI1NzctNjBjMjgxZDQ3MjAzIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxTjFwbjJyWkpqYmRLb2lFYzFXSFZNVyIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvbWFpbi9jb25maWdfbW9kZWwucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFxcblxcbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcXG5cXG5mcm9tIC5tb2RlbCBpbXBvcnQgQmFzZU1vZGVsXFxuXFxuXFxuY2xhc3MgY29uZmlnKEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBDb25maWd1cmF0aW9uIG1vZGVsXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBuYW1lID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUnXFx1OTUyZVxcdTU0MGQnKVxcbiAgICB2YWx1ZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J1xcdTk1MmVcXHU1MDNjJylcXG4gICAgdXJsID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUnXFx1OTUyZVxcdTUwM2MnKVxcblxcbiAgICBfX3RhYmxlbmFtZV9fID0gJ2NvbmZpZydcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ2NvbmZpZydcXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J1xcdTkxNGRcXHU3ZjZlXFx1ODg2OCdcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIxMC4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgY29uZmlnX21vZGVsLnB5IGZpbGU6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDE2a3dlNFBvU0QyWEFFQkdaNnE5cG5vIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjI5OjQ1Ljk5MFoiLCJyZXNwb25zZV90ZXh0IjoiMTAuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIGNvbmZpZ19tb2RlbC5weSBmaWxlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6ImU5MWUzZjc2LTc1ZTItNDZjNC1hNTBhLTFiNDJjZDkwMTE2ZiIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFoUmY5OThRVjh3NW1xV2VDcWNCVjYiLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L21haW4vbW9kZWxzLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgbW9kZWxzXFxuXFxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxcblxcbmZyb20gZGF0ZXRpbWUgaW1wb3J0IGRhdGV0aW1lXFxuXFxuXFxuY2xhc3MgeW9uZ2h1KEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU3NTI4XFx1NjIzN1xcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ3lvbmdodScnJ1xcbiAgICBfX3RhYmxlbmFtZV9fID0gJ3lvbmdodSdcXG4gICAgX19sb2dpblVzZXJfXyA9ICd5b25naHVtaW5nJ1xcbiAgICBfX2ZvcmVFbmRMaXN0X18gPSAnXFx1NTQyNicgICMgXFx1ODg2OFxcdTVjNWVcXHU2MDI3W2ZvcmVFbmRMaXN0XVxcdTUyNGRcXHU1M2YwbGlzdDpcXHU1NDhjXFx1NTQwZVxcdTUzZjBcXHU5ZWQ4XFx1OGJhNFxcdTc2ODRsaXN0XFx1NTIxN1xcdTg4NjhcXHU5ODc1XFx1NzZmOFxcdTRmM2MsXFx1NTNlYVxcdTY2MmZcXHU2NDQ2XFx1NTcyOFxcdTUyNGRcXHU1M2YwLFxcdTU0MjY6XFx1NjMwN1xcdTZjYTFcXHU2NzA5XFx1NmI2NFxcdTk4NzUsXFx1NjYyZjpcXHU4ODY4XFx1NzkzYVxcdTY3MDlcXHU2YjY0XFx1OTg3NShcXHU0ZTBkXFx1OTcwMFxcdTg5ODFcXHU3NjdiXFx1OTY0NlxcdTUzNzNcXHU1M2VmXFx1NjdlNVxcdTc3MGIpLFxcdTUyNGRcXHU4OTgxXFx1NzY3YjpcXHU4ODY4XFx1NzkzYVxcdTY3MDlcXHU2YjY0XFx1OTg3NVxcdTRlMTRcXHU5NzAwXFx1ODk4MVxcdTc2N2JcXHU5NjQ2XFx1NTQwZVxcdTYyNGRcXHU4MGZkXFx1NjdlNVxcdTc3MGJcXG4gICAgX19pc0FkbWluX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OFxcdTVjNWVcXHU2MDI3aXNBZG1pbj1cXFwiXFx1NjYyZlxcXCIsXFx1NTIzN1xcdTUxZmFcXHU2NzY1XFx1NzY4NFxcdTc1MjhcXHU2MjM3XFx1ODg2OFxcdTRlNWZcXHU2NjJmXFx1N2JhMVxcdTc0MDZcXHU1NDU4XFx1ZmYwY1xcdTUzNzNwYWdlXFx1NTQ4Y2xpc3RcXHU1M2VmXFx1NGVlNVxcdTY3ZTVcXHU3NzBiXFx1NjI0MFxcdTY3MDlcXHU0ZWJhXFx1NzY4NFxcdTgwMDNcXHU4YmQ1XFx1OGJiMFxcdTVmNTUoXFx1NTQwY1xcdTY1ZjZcXHU1ZTk0XFx1NzUyOFxcdTRlOGVcXHU1MTc2XFx1NGVkNlxcdTg4NjgpXFxuICAgIFxcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSdcXHU1MjFiXFx1NWVmYVxcdTY1ZjZcXHU5NWY0JylcXG4gICAgeW9uZ2h1bWluZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J1xcdTc1MjhcXHU2MjM3XFx1NTQwZCcpXFxuICAgIG1pbWEgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NWJjNlxcdTc4MDEnKVxcbiAgICB4aW5nbWluZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1OWQzXFx1NTQwZCcpXFxuICAgIHhpbmdiaWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2MDI3XFx1NTIyYicpXFxuICAgIHRvdXhpYW5nID0gbW9kZWxzLlRleHRGaWVsZChudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1OTM0XFx1NTBjZicpXFxuICAgIG5pYW5saW5nID0gbW9kZWxzLkludGVnZXJGaWVsZChudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1ZTc0XFx1OWY4NCcpXFxuICAgIHlvdXhpYW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTkwYWVcXHU3YmIxJylcXG4gICAgc2hvdWppID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTYyNGJcXHU2NzNhJylcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ3lvbmdodSdcXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSAnXFx1NzUyOFxcdTYyMzcnXFxuXFxuXFxuY2xhc3Mgbm9uZ2h1KEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU1MTljXFx1NjIzN1xcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ25vbmdodScnJ1xcbiAgICBfX3RhYmxlbmFtZV9fID0gJ25vbmdodSdcXG4gICAgX19sb2dpblVzZXJfXyA9ICd6aGFuZ2hhbydcXG4gICAgX19mb3JlRW5kTGlzdF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN1tmb3JlRW5kTGlzdF1cXHU1MjRkXFx1NTNmMGxpc3Q6XFx1NTQ4Y1xcdTU0MGVcXHU1M2YwXFx1OWVkOFxcdThiYTRcXHU3Njg0bGlzdFxcdTUyMTdcXHU4ODY4XFx1OTg3NVxcdTc2ZjhcXHU0ZjNjLFxcdTUzZWFcXHU2NjJmXFx1NjQ0NlxcdTU3MjhcXHU1MjRkXFx1NTNmMCxcXHU1NDI2OlxcdTYzMDdcXHU2Y2ExXFx1NjcwOVxcdTZiNjRcXHU5ODc1LFxcdTY2MmY6XFx1ODg2OFxcdTc5M2FcXHU2NzA5XFx1NmI2NFxcdTk4NzUoXFx1NGUwZFxcdTk3MDBcXHU4OTgxXFx1NzY3YlxcdTk2NDZcXHU1MzczXFx1NTNlZlxcdTY3ZTVcXHU3NzBiKSxcXHU1MjRkXFx1ODk4MVxcdTc2N2I6XFx1ODg2OFxcdTc5M2FcXHU2NzA5XFx1NmI2NFxcdTk4NzVcXHU0ZTE0XFx1OTcwMFxcdTg5ODFcXHU3NjdiXFx1OTY0NlxcdTU0MGVcXHU2MjRkXFx1ODBmZFxcdTY3ZTVcXHU3NzBiXFxuICAgIF9faXNBZG1pbl9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN2lzQWRtaW49XFxcIlxcdTY2MmZcXFwiLFxcdTUyMzdcXHU1MWZhXFx1Njc2NVxcdTc2ODRcXHU3NTI4XFx1NjIzN1xcdTg4NjhcXHU0ZTVmXFx1NjYyZlxcdTdiYTFcXHU3NDA2XFx1NTQ1OFxcdWZmMGNcXHU1MzczcGFnZVxcdTU0OGNsaXN0XFx1NTNlZlxcdTRlZTVcXHU2N2U1XFx1NzcwYlxcdTYyNDBcXHU2NzA5XFx1NGViYVxcdTc2ODRcXHU4MDAzXFx1OGJkNVxcdThiYjBcXHU1ZjU1KFxcdTU0MGNcXHU2NWY2XFx1NWU5NFxcdTc1MjhcXHU0ZThlXFx1NTE3NlxcdTRlZDZcXHU4ODY4KVxcbiAgICBcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIHpoYW5naGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0nXFx1OGQyNlxcdTUzZjcnKVxcbiAgICBtaW1hID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTViYzZcXHU3ODAxJylcXG4gICAgbm9uZ2h1eGluZ21pbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE5Y1xcdTYyMzdcXHU1OWQzXFx1NTQwZCcpXFxuICAgIHRvdXhpYW5nID0gbW9kZWxzLlRleHRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTkzNFxcdTUwY2YnKVxcbiAgICB4aW5nYmllID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTYwMjdcXHU1MjJiJylcXG4gICAgbmlhbmxpbmcgPSBtb2RlbHMuSW50ZWdlckZpZWxkKG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTVlNzRcXHU5Zjg0JylcXG4gICAgeW91eGlhbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1OTBhZVxcdTdiYjEnKVxcbiAgICBkaWFuaHVhID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTc1MzVcXHU4YmRkJylcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ25vbmdodSdcXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSAnXFx1NTE5Y1xcdTYyMzcnXFxuXFxuXFxuY2xhc3MgZ3VveXVhbnhpbnhpKEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU2NzljXFx1NTZlZFxcdTRmZTFcXHU2MDZmXFx1NmEyMVxcdTU3OGJcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIF9fZG9jX18gPSB1JycnZ3VveXVhbnhpbnhpJycnXFxuICAgIF9fdGFibGVuYW1lX18gPSAnZ3VveXVhbnhpbnhpJ1xcbiAgICBfX2F1dGhUYWJsZXNfXyA9IHsnemhhbmdoYW8nOiAnbm9uZ2h1J31cXG4gICAgX19hdXRoUGVvcGxlX18gPSAnXFx1NTQyNicgICMgXFx1NzUyOFxcdTYyMzdcXHU4ODY4XFx1ZmYwY1xcdTg4NjhcXHU1YzVlXFx1NjAyN2xvZ2luVXNlckNvbHVtblxcdTViZjlcXHU1ZTk0XFx1NzY4NFxcdTUwM2NcXHU1YzMxXFx1NjYyZlxcdTc1MjhcXHU2MjM3XFx1NTQwZFxcdTViNTdcXHU2YmI1XFx1ZmYwY21pbWFcXHU1YzMxXFx1NjYyZlxcdTViYzZcXHU3ODAxXFx1NWI1N1xcdTZiYjVcXG4gICAgX19zZnNoX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OHNmc2goXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcdWZmMGNcXFwiXFx1NjYyZlxcXCJcXHU2MjE2XFxcIlxcdTU0MjZcXFwiKVxcdTViNTdcXHU2YmI1XFx1NTQ4Y3NmaGYoXFx1NWJhMVxcdTY4MzhcXHU1NmRlXFx1NTkwZClcXHU1YjU3XFx1NmJiNVxcdWZmMGNcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4KHBhZ2UpXFx1NzY4NFxcdTY0Y2RcXHU0ZjVjXFx1NGUyZFxcdTg5ODFcXHU1OTFhXFx1NGUwMFxcdTRlMmFcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NjMwOVxcdTk0YWVcXHVmZjBjXFx1NzBiOVxcdTUxZmJcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NWYzOVxcdTUxZmFcXHU0ZTAwXFx1NGUyYVxcdTk4NzVcXHU5NzYyXFx1ZmYwY1xcdTUzMDVcXHU1NDJiXFxcIlxcdTY2MmZcXHU1NDI2XFx1NWJhMVxcdTY4MzhcXFwiXFx1NTQ4Y1xcXCJcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkXFxcIlxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTc4NmVcXHU1YjlhXFx1OGMwM1xcdTc1Mjh1cGRhdGVcXHU2M2E1XFx1NTNlM1xcdWZmMGNcXHU0ZmVlXFx1NjUzOXNmc2hcXHU1NDhjc2ZoZlxcdTRlMjRcXHU0ZTJhXFx1NWI1N1xcdTZiYjVcXHUzMDAyXFxuICAgIF9fYXV0aFNlcGFyYXRlX18gPSAnXFx1NTQyNicgICMgXFx1NTQwZVxcdTUzZjBcXHU1MjE3XFx1ODg2OFxcdTY3NDNcXHU5NjUwXFxuICAgIF9fdGh1bWJzVXBfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjd0aHVtYnNVcFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTY1YjBcXHU1ODlldGh1bWJzdXBudW1cXHU4ZDVlXFx1NTQ4Y2NyYXppbHludW1cXHU4ZTI5XFx1NWI1N1xcdTZiYjVcXG4gICAgX19pbnRlbFJlY29tX18gPSAnXFx1NTQyNicgICMgXFx1NjY3YVxcdTgwZmRcXHU2M2E4XFx1ODM1MFxcdTUyOWZcXHU4MGZkKFxcdTg4NjhcXHU1YzVlXFx1NjAyN1xcdWZmMWFbaW50ZWxSZWNvbVxcdWZmMDhcXHU2NjJmL1xcdTU0MjZcXHVmZjA5XSxcXHU2NWIwXFx1NTg5ZWNsaWNrdGltZVtcXHU1MjRkXFx1N2FlZlxcdTRlMGRcXHU2NjNlXFx1NzkzYVxcdThiZTVcXHU1YjU3XFx1NmJiNV1cXHU1YjU3XFx1NmJiNVxcdWZmMDhcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU2NmY0XFx1NjViMFxcdWZmMDlcXHVmZjBjXFx1NjMwOWNsaWNrdGltZVxcdTYzOTJcXHU1ZThmXFx1NjdlNVxcdThiZTIpXFxuICAgIF9fYnJvd3NlQ2xpY2tfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbYnJvd3NlQ2xpY2s6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTViNTdcXHU2YmI1XFx1ZmYwOGNsaWNrbnVtXFx1ZmYwOVxcdWZmMGNcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU1NDBlXFx1N2FlZlxcdTgxZWFcXHU1MmE4KzFcXHVmZjA5XFx1MzAwMVxcdTYyOTVcXHU3OTY4XFx1NTI5ZlxcdTgwZmRcXHVmZjA4XFx1ODg2OFxcdTVjNWVcXHU2MDI3W3ZvdGU6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU2Mjk1XFx1Nzk2OFxcdTViNTdcXHU2YmI1XFx1ZmYwOHZvdGVudW1cXHVmZjA5LFxcdThjMDNcXHU3NTI4dm90ZVxcdTYzYTVcXHU1M2UzXFx1NTQwZVxcdTdhZWZ2b3RlbnVtKzFcXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXyA9ICdcXHU1NDI2JyAgIyBcXHU1MjRkXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBmb3JlRW5kTGlzdEF1dGhbXFx1NjYyZi9cXHU1NDI2XVxcdWZmMWJcXHU1ZjUzZm9yZUVuZExpc3RBdXRoPVxcdTY2MmZcXHVmZjBjXFx1NTIzN1xcdTc2ODRcXHU4ODY4XFx1NjViMFxcdTU4OWVcXHU3NTI4XFx1NjIzN1xcdTViNTdcXHU2YmI1dXNlcmlkXFx1ZmYwY1xcdTUyNGRcXHU1M2YwbGlzdFxcdTUyMTdcXHU4ODY4XFx1NjNhNVxcdTUzZTNcXHU0ZWM1XFx1ODBmZFxcdTY3ZTVcXHU3NzBiXFx1ODFlYVxcdTVkZjFcXHU3Njg0XFx1OGJiMFxcdTVmNTVcXHU1NDhjYWRkXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1NTNmMFxcdThkNGJcXHU1MDNjdXNlcmlkXFx1NzY4NFxcdTUwM2NcXG4gICAgXFxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J1xcdTUyMWJcXHU1ZWZhXFx1NjVmNlxcdTk1ZjQnKVxcbiAgICBndW95dWFubWluZ2NoZW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY3OWNcXHU1NmVkXFx1NTQwZFxcdTc5ZjAnKVxcbiAgICBndW95dWFudHVwaWFuID0gbW9kZWxzLlRleHRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU2ZWRcXHU1NmZlXFx1NzI0NycpXFxuICAgIGd1b3l1YW5kaXpoaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NzljXFx1NTZlZFxcdTU3MzBcXHU1NzQwJylcXG4gICAgemhvbmd6aGlwaW56aG9uZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU3OWNkXFx1NjkwZFxcdTU0YzFcXHU3OWNkJylcXG4gICAgemhvbmd6aGltaWFuamkgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NzljZFxcdTY5MGRcXHU5NzYyXFx1NzllZicpXFxuICAgIHpoYW5naGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjZcXHU1M2Y3JylcXG4gICAgbm9uZ2h1eGluZ21pbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE5Y1xcdTYyMzdcXHU1OWQzXFx1NTQwZCcpXFxuICAgIGxpYW54aWRpYW5odWEgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1ODA1NFxcdTdjZmJcXHU3NTM1XFx1OGJkZCcpXFxuXFxuICAgIGNsYXNzIE1ldGE6XFxuICAgICAgICBkYl90YWJsZSA9ICdndW95dWFueGlueGknXFxuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ1xcdTY3OWNcXHU1NmVkXFx1NGZlMVxcdTYwNmYnXFxuXFxuXFxuY2xhc3MgZ3VveXVhbnpodWFuZ3RhaShCYXNlTW9kZWwpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgXFx1Njc5Y1xcdTU2ZWRcXHU3MmI2XFx1NjAwMVxcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ2d1b3l1YW56aHVhbmd0YWknJydcXG4gICAgX190YWJsZW5hbWVfXyA9ICdndW95dWFuemh1YW5ndGFpJ1xcbiAgICBfX2F1dGhUYWJsZXNfXyA9IHsnemhhbmdoYW8nOiAnbm9uZ2h1J31cXG4gICAgX19hdXRoUGVvcGxlX18gPSAnXFx1NTQyNicgICMgXFx1NzUyOFxcdTYyMzdcXHU4ODY4XFx1ZmYwY1xcdTg4NjhcXHU1YzVlXFx1NjAyN2xvZ2luVXNlckNvbHVtblxcdTViZjlcXHU1ZTk0XFx1NzY4NFxcdTUwM2NcXHU1YzMxXFx1NjYyZlxcdTc1MjhcXHU2MjM3XFx1NTQwZFxcdTViNTdcXHU2YmI1XFx1ZmYwY21pbWFcXHU1YzMxXFx1NjYyZlxcdTViYzZcXHU3ODAxXFx1NWI1N1xcdTZiYjVcXG4gICAgX19zZnNoX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OHNmc2goXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcdWZmMGNcXFwiXFx1NjYyZlxcXCJcXHU2MjE2XFxcIlxcdTU0MjZcXFwiKVxcdTViNTdcXHU2YmI1XFx1NTQ4Y3NmaGYoXFx1NWJhMVxcdTY4MzhcXHU1NmRlXFx1NTkwZClcXHU1YjU3XFx1NmJiNVxcdWZmMGNcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4KHBhZ2UpXFx1NzY4NFxcdTY0Y2RcXHU0ZjVjXFx1NGUyZFxcdTg5ODFcXHU1OTFhXFx1NGUwMFxcdTRlMmFcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NjMwOVxcdTk0YWVcXHVmZjBjXFx1NzBiOVxcdTUxZmJcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NWYzOVxcdTUxZmFcXHU0ZTAwXFx1NGUyYVxcdTk4NzVcXHU5NzYyXFx1ZmYwY1xcdTUzMDVcXHU1NDJiXFxcIlxcdTY2MmZcXHU1NDI2XFx1NWJhMVxcdTY4MzhcXFwiXFx1NTQ4Y1xcXCJcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkXFxcIlxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTc4NmVcXHU1YjlhXFx1OGMwM1xcdTc1Mjh1cGRhdGVcXHU2M2E1XFx1NTNlM1xcdWZmMGNcXHU0ZmVlXFx1NjUzOXNmc2hcXHU1NDhjc2ZoZlxcdTRlMjRcXHU0ZTJhXFx1NWI1N1xcdTZiYjVcXHUzMDAyXFxuICAgIF9fYXV0aFNlcGFyYXRlX18gPSAnXFx1NTQyNicgICMgXFx1NTQwZVxcdTUzZjBcXHU1MjE3XFx1ODg2OFxcdTY3NDNcXHU5NjUwXFxuICAgIF9fdGh1bWJzVXBfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjd0aHVtYnNVcFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTY1YjBcXHU1ODlldGh1bWJzdXBudW1cXHU4ZDVlXFx1NTQ4Y2NyYXppbHludW1cXHU4ZTI5XFx1NWI1N1xcdTZiYjVcXG4gICAgX19pbnRlbFJlY29tX18gPSAnXFx1NTQyNicgICMgXFx1NjY3YVxcdTgwZmRcXHU2M2E4XFx1ODM1MFxcdTUyOWZcXHU4MGZkKFxcdTg4NjhcXHU1YzVlXFx1NjAyN1xcdWZmMWFbaW50ZWxSZWNvbVxcdWZmMDhcXHU2NjJmL1xcdTU0MjZcXHVmZjA5XSxcXHU2NWIwXFx1NTg5ZWNsaWNrdGltZVtcXHU1MjRkXFx1N2FlZlxcdTRlMGRcXHU2NjNlXFx1NzkzYVxcdThiZTVcXHU1YjU3XFx1NmJiNV1cXHU1YjU3XFx1NmJiNVxcdWZmMDhcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU2NmY0XFx1NjViMFxcdWZmMDlcXHVmZjBjXFx1NjMwOWNsaWNrdGltZVxcdTYzOTJcXHU1ZThmXFx1NjdlNVxcdThiZTIpXFxuICAgIF9fYnJvd3NlQ2xpY2tfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbYnJvd3NlQ2xpY2s6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTViNTdcXHU2YmI1XFx1ZmYwOGNsaWNrbnVtXFx1ZmYwOVxcdWZmMGNcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU1NDBlXFx1N2FlZlxcdTgxZWFcXHU1MmE4KzFcXHVmZjA5XFx1MzAwMVxcdTYyOTVcXHU3OTY4XFx1NTI5ZlxcdTgwZmRcXHVmZjA4XFx1ODg2OFxcdTVjNWVcXHU2MDI3W3ZvdGU6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU2Mjk1XFx1Nzk2OFxcdTViNTdcXHU2YmI1XFx1ZmYwOHZvdGVudW1cXHVmZjA5LFxcdThjMDNcXHU3NTI4dm90ZVxcdTYzYTVcXHU1M2UzXFx1NTQwZVxcdTdhZWZ2b3RlbnVtKzFcXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXyA9ICdcXHU1NDI2JyAgIyBcXHU1MjRkXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBmb3JlRW5kTGlzdEF1dGhbXFx1NjYyZi9cXHU1NDI2XVxcdWZmMWJcXHU1ZjUzZm9yZUVuZExpc3RBdXRoPVxcdTY2MmZcXHVmZjBjXFx1NTIzN1xcdTc2ODRcXHU4ODY4XFx1NjViMFxcdTU4OWVcXHU3NTI4XFx1NjIzN1xcdTViNTdcXHU2YmI1dXNlcmlkXFx1ZmYwY1xcdTUyNGRcXHU1M2YwbGlzdFxcdTUyMTdcXHU4ODY4XFx1NjNhNVxcdTUzZTNcXHU0ZWM1XFx1ODBmZFxcdTY3ZTVcXHU3NzBiXFx1ODFlYVxcdTVkZjFcXHU3Njg0XFx1OGJiMFxcdTVmNTVcXHU1NDhjYWRkXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1NTNmMFxcdThkNGJcXHU1MDNjdXNlcmlkXFx1NzY4NFxcdTUwM2NcXG4gICAgXFxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J1xcdTUyMWJcXHU1ZWZhXFx1NjVmNlxcdTk1ZjQnKVxcbiAgICBndW95dWFubWluZ2NoZW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY3OWNcXHU1NmVkXFx1NTQwZFxcdTc5ZjAnKVxcbiAgICB6aHVhbmd0YWltaW5nY2hlbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NzJiNlxcdTYwMDFcXHU1NDBkXFx1NzlmMCcpXFxuICAgIHpodWFuZ3RhaXR1cGlhbiA9IG1vZGVscy5UZXh0RmllbGQobnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTcyYjZcXHU2MDAxXFx1NTZmZVxcdTcyNDcnKVxcbiAgICB6aHVhbmd0YWltaWFvc2h1ID0gbW9kZWxzLlRleHRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NzJiNlxcdTYwMDFcXHU2M2NmXFx1OGZmMCcpXFxuICAgIGZhYnVyaXFpID0gbW9kZWxzLkRhdGVGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTNkMVxcdTVlMDNcXHU2NWU1XFx1NjcxZicpXFxuICAgIHpoYW5naGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjZcXHU1M2Y3JylcXG4gICAgbm9uZ2h1eGluZ21pbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE5Y1xcdTYyMzdcXHU1OWQzXFx1NTQwZCcpXFxuXFxuICAgIGNsYXNzIE1ldGE6XFxuICAgICAgICBkYl90YWJsZSA9ICdndW95dWFuemh1YW5ndGFpJ1xcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICdcXHU2NzljXFx1NTZlZFxcdTcyYjZcXHU2MDAxJ1xcblxcblxcbmNsYXNzIGd1b3BpbnhpbnhpKEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU2NzljXFx1NTRjMVxcdTRmZTFcXHU2MDZmXFx1NmEyMVxcdTU3OGJcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIF9fZG9jX18gPSB1JycnZ3VvcGlueGlueGknJydcXG4gICAgX190YWJsZW5hbWVfXyA9ICdndW9waW54aW54aSdcXG4gICAgX19hdXRoVGFibGVzX18gPSB7J3poYW5naGFvJzogJ25vbmdodSd9XFxuICAgIF9fYXV0aFBlb3BsZV9fID0gJ1xcdTU0MjYnICAjIFxcdTc1MjhcXHU2MjM3XFx1ODg2OFxcdWZmMGNcXHU4ODY4XFx1NWM1ZVxcdTYwMjdsb2dpblVzZXJDb2x1bW5cXHU1YmY5XFx1NWU5NFxcdTc2ODRcXHU1MDNjXFx1NWMzMVxcdTY2MmZcXHU3NTI4XFx1NjIzN1xcdTU0MGRcXHU1YjU3XFx1NmJiNVxcdWZmMGNtaW1hXFx1NWMzMVxcdTY2MmZcXHU1YmM2XFx1NzgwMVxcdTViNTdcXHU2YmI1XFxuICAgIF9fc2ZzaF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhzZnNoKFxcdTY2MmZcXHU1NDI2XFx1NWJhMVxcdTY4MzhcXHVmZjBjXFxcIlxcdTY2MmZcXFwiXFx1NjIxNlxcXCJcXHU1NDI2XFxcIilcXHU1YjU3XFx1NmJiNVxcdTU0OGNzZmhmKFxcdTViYTFcXHU2ODM4XFx1NTZkZVxcdTU5MGQpXFx1NWI1N1xcdTZiYjVcXHVmZjBjXFx1NTQwZVxcdTUzZjBcXHU1MjE3XFx1ODg2OChwYWdlKVxcdTc2ODRcXHU2NGNkXFx1NGY1Y1xcdTRlMmRcXHU4OTgxXFx1NTkxYVxcdTRlMDBcXHU0ZTJhXFxcIlxcdTViYTFcXHU2ODM4XFxcIlxcdTYzMDlcXHU5NGFlXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFxcIlxcdTViYTFcXHU2ODM4XFxcIlxcdTVmMzlcXHU1MWZhXFx1NGUwMFxcdTRlMmFcXHU5ODc1XFx1OTc2MlxcdWZmMGNcXHU1MzA1XFx1NTQyYlxcXCJcXHU2NjJmXFx1NTQyNlxcdTViYTFcXHU2ODM4XFxcIlxcdTU0OGNcXFwiXFx1NWJhMVxcdTY4MzhcXHU1NmRlXFx1NTkwZFxcXCJcXHVmZjBjXFx1NzBiOVxcdTUxZmJcXHU3ODZlXFx1NWI5YVxcdThjMDNcXHU3NTI4dXBkYXRlXFx1NjNhNVxcdTUzZTNcXHVmZjBjXFx1NGZlZVxcdTY1MzlzZnNoXFx1NTQ4Y3NmaGZcXHU0ZTI0XFx1NGUyYVxcdTViNTdcXHU2YmI1XFx1MzAwMlxcbiAgICBfX2F1dGhTZXBhcmF0ZV9fID0gJ1xcdTU0MjYnICAjIFxcdTU0MGVcXHU1M2YwXFx1NTIxN1xcdTg4NjhcXHU2NzQzXFx1OTY1MFxcbiAgICBfX3RodW1ic1VwX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OFxcdTVjNWVcXHU2MDI3dGh1bWJzVXBbXFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU2NWIwXFx1NTg5ZXRodW1ic3VwbnVtXFx1OGQ1ZVxcdTU0OGNjcmF6aWx5bnVtXFx1OGUyOVxcdTViNTdcXHU2YmI1XFxuICAgIF9faW50ZWxSZWNvbV9fID0gJ1xcdTU0MjYnICAjIFxcdTY2N2FcXHU4MGZkXFx1NjNhOFxcdTgzNTBcXHU1MjlmXFx1ODBmZChcXHU4ODY4XFx1NWM1ZVxcdTYwMjdcXHVmZjFhW2ludGVsUmVjb21cXHVmZjA4XFx1NjYyZi9cXHU1NDI2XFx1ZmYwOV0sXFx1NjViMFxcdTU4OWVjbGlja3RpbWVbXFx1NTI0ZFxcdTdhZWZcXHU0ZTBkXFx1NjYzZVxcdTc5M2FcXHU4YmU1XFx1NWI1N1xcdTZiYjVdXFx1NWI1N1xcdTZiYjVcXHVmZjA4XFx1OGMwM1xcdTc1MjhpbmZvL2RldGFpbFxcdTYzYTVcXHU1M2UzXFx1NzY4NFxcdTY1ZjZcXHU1MDE5XFx1NjZmNFxcdTY1YjBcXHVmZjA5XFx1ZmYwY1xcdTYzMDljbGlja3RpbWVcXHU2MzkyXFx1NWU4ZlxcdTY3ZTVcXHU4YmUyKVxcbiAgICBfX2Jyb3dzZUNsaWNrX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OFxcdTVjNWVcXHU2MDI3W2Jyb3dzZUNsaWNrOlxcdTY2MmYvXFx1NTQyNl1cXHVmZjBjXFx1NzBiOVxcdTUxZmJcXHU1YjU3XFx1NmJiNVxcdWZmMDhjbGlja251bVxcdWZmMDlcXHVmZjBjXFx1OGMwM1xcdTc1MjhpbmZvL2RldGFpbFxcdTYzYTVcXHU1M2UzXFx1NzY4NFxcdTY1ZjZcXHU1MDE5XFx1NTQwZVxcdTdhZWZcXHU4MWVhXFx1NTJhOCsxXFx1ZmYwOVxcdTMwMDFcXHU2Mjk1XFx1Nzk2OFxcdTUyOWZcXHU4MGZkXFx1ZmYwOFxcdTg4NjhcXHU1YzVlXFx1NjAyN1t2b3RlOlxcdTY2MmYvXFx1NTQyNl1cXHVmZjBjXFx1NjI5NVxcdTc5NjhcXHU1YjU3XFx1NmJiNVxcdWZmMDh2b3RlbnVtXFx1ZmYwOSxcXHU4YzAzXFx1NzUyOHZvdGVcXHU2M2E1XFx1NTNlM1xcdTU0MGVcXHU3YWVmdm90ZW51bSsxXFxuICAgIF9fZm9yZUVuZExpc3RBdXRoX18gPSAnXFx1NTQyNicgICMgXFx1NTI0ZFxcdTUzZjBcXHU1MjE3XFx1ODg2OFxcdTY3NDNcXHU5NjUwZm9yZUVuZExpc3RBdXRoW1xcdTY2MmYvXFx1NTQyNl1cXHVmZjFiXFx1NWY1M2ZvcmVFbmRMaXN0QXV0aD1cXHU2NjJmXFx1ZmYwY1xcdTUyMzdcXHU3Njg0XFx1ODg2OFxcdTY1YjBcXHU1ODllXFx1NzUyOFxcdTYyMzdcXHU1YjU3XFx1NmJiNXVzZXJpZFxcdWZmMGNcXHU1MjRkXFx1NTNmMGxpc3RcXHU1MjE3XFx1ODg2OFxcdTYzYTVcXHU1M2UzXFx1NGVjNVxcdTgwZmRcXHU2N2U1XFx1NzcwYlxcdTgxZWFcXHU1ZGYxXFx1NzY4NFxcdThiYjBcXHU1ZjU1XFx1NTQ4Y2FkZFxcdTYzYTVcXHU1M2UzXFx1NTQwZVxcdTUzZjBcXHU4ZDRiXFx1NTAzY3VzZXJpZFxcdTc2ODRcXHU1MDNjXFxuICAgIFxcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSdcXHU1MjFiXFx1NWVmYVxcdTY1ZjZcXHU5NWY0JylcXG4gICAgZ3VvcGlubWluZ2NoZW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY3OWNcXHU1NGMxXFx1NTQwZFxcdTc5ZjAnKVxcbiAgICBndW9waW50dXBpYW4gPSBtb2RlbHMuVGV4dEZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NzljXFx1NTRjMVxcdTU2ZmVcXHU3MjQ3JylcXG4gICAgZ3VvcGlucGluemhvbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU0YzFcXHU1NGMxXFx1NzljZCcpXFxuICAgIHpoaWxpYW5nZGVuZ2ppID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjhcXHU5MWNmXFx1N2I0OVxcdTdlYTcnKVxcbiAgICBndW9waW5qaWFnZSA9IG1vZGVscy5GbG9hdEZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NzljXFx1NTRjMVxcdTRlZjdcXHU2ODNjJylcXG4gICAgc2hlbmdjaGFucmlxaSA9IG1vZGVscy5EYXRlRmllbGQobnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTc1MWZcXHU0ZWE3XFx1NjVlNVxcdTY3MWYnKVxcbiAgICBiYW96aGlxaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU0ZmRkXFx1OGQyOFxcdTY3MWYnKVxcbiAgICBndW9waW5taWFvc2h1ID0gbW9kZWxzLlRleHRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU0YzFcXHU2M2NmXFx1OGZmMCcpXFxuICAgIHpoYW5naGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjZcXHU1M2Y3JylcXG4gICAgbm9uZ2h1eGluZ21pbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE5Y1xcdTYyMzdcXHU1OWQzXFx1NTQwZCcpXFxuXFxuICAgIGNsYXNzIE1ldGE6XFxuICAgICAgICBkYl90YWJsZSA9ICdndW9waW54aW54aSdcXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSAnXFx1Njc5Y1xcdTU0YzFcXHU0ZmUxXFx1NjA2ZidcXG5cXG5cXG5jbGFzcyBjYW5nY2h1KEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU0ZWQzXFx1NTBhOFxcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ2NhbmdjaHUnJydcXG4gICAgX190YWJsZW5hbWVfXyA9ICdjYW5nY2h1J1xcbiAgICBfX2F1dGhUYWJsZXNfXyA9IHsnemhhbmdoYW8nOiAnbm9uZ2h1J31cXG4gICAgX19hdXRoUGVvcGxlX18gPSAnXFx1NTQyNicgICMgXFx1NzUyOFxcdTYyMzdcXHU4ODY4XFx1ZmYwY1xcdTg4NjhcXHU1YzVlXFx1NjAyN2xvZ2luVXNlckNvbHVtblxcdTViZjlcXHU1ZTk0XFx1NzY4NFxcdTUwM2NcXHU1YzMxXFx1NjYyZlxcdTc1MjhcXHU2MjM3XFx1NTQwZFxcdTViNTdcXHU2YmI1XFx1ZmYwY21pbWFcXHU1YzMxXFx1NjYyZlxcdTViYzZcXHU3ODAxXFx1NWI1N1xcdTZiYjVcXG4gICAgX19zZnNoX18gPSAnXFx1NTQyNicgICMgXFx1ODg2OHNmc2goXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcdWZmMGNcXFwiXFx1NjYyZlxcXCJcXHU2MjE2XFxcIlxcdTU0MjZcXFwiKVxcdTViNTdcXHU2YmI1XFx1NTQ4Y3NmaGYoXFx1NWJhMVxcdTY4MzhcXHU1NmRlXFx1NTkwZClcXHU1YjU3XFx1NmJiNVxcdWZmMGNcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4KHBhZ2UpXFx1NzY4NFxcdTY0Y2RcXHU0ZjVjXFx1NGUyZFxcdTg5ODFcXHU1OTFhXFx1NGUwMFxcdTRlMmFcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NjMwOVxcdTk0YWVcXHVmZjBjXFx1NzBiOVxcdTUxZmJcXFwiXFx1NWJhMVxcdTY4MzhcXFwiXFx1NWYzOVxcdTUxZmFcXHU0ZTAwXFx1NGUyYVxcdTk4NzVcXHU5NzYyXFx1ZmYwY1xcdTUzMDVcXHU1NDJiXFxcIlxcdTY2MmZcXHU1NDI2XFx1NWJhMVxcdTY4MzhcXFwiXFx1NTQ4Y1xcXCJcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkXFxcIlxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTc4NmVcXHU1YjlhXFx1OGMwM1xcdTc1Mjh1cGRhdGVcXHU2M2E1XFx1NTNlM1xcdWZmMGNcXHU0ZmVlXFx1NjUzOXNmc2hcXHU1NDhjc2ZoZlxcdTRlMjRcXHU0ZTJhXFx1NWI1N1xcdTZiYjVcXHUzMDAyXFxuICAgIF9fYXV0aFNlcGFyYXRlX18gPSAnXFx1NTQyNicgICMgXFx1NTQwZVxcdTUzZjBcXHU1MjE3XFx1ODg2OFxcdTY3NDNcXHU5NjUwXFxuICAgIF9fdGh1bWJzVXBfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjd0aHVtYnNVcFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTY1YjBcXHU1ODlldGh1bWJzdXBudW1cXHU4ZDVlXFx1NTQ4Y2NyYXppbHludW1cXHU4ZTI5XFx1NWI1N1xcdTZiYjVcXG4gICAgX19pbnRlbFJlY29tX18gPSAnXFx1NTQyNicgICMgXFx1NjY3YVxcdTgwZmRcXHU2M2E4XFx1ODM1MFxcdTUyOWZcXHU4MGZkKFxcdTg4NjhcXHU1YzVlXFx1NjAyN1xcdWZmMWFbaW50ZWxSZWNvbVxcdWZmMDhcXHU2NjJmL1xcdTU0MjZcXHVmZjA5XSxcXHU2NWIwXFx1NTg5ZWNsaWNrdGltZVtcXHU1MjRkXFx1N2FlZlxcdTRlMGRcXHU2NjNlXFx1NzkzYVxcdThiZTVcXHU1YjU3XFx1NmJiNV1cXHU1YjU3XFx1NmJiNVxcdWZmMDhcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU2NmY0XFx1NjViMFxcdWZmMDlcXHVmZjBjXFx1NjMwOWNsaWNrdGltZVxcdTYzOTJcXHU1ZThmXFx1NjdlNVxcdThiZTIpXFxuICAgIF9fYnJvd3NlQ2xpY2tfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbYnJvd3NlQ2xpY2s6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcdTViNTdcXHU2YmI1XFx1ZmYwOGNsaWNrbnVtXFx1ZmYwOVxcdWZmMGNcXHU4YzAzXFx1NzUyOGluZm8vZGV0YWlsXFx1NjNhNVxcdTUzZTNcXHU3Njg0XFx1NjVmNlxcdTUwMTlcXHU1NDBlXFx1N2FlZlxcdTgxZWFcXHU1MmE4KzFcXHVmZjA5XFx1MzAwMVxcdTYyOTVcXHU3OTY4XFx1NTI5ZlxcdTgwZmRcXHVmZjA4XFx1ODg2OFxcdTVjNWVcXHU2MDI3W3ZvdGU6XFx1NjYyZi9cXHU1NDI2XVxcdWZmMGNcXHU2Mjk1XFx1Nzk2OFxcdTViNTdcXHU2YmI1XFx1ZmYwOHZvdGVudW1cXHVmZjA5LFxcdThjMDNcXHU3NTI4dm90ZVxcdTYzYTVcXHU1M2UzXFx1NTQwZVxcdTdhZWZ2b3RlbnVtKzFcXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXyA9ICdcXHU1NDI2JyAgIyBcXHU1MjRkXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBmb3JlRW5kTGlzdEF1dGhbXFx1NjYyZi9cXHU1NDI2XVxcdWZmMWJcXHU1ZjUzZm9yZUVuZExpc3RBdXRoPVxcdTY2MmZcXHVmZjBjXFx1NTIzN1xcdTc2ODRcXHU4ODY4XFx1NjViMFxcdTU4OWVcXHU3NTI4XFx1NjIzN1xcdTViNTdcXHU2YmI1dXNlcmlkXFx1ZmYwY1xcdTUyNGRcXHU1M2YwbGlzdFxcdTUyMTdcXHU4ODY4XFx1NjNhNVxcdTUzZTNcXHU0ZWM1XFx1ODBmZFxcdTY3ZTVcXHU3NzBiXFx1ODFlYVxcdTVkZjFcXHU3Njg0XFx1OGJiMFxcdTVmNTVcXHU1NDhjYWRkXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1NTNmMFxcdThkNGJcXHU1MDNjdXNlcmlkXFx1NzY4NFxcdTUwM2NcXG4gICAgXFxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J1xcdTUyMWJcXHU1ZWZhXFx1NjVmNlxcdTk1ZjQnKVxcbiAgICBjYW5na3VtaW5nY2hlbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NGVkM1xcdTVlOTNcXHU1NDBkXFx1NzlmMCcpXFxuICAgIGNhbmdrdXR1cGlhbiA9IG1vZGVscy5UZXh0RmllbGQobnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTRlZDNcXHU1ZTkzXFx1NTZmZVxcdTcyNDcnKVxcbiAgICBjYW5na3VkaXpoaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU0ZWQzXFx1NWU5M1xcdTU3MzBcXHU1NzQwJylcXG4gICAgcm9uZ2xpYW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTViYjlcXHU5MWNmJylcXG4gICAgd2VuZHUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NmUyOVxcdTVlYTYnKVxcbiAgICBzaGlkdSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2ZTdmXFx1NWVhNicpXFxuICAgIGNhbmdrdW1pYW9zaHUgPSBtb2RlbHMuVGV4dEZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU0ZWQzXFx1NWU5M1xcdTYzY2ZcXHU4ZmYwJylcXG4gICAgemhhbmdoYW8gPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1OGQyNlxcdTUzZjcnKVxcbiAgICBub25naHV4aW5nbWluZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1MTljXFx1NjIzN1xcdTU5ZDNcXHU1NDBkJylcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ2NhbmdjaHUnXFxuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ1xcdTRlZDNcXHU1MGE4J1xcblxcblxcbmNsYXNzIHJ1a3UoQmFzZU1vZGVsKTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIFxcdTUxNjVcXHU1ZTkzXFx1NmEyMVxcdTU3OGJcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIF9fZG9jX18gPSB1JycncnVrdScnJ1xcbiAgICBfX3RhYmxlbmFtZV9fID0gJ3J1a3UnXFxuICAgIF9fYXV0aFRhYmxlc19fID0geyd6aGFuZ2hhbyc6ICdub25naHUnfVxcbiAgICBfX2F1dGhQZW9wbGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU3NTI4XFx1NjIzN1xcdTg4NjhcXHVmZjBjXFx1ODg2OFxcdTVjNWVcXHU2MDI3bG9naW5Vc2VyQ29sdW1uXFx1NWJmOVxcdTVlOTRcXHU3Njg0XFx1NTAzY1xcdTVjMzFcXHU2NjJmXFx1NzUyOFxcdTYyMzdcXHU1NDBkXFx1NWI1N1xcdTZiYjVcXHVmZjBjbWltYVxcdTVjMzFcXHU2NjJmXFx1NWJjNlxcdTc4MDFcXHU1YjU3XFx1NmJiNVxcbiAgICBfX3Nmc2hfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4c2ZzaChcXHU2NjJmXFx1NTQyNlxcdTViYTFcXHU2ODM4XFx1ZmYwY1xcXCJcXHU2NjJmXFxcIlxcdTYyMTZcXFwiXFx1NTQyNlxcXCIpXFx1NWI1N1xcdTZiYjVcXHU1NDhjc2ZoZihcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkKVxcdTViNTdcXHU2YmI1XFx1ZmYwY1xcdTU0MGVcXHU1M2YwXFx1NTIxN1xcdTg4NjgocGFnZSlcXHU3Njg0XFx1NjRjZFxcdTRmNWNcXHU0ZTJkXFx1ODk4MVxcdTU5MWFcXHU0ZTAwXFx1NGUyYVxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU2MzA5XFx1OTRhZVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU1ZjM5XFx1NTFmYVxcdTRlMDBcXHU0ZTJhXFx1OTg3NVxcdTk3NjJcXHVmZjBjXFx1NTMwNVxcdTU0MmJcXFwiXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcXCJcXHU1NDhjXFxcIlxcdTViYTFcXHU2ODM4XFx1NTZkZVxcdTU5MGRcXFwiXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1Nzg2ZVxcdTViOWFcXHU4YzAzXFx1NzUyOHVwZGF0ZVxcdTYzYTVcXHU1M2UzXFx1ZmYwY1xcdTRmZWVcXHU2NTM5c2ZzaFxcdTU0OGNzZmhmXFx1NGUyNFxcdTRlMmFcXHU1YjU3XFx1NmJiNVxcdTMwMDJcXG4gICAgX19hdXRoU2VwYXJhdGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBcXG4gICAgX190aHVtYnNVcF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN3RodW1ic1VwW1xcdTY2MmYvXFx1NTQyNl1cXHVmZjBjXFx1NjViMFxcdTU4OWV0aHVtYnN1cG51bVxcdThkNWVcXHU1NDhjY3JhemlseW51bVxcdThlMjlcXHU1YjU3XFx1NmJiNVxcbiAgICBfX2ludGVsUmVjb21fXyA9ICdcXHU1NDI2JyAgIyBcXHU2NjdhXFx1ODBmZFxcdTYzYThcXHU4MzUwXFx1NTI5ZlxcdTgwZmQoXFx1ODg2OFxcdTVjNWVcXHU2MDI3XFx1ZmYxYVtpbnRlbFJlY29tXFx1ZmYwOFxcdTY2MmYvXFx1NTQyNlxcdWZmMDldLFxcdTY1YjBcXHU1ODllY2xpY2t0aW1lW1xcdTUyNGRcXHU3YWVmXFx1NGUwZFxcdTY2M2VcXHU3OTNhXFx1OGJlNVxcdTViNTdcXHU2YmI1XVxcdTViNTdcXHU2YmI1XFx1ZmYwOFxcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTY2ZjRcXHU2NWIwXFx1ZmYwOVxcdWZmMGNcXHU2MzA5Y2xpY2t0aW1lXFx1NjM5MlxcdTVlOGZcXHU2N2U1XFx1OGJlMilcXG4gICAgX19icm93c2VDbGlja19fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN1ticm93c2VDbGljazpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1NWI1N1xcdTZiYjVcXHVmZjA4Y2xpY2tudW1cXHVmZjA5XFx1ZmYwY1xcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTU0MGVcXHU3YWVmXFx1ODFlYVxcdTUyYTgrMVxcdWZmMDlcXHUzMDAxXFx1NjI5NVxcdTc5NjhcXHU1MjlmXFx1ODBmZFxcdWZmMDhcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbdm90ZTpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTYyOTVcXHU3OTY4XFx1NWI1N1xcdTZiYjVcXHVmZjA4dm90ZW51bVxcdWZmMDksXFx1OGMwM1xcdTc1Mjh2b3RlXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1N2FlZnZvdGVudW0rMVxcbiAgICBfX2ZvcmVFbmRMaXN0QXV0aF9fID0gJ1xcdTU0MjYnICAjIFxcdTUyNGRcXHU1M2YwXFx1NTIxN1xcdTg4NjhcXHU2NzQzXFx1OTY1MGZvcmVFbmRMaXN0QXV0aFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYxYlxcdTVmNTNmb3JlRW5kTGlzdEF1dGg9XFx1NjYyZlxcdWZmMGNcXHU1MjM3XFx1NzY4NFxcdTg4NjhcXHU2NWIwXFx1NTg5ZVxcdTc1MjhcXHU2MjM3XFx1NWI1N1xcdTZiYjV1c2VyaWRcXHVmZjBjXFx1NTI0ZFxcdTUzZjBsaXN0XFx1NTIxN1xcdTg4NjhcXHU2M2E1XFx1NTNlM1xcdTRlYzVcXHU4MGZkXFx1NjdlNVxcdTc3MGJcXHU4MWVhXFx1NWRmMVxcdTc2ODRcXHU4YmIwXFx1NWY1NVxcdTU0OGNhZGRcXHU2M2E1XFx1NTNlM1xcdTU0MGVcXHU1M2YwXFx1OGQ0YlxcdTUwM2N1c2VyaWRcXHU3Njg0XFx1NTAzY1xcbiAgICBcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIHJ1a3ViaWFuaGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0nXFx1NTE2NVxcdTVlOTNcXHU3ZjE2XFx1NTNmNycpXFxuICAgIGd1b3Bpbm1pbmdjaGVuZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NzljXFx1NTRjMVxcdTU0MGRcXHU3OWYwJylcXG4gICAgZ3VvcGlucGluemhvbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU0YzFcXHU1NGMxXFx1NzljZCcpXFxuICAgIHpoaWxpYW5nZGVuZ2ppID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjhcXHU5MWNmXFx1N2I0OVxcdTdlYTcnKVxcbiAgICBzaHVsaWFuZyA9IG1vZGVscy5JbnRlZ2VyRmllbGQobnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY1NzBcXHU5MWNmJylcXG4gICAgcnVrdXNoaWppYW4gPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE2NVxcdTVlOTNcXHU2NWY2XFx1OTVmNCcpXFxuICAgIGNhbmdrdW1pbmdjaGVuZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU0ZWQzXFx1NWU5M1xcdTU0MGRcXHU3OWYwJylcXG4gICAgemhhbmdoYW8gPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1OGQyNlxcdTUzZjcnKVxcbiAgICBub25naHV4aW5nbWluZyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1MTljXFx1NjIzN1xcdTU5ZDNcXHU1NDBkJylcXG5cXG4gICAgY2xhc3MgTWV0YTpcXG4gICAgICAgIGRiX3RhYmxlID0gJ3J1a3UnXFxuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ1xcdTUxNjVcXHU1ZTkzJ1xcblxcblxcbmNsYXNzIGNodWt1KEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU1MWZhXFx1NWU5M1xcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ2NodWt1JycnXFxuICAgIF9fdGFibGVuYW1lX18gPSAnY2h1a3UnXFxuICAgIF9fYXV0aFRhYmxlc19fID0geyd6aGFuZ2hhbyc6ICdub25naHUnfVxcbiAgICBfX2F1dGhQZW9wbGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU3NTI4XFx1NjIzN1xcdTg4NjhcXHVmZjBjXFx1ODg2OFxcdTVjNWVcXHU2MDI3bG9naW5Vc2VyQ29sdW1uXFx1NWJmOVxcdTVlOTRcXHU3Njg0XFx1NTAzY1xcdTVjMzFcXHU2NjJmXFx1NzUyOFxcdTYyMzdcXHU1NDBkXFx1NWI1N1xcdTZiYjVcXHVmZjBjbWltYVxcdTVjMzFcXHU2NjJmXFx1NWJjNlxcdTc4MDFcXHU1YjU3XFx1NmJiNVxcbiAgICBfX3Nmc2hfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4c2ZzaChcXHU2NjJmXFx1NTQyNlxcdTViYTFcXHU2ODM4XFx1ZmYwY1xcXCJcXHU2NjJmXFxcIlxcdTYyMTZcXFwiXFx1NTQyNlxcXCIpXFx1NWI1N1xcdTZiYjVcXHU1NDhjc2ZoZihcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkKVxcdTViNTdcXHU2YmI1XFx1ZmYwY1xcdTU0MGVcXHU1M2YwXFx1NTIxN1xcdTg4NjgocGFnZSlcXHU3Njg0XFx1NjRjZFxcdTRmNWNcXHU0ZTJkXFx1ODk4MVxcdTU5MWFcXHU0ZTAwXFx1NGUyYVxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU2MzA5XFx1OTRhZVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU1ZjM5XFx1NTFmYVxcdTRlMDBcXHU0ZTJhXFx1OTg3NVxcdTk3NjJcXHVmZjBjXFx1NTMwNVxcdTU0MmJcXFwiXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcXCJcXHU1NDhjXFxcIlxcdTViYTFcXHU2ODM4XFx1NTZkZVxcdTU5MGRcXFwiXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1Nzg2ZVxcdTViOWFcXHU4YzAzXFx1NzUyOHVwZGF0ZVxcdTYzYTVcXHU1M2UzXFx1ZmYwY1xcdTRmZWVcXHU2NTM5c2ZzaFxcdTU0OGNzZmhmXFx1NGUyNFxcdTRlMmFcXHU1YjU3XFx1NmJiNVxcdTMwMDJcXG4gICAgX19hdXRoU2VwYXJhdGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBcXG4gICAgX190aHVtYnNVcF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN3RodW1ic1VwW1xcdTY2MmYvXFx1NTQyNl1cXHVmZjBjXFx1NjViMFxcdTU4OWV0aHVtYnN1cG51bVxcdThkNWVcXHU1NDhjY3JhemlseW51bVxcdThlMjlcXHU1YjU3XFx1NmJiNVxcbiAgICBfX2ludGVsUmVjb21fXyA9ICdcXHU1NDI2JyAgIyBcXHU2NjdhXFx1ODBmZFxcdTYzYThcXHU4MzUwXFx1NTI5ZlxcdTgwZmQoXFx1ODg2OFxcdTVjNWVcXHU2MDI3XFx1ZmYxYVtpbnRlbFJlY29tXFx1ZmYwOFxcdTY2MmYvXFx1NTQyNlxcdWZmMDldLFxcdTY1YjBcXHU1ODllY2xpY2t0aW1lW1xcdTUyNGRcXHU3YWVmXFx1NGUwZFxcdTY2M2VcXHU3OTNhXFx1OGJlNVxcdTViNTdcXHU2YmI1XVxcdTViNTdcXHU2YmI1XFx1ZmYwOFxcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTY2ZjRcXHU2NWIwXFx1ZmYwOVxcdWZmMGNcXHU2MzA5Y2xpY2t0aW1lXFx1NjM5MlxcdTVlOGZcXHU2N2U1XFx1OGJlMilcXG4gICAgX19icm93c2VDbGlja19fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN1ticm93c2VDbGljazpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1NWI1N1xcdTZiYjVcXHVmZjA4Y2xpY2tudW1cXHVmZjA5XFx1ZmYwY1xcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTU0MGVcXHU3YWVmXFx1ODFlYVxcdTUyYTgrMVxcdWZmMDlcXHUzMDAxXFx1NjI5NVxcdTc5NjhcXHU1MjlmXFx1ODBmZFxcdWZmMDhcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbdm90ZTpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTYyOTVcXHU3OTY4XFx1NWI1N1xcdTZiYjVcXHVmZjA4dm90ZW51bVxcdWZmMDksXFx1OGMwM1xcdTc1Mjh2b3RlXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1N2FlZnZvdGVudW0rMVxcbiAgICBfX2ZvcmVFbmRMaXN0QXV0aF9fID0gJ1xcdTU0MjYnICAjIFxcdTUyNGRcXHU1M2YwXFx1NTIxN1xcdTg4NjhcXHU2NzQzXFx1OTY1MGZvcmVFbmRMaXN0QXV0aFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYxYlxcdTVmNTNmb3JlRW5kTGlzdEF1dGg9XFx1NjYyZlxcdWZmMGNcXHU1MjM3XFx1NzY4NFxcdTg4NjhcXHU2NWIwXFx1NTg5ZVxcdTc1MjhcXHU2MjM3XFx1NWI1N1xcdTZiYjV1c2VyaWRcXHVmZjBjXFx1NTI0ZFxcdTUzZjBsaXN0XFx1NTIxN1xcdTg4NjhcXHU2M2E1XFx1NTNlM1xcdTRlYzVcXHU4MGZkXFx1NjdlNVxcdTc3MGJcXHU4MWVhXFx1NWRmMVxcdTc2ODRcXHU4YmIwXFx1NWY1NVxcdTU0OGNhZGRcXHU2M2E1XFx1NTNlM1xcdTU0MGVcXHU1M2YwXFx1OGQ0YlxcdTUwM2N1c2VyaWRcXHU3Njg0XFx1NTAzY1xcbiAgICBcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIGNodWt1YmlhbmhhbyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J1xcdTUxZmFcXHU1ZTkzXFx1N2YxNlxcdTUzZjcnKVxcbiAgICBndW9waW5taW5nY2hlbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU0YzFcXHU1NDBkXFx1NzlmMCcpXFxuICAgIGd1b3BpbnBpbnpob25nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY3OWNcXHU1NGMxXFx1NTRjMVxcdTc5Y2QnKVxcbiAgICB6aGlsaWFuZ2RlbmdqaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU4ZDI4XFx1OTFjZlxcdTdiNDlcXHU3ZWE3JylcXG4gICAgc2h1bGlhbmcgPSBtb2RlbHMuSW50ZWdlckZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NTcwXFx1OTFjZicpXFxuICAgIGNodWt1c2hpamlhbiA9IG1vZGVscy5EYXRlVGltZUZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU1MWZhXFx1NWU5M1xcdTY1ZjZcXHU5NWY0JylcXG4gICAgY2FuZ2t1bWluZ2NoZW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTRlZDNcXHU1ZTkzXFx1NTQwZFxcdTc5ZjAnKVxcbiAgICB6aGFuZ2hhbyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU4ZDI2XFx1NTNmNycpXFxuICAgIG5vbmdodXhpbmdtaW5nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTUxOWNcXHU2MjM3XFx1NTlkM1xcdTU0MGQnKVxcblxcbiAgICBjbGFzcyBNZXRhOlxcbiAgICAgICAgZGJfdGFibGUgPSAnY2h1a3UnXFxuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ1xcdTUxZmFcXHU1ZTkzJ1xcblxcblxcbmNsYXNzIHd1bGl1KEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU3MjY5XFx1NmQ0MVxcdTZhMjFcXHU1NzhiXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBfX2RvY19fID0gdScnJ3d1bGl1JycnXFxuICAgIF9fdGFibGVuYW1lX18gPSAnd3VsaXUnXFxuICAgIF9fYXV0aFRhYmxlc19fID0geyd6aGFuZ2hhbyc6ICdub25naHUnfVxcbiAgICBfX2F1dGhQZW9wbGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU3NTI4XFx1NjIzN1xcdTg4NjhcXHVmZjBjXFx1ODg2OFxcdTVjNWVcXHU2MDI3bG9naW5Vc2VyQ29sdW1uXFx1NWJmOVxcdTVlOTRcXHU3Njg0XFx1NTAzY1xcdTVjMzFcXHU2NjJmXFx1NzUyOFxcdTYyMzdcXHU1NDBkXFx1NWI1N1xcdTZiYjVcXHVmZjBjbWltYVxcdTVjMzFcXHU2NjJmXFx1NWJjNlxcdTc4MDFcXHU1YjU3XFx1NmJiNVxcbiAgICBfX3Nmc2hfXyA9ICdcXHU1NDI2JyAgIyBcXHU4ODY4c2ZzaChcXHU2NjJmXFx1NTQyNlxcdTViYTFcXHU2ODM4XFx1ZmYwY1xcXCJcXHU2NjJmXFxcIlxcdTYyMTZcXFwiXFx1NTQyNlxcXCIpXFx1NWI1N1xcdTZiYjVcXHU1NDhjc2ZoZihcXHU1YmExXFx1NjgzOFxcdTU2ZGVcXHU1OTBkKVxcdTViNTdcXHU2YmI1XFx1ZmYwY1xcdTU0MGVcXHU1M2YwXFx1NTIxN1xcdTg4NjgocGFnZSlcXHU3Njg0XFx1NjRjZFxcdTRmNWNcXHU0ZTJkXFx1ODk4MVxcdTU5MWFcXHU0ZTAwXFx1NGUyYVxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU2MzA5XFx1OTRhZVxcdWZmMGNcXHU3MGI5XFx1NTFmYlxcXCJcXHU1YmExXFx1NjgzOFxcXCJcXHU1ZjM5XFx1NTFmYVxcdTRlMDBcXHU0ZTJhXFx1OTg3NVxcdTk3NjJcXHVmZjBjXFx1NTMwNVxcdTU0MmJcXFwiXFx1NjYyZlxcdTU0MjZcXHU1YmExXFx1NjgzOFxcXCJcXHU1NDhjXFxcIlxcdTViYTFcXHU2ODM4XFx1NTZkZVxcdTU5MGRcXFwiXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1Nzg2ZVxcdTViOWFcXHU4YzAzXFx1NzUyOHVwZGF0ZVxcdTYzYTVcXHU1M2UzXFx1ZmYwY1xcdTRmZWVcXHU2NTM5c2ZzaFxcdTU0OGNzZmhmXFx1NGUyNFxcdTRlMmFcXHU1YjU3XFx1NmJiNVxcdTMwMDJcXG4gICAgX19hdXRoU2VwYXJhdGVfXyA9ICdcXHU1NDI2JyAgIyBcXHU1NDBlXFx1NTNmMFxcdTUyMTdcXHU4ODY4XFx1Njc0M1xcdTk2NTBcXG4gICAgX190aHVtYnNVcF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN3RodW1ic1VwW1xcdTY2MmYvXFx1NTQyNl1cXHVmZjBjXFx1NjViMFxcdTU4OWV0aHVtYnN1cG51bVxcdThkNWVcXHU1NDhjY3JhemlseW51bVxcdThlMjlcXHU1YjU3XFx1NmJiNVxcbiAgICBfX2ludGVsUmVjb21fXyA9ICdcXHU1NDI2JyAgIyBcXHU2NjdhXFx1ODBmZFxcdTYzYThcXHU4MzUwXFx1NTI5ZlxcdTgwZmQoXFx1ODg2OFxcdTVjNWVcXHU2MDI3XFx1ZmYxYVtpbnRlbFJlY29tXFx1ZmYwOFxcdTY2MmYvXFx1NTQyNlxcdWZmMDldLFxcdTY1YjBcXHU1ODllY2xpY2t0aW1lW1xcdTUyNGRcXHU3YWVmXFx1NGUwZFxcdTY2M2VcXHU3OTNhXFx1OGJlNVxcdTViNTdcXHU2YmI1XVxcdTViNTdcXHU2YmI1XFx1ZmYwOFxcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTY2ZjRcXHU2NWIwXFx1ZmYwOVxcdWZmMGNcXHU2MzA5Y2xpY2t0aW1lXFx1NjM5MlxcdTVlOGZcXHU2N2U1XFx1OGJlMilcXG4gICAgX19icm93c2VDbGlja19fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN1ticm93c2VDbGljazpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTcwYjlcXHU1MWZiXFx1NWI1N1xcdTZiYjVcXHVmZjA4Y2xpY2tudW1cXHVmZjA5XFx1ZmYwY1xcdThjMDNcXHU3NTI4aW5mby9kZXRhaWxcXHU2M2E1XFx1NTNlM1xcdTc2ODRcXHU2NWY2XFx1NTAxOVxcdTU0MGVcXHU3YWVmXFx1ODFlYVxcdTUyYTgrMVxcdWZmMDlcXHUzMDAxXFx1NjI5NVxcdTc5NjhcXHU1MjlmXFx1ODBmZFxcdWZmMDhcXHU4ODY4XFx1NWM1ZVxcdTYwMjdbdm90ZTpcXHU2NjJmL1xcdTU0MjZdXFx1ZmYwY1xcdTYyOTVcXHU3OTY4XFx1NWI1N1xcdTZiYjVcXHVmZjA4dm90ZW51bVxcdWZmMDksXFx1OGMwM1xcdTc1Mjh2b3RlXFx1NjNhNVxcdTUzZTNcXHU1NDBlXFx1N2FlZnZvdGVudW0rMVxcbiAgICBfX2ZvcmVFbmRMaXN0QXV0aF9fID0gJ1xcdTU0MjYnICAjIFxcdTUyNGRcXHU1M2YwXFx1NTIxN1xcdTg4NjhcXHU2NzQzXFx1OTY1MGZvcmVFbmRMaXN0QXV0aFtcXHU2NjJmL1xcdTU0MjZdXFx1ZmYxYlxcdTVmNTNmb3JlRW5kTGlzdEF1dGg9XFx1NjYyZlxcdWZmMGNcXHU1MjM3XFx1NzY4NFxcdTg4NjhcXHU2NWIwXFx1NTg5ZVxcdTc1MjhcXHU2MjM3XFx1NWI1N1xcdTZiYjV1c2VyaWRcXHVmZjBjXFx1NTI0ZFxcdTUzZjBsaXN0XFx1NTIxN1xcdTg4NjhcXHU2M2E1XFx1NTNlM1xcdTRlYzVcXHU4MGZkXFx1NjdlNVxcdTc3MGJcXHU4MWVhXFx1NWRmMVxcdTc2ODRcXHU4YmIwXFx1NWY1NVxcdTU0OGNhZGRcXHU2M2E1XFx1NTNlM1xcdTU0MGVcXHU1M2YwXFx1OGQ0YlxcdTUwM2N1c2VyaWRcXHU3Njg0XFx1NTAzY1xcbiAgICBcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIHd1bGl1YmlhbmhhbyA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J1xcdTcyNjlcXHU2ZDQxXFx1N2YxNlxcdTUzZjcnKVxcbiAgICBndW9waW5taW5nY2hlbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1Njc5Y1xcdTU0YzFcXHU1NDBkXFx1NzlmMCcpXFxuICAgIGd1b3BpbnBpbnpob25nID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY3OWNcXHU1NGMxXFx1NTRjMVxcdTc5Y2QnKVxcbiAgICB6aGlsaWFuZ2RlbmdqaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU4ZDI4XFx1OTFjZlxcdTdiNDlcXHU3ZWE3JylcXG4gICAgc2h1bGlhbmcgPSBtb2RlbHMuSW50ZWdlckZpZWxkKG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU2NTcwXFx1OTFjZicpXFxuICAgIHd1bGl1Z29uZ3NpID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTcyNjlcXHU2ZDQxXFx1NTE2Y1xcdTUzZjgnKVxcbiAgICB5dW5mZWkgPSBtb2RlbHMuRmxvYXRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1OGZkMFxcdThkMzknKVxcbiAgICB3dWxpdXpodWFuZ3RhaSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0yNTUsIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSdcXHU3MjY5XFx1NmQ0MVxcdTcyYjZcXHU2MDAxJylcXG4gICAgYmVpemh1ID0gbW9kZWxzLlRleHRGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTkwN1xcdTZjZTgnKVxcbiAgICBmYWh1b3NoaWppYW4gPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTNkMVxcdThkMjdcXHU2NWY2XFx1OTVmNCcpXFxuICAgIHpoYW5naGFvID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdThkMjZcXHU1M2Y3JylcXG4gICAgbm9uZ2h1eGluZ21pbmcgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NTE5Y1xcdTYyMzdcXHU1OWQzXFx1NTQwZCcpXFxuICAgIHNob3VodW9yZW4gPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MjU1LCBudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0nXFx1NjUzNlxcdThkMjdcXHU0ZWJhJylcXG4gICAgc2hvdWh1b2RpemhpID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTY1MzZcXHU4ZDI3XFx1NTczMFxcdTU3NDAnKVxcbiAgICBsaWFueGlkaWFuaHVhID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J1xcdTgwNTRcXHU3Y2ZiXFx1NzUzNVxcdThiZGQnKVxcblxcbiAgICBjbGFzcyBNZXRhOlxcbiAgICAgICAgZGJfdGFibGUgPSAnd3VsaXUnXFxuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ1xcdTcyNjlcXHU2ZDQxJ1xcblxcblxcbmNsYXNzIHpoaWxpYW5nZGVuZ2ppKEJhc2VNb2RlbCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBcXHU4ZDI4XFx1OTFjZlxcdTdiNDlcXHU3ZWE3XFx1NmEyMVxcdTU3OGJcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIF9fZG9jX18gPSB1JycnemhpbGlhbmdkZW5namknJydcXG4gICAgX190YWJsZW5hbWVfXyA9ICd6aGlsaWFuZ2RlbmdqaSdcXG4gICAgX19mb3JlRW5kTGlzdF9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN1tmb3JlRW5kTGlzdF1cXHU1MjRkXFx1NTNmMGxpc3Q6XFx1NTQ4Y1xcdTU0MGVcXHU1M2YwXFx1OWVkOFxcdThiYTRcXHU3Njg0bGlzdFxcdTUyMTdcXHU4ODY4XFx1OTg3NVxcdTc2ZjhcXHU0ZjNjLFxcdTUzZWFcXHU2NjJmXFx1NjQ0NlxcdTU3MjhcXHU1MjRkXFx1NTNmMCxcXHU1NDI2OlxcdTYzMDdcXHU2Y2ExXFx1NjcwOVxcdTZiNjRcXHU5ODc1LFxcdTY2MmY6XFx1ODg2OFxcdTc5M2FcXHU2NzA5XFx1NmI2NFxcdTk4NzUoXFx1NGUwZFxcdTk3MDBcXHU4OTgxXFx1NzY3YlxcdTk2NDZcXHU1MzczXFx1NTNlZlxcdTY3ZTVcXHU3NzBiKSxcXHU1MjRkXFx1ODk4MVxcdTc2N2I6XFx1ODg2OFxcdTc5M2FcXHU2NzA5XFx1NmI2NFxcdTk4NzVcXHU0ZTE0XFx1OTcwMFxcdTg5ODFcXHU3NjdiXFx1OTY0NlxcdTU0MGVcXHU2MjRkXFx1ODBmZFxcdTY3ZTVcXHU3NzBiXFxuICAgIF9faXNBZG1pbl9fID0gJ1xcdTU0MjYnICAjIFxcdTg4NjhcXHU1YzVlXFx1NjAyN2lzQWRtaW49XFxcIlxcdTY2MmZcXFwiLFxcdTUyMzdcXHU1MWZhXFx1Njc2NVxcdTc2ODRcXHU3NTI4XFx1NjIzN1xcdTg4NjhcXHU0ZTVmXFx1NjYyZlxcdTdiYTFcXHU3NDA2XFx1NTQ1OFxcdWZmMGNcXHU1MzczcGFnZVxcdTU0OGNsaXN0XFx1NTNlZlxcdTRlZTVcXHU2N2U1XFx1NzcwYlxcdTYyNDBcXHU2NzA5XFx1NGViYVxcdTc2ODRcXHU4MDAzXFx1OGJkNVxcdThiYjBcXHU1ZjU1KFxcdTU0MGNcXHU2NWY2XFx1NWU5NFxcdTc1MjhcXHU0ZThlXFx1NTE3NlxcdTRlZDZcXHU4ODY4KVxcbiAgICBcXG4gICAgYWRkdGltZSA9IG1vZGVscy5EYXRlVGltZUZpZWxkKGF1dG9fbm93X2FkZD1GYWxzZSwgdmVyYm9zZV9uYW1lPXUnXFx1NTIxYlxcdTVlZmFcXHU2NWY2XFx1OTVmNCcpXFxuICAgIHpoaWxpYW5nZGVuZ2ppID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTI1NSwgbnVsbD1GYWxzZSwgdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0nXFx1OGQyOFxcdTkxY2ZcXHU3YjQ5XFx1N2VhNycpXFxuXFxuICAgIGNsYXNzIE1ldGE6XFxuICAgICAgICBkYl90YWJsZSA9ICd6aGlsaWFuZ2RlbmdqaSdcXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSAnXFx1OGQyOFxcdTkxY2ZcXHU3YjQ5XFx1N2VhNydcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIxMS4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgbW9kZWxzLnB5IGZpbGU6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFOMXBuMnJaSmpiZEtvaUVjMVdIVk1XIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjI5OjU1Ljc3N1oiLCJyZXNwb25zZV90ZXh0IjoiMTEuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIG1vZGVscy5weSBmaWxlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6Ijg4ZWM2MGYzLWIyOWUtNDMxYi1hMDdjLWVkNTYwMzYxNDFlMiIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTVtSm5IWmpwWEpQY2M5eXpmc3pOcTkiLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L21haW4vdXNlcnNfdi5weVwiLCBcImZpbGVfY29udGVudFwiOiBcIiMgY29kaW5nOnV0Zi04XFxuXFxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXFxuXFxuZnJvbSAudXNlcnNfbW9kZWwgaW1wb3J0IHVzZXJzXFxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXFxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcXG5cXG5cXG5kZWYgdXNlcnNfbG9naW4ocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBVc2VyIGxvZ2luIGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7J2NvZGUnOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZX1cXG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcXFwicmVxX2RpY3RcXFwiKVxcbiAgICAgICAgXFxuICAgICAgICAjIFJlbW92ZSByb2xlIGlmIHByZXNlbnRcXG4gICAgICAgIGlmIHJlcV9kaWN0LmdldCgncm9sZScpIGlzIG5vdCBOb25lOlxcbiAgICAgICAgICAgIGRlbCByZXFfZGljdFsncm9sZSddXFxuICAgICAgICBcXG4gICAgICAgICMgR2V0IHVzZXIgYnkgcGFyYW1ldGVyc1xcbiAgICAgICAgZGF0YXMgPSB1c2Vycy5nZXRieXBhcmFtcyh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVxcbiAgICAgICAgaWYgbm90IGRhdGFzOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gcGFzc3dvcmRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBtZXMucGFzc3dvcmRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcbiAgICAgICAgIyBTZXQgaWQgYW5kIGF1dGhlbnRpY2F0ZVxcbiAgICAgICAgcmVxX2RpY3RbJ2lkJ10gPSBkYXRhc1swXS5nZXQoJ2lkJylcXG4gICAgICAgIHJldHVybiBBdXRoLmF1dGhlbnRpY2F0ZShBdXRoLCB1c2VycywgcmVxX2RpY3QpXFxuXFxuXFxuZGVmIHVzZXJzX3JlZ2lzdGVyKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgVXNlciByZWdpc3RyYXRpb24gZnVuY3Rpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHsnY29kZSc6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlfVxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJyZXFfZGljdFxcXCIpXFxuXFxuICAgICAgICAjIENyZWF0ZSB1c2VyXFxuICAgICAgICBpZE9yRXJyID0gdXNlcnMuY3JlYXRlYnlyZXEodXNlcnMsIHVzZXJzLCByZXFfZGljdClcXG4gICAgICAgIGlmIGlzaW5zdGFuY2UoaWRPckVyciwgRXhjZXB0aW9uKTpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBpZE9yRXJyXFxuICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddID0gaWRPckVyclxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgdXNlcnNfc2Vzc2lvbihyZXF1ZXN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIEdldCB1c2VyIHNlc3Npb24gaW5mb3JtYXRpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcblxcbiAgICAgICAgIyBHZXQgdXNlciBieSBpZFxcbiAgICAgICAgcmVxX2RpY3QgPSB7XFxcImlkXFxcIjogcmVxdWVzdC5zZXNzaW9uLmdldCgncGFyYW1zJykuZ2V0KFxcXCJpZFxcXCIpfVxcbiAgICAgICAgbXNnWydkYXRhJ10gPSB1c2Vycy5nZXRieXBhcmFtcyh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVswXVxcblxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuXFxuXFxuZGVmIHVzZXJzX2xvZ291dChyZXF1ZXN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIFVzZXIgbG9nb3V0IGZ1bmN0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxuICAgICAgICAgICAgXFxcIm1zZ1xcXCI6IFxcXCJcXHU5MDAwXFx1NTFmYVxcdTYyMTBcXHU1MjlmXFxcIixcXG4gICAgICAgICAgICBcXFwiY29kZVxcXCI6IDBcXG4gICAgICAgIH1cXG5cXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiB1c2Vyc19wYWdlKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgR2V0IHBhZ2luYXRlZCB1c2Vyc1xcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsXFxuICAgICAgICAgICAgICAgXFxcImRhdGFcXFwiOiB7XFxcImN1cnJQYWdlXFxcIjogMSwgXFxcInRvdGFsUGFnZVxcXCI6IDEsIFxcXCJ0b3RhbFxcXCI6IDEsIFxcXCJwYWdlU2l6ZVxcXCI6IDEwLCBcXFwibGlzdFxcXCI6IFtdfX1cXG4gICAgICAgIFxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJyZXFfZGljdFxcXCIpXFxuICAgICAgICB0YWJsZW5hbWUgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJ0YWJsZW5hbWVcXFwiKVxcbiAgICAgICAgXFxuICAgICAgICAjIENoZWNrIGlmIGhhcyBtZXNzYWdlXFxuICAgICAgICB0cnk6XFxuICAgICAgICAgICAgX19oYXNNZXNzYWdlX18gPSB1c2Vycy5fX2hhc01lc3NhZ2VfX1xcbiAgICAgICAgZXhjZXB0OlxcbiAgICAgICAgICAgIF9faGFzTWVzc2FnZV9fID0gTm9uZVxcbiAgICAgICAgXFxuICAgICAgICBpZiBfX2hhc01lc3NhZ2VfXyBhbmQgX19oYXNNZXNzYWdlX18gIT0gXFxcIlxcdTU0MjZcXFwiOlxcbiAgICAgICAgICAgIGlmIHRhYmxlbmFtZSAhPSBcXFwidXNlcnNcXFwiOlxcbiAgICAgICAgICAgICAgICByZXFfZGljdFtcXFwidXNlcmlkXFxcIl0gPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJwYXJhbXNcXFwiKS5nZXQoXFxcImlkXFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgcGFnaW5hdGVkIGRhdGFcXG4gICAgICAgIGlmIHRhYmxlbmFtZSA9PSBcXFwidXNlcnNcXFwiOlxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydsaXN0J10sIG1zZ1snZGF0YSddWydjdXJyUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWxQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbCddLCBcXFxcXFxuICAgICAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSB1c2Vycy5wYWdlKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXFxuICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydsaXN0J10sIG1zZ1snZGF0YSddWydjdXJyUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWxQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbCddLCBcXFxcXFxuICAgICAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSBbXSwgMSwgMCwgMCwgMTBcXG5cXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiB1c2Vyc19pbmZvKHJlcXVlc3QsIGlkXyk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBHZXQgdXNlciBpbmZvcm1hdGlvbiBieSBpZFxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsIFxcXCJkYXRhXFxcIjoge319XFxuXFxuICAgICAgICAjIEdldCB1c2VyIGJ5IGlkXFxuICAgICAgICBkYXRhID0gdXNlcnMuZ2V0YnlpZCh1c2VycywgdXNlcnMsIGludChpZF8pKVxcbiAgICAgICAgaWYgbGVuKGRhdGEpID4gMDpcXG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGRhdGFbMF1cXG4gICAgICAgIFxcbiAgICAgICAgIyBVcGRhdGUgYnJvd3NlIGNsaWNrIGNvdW50IGlmIG5lZWRlZFxcbiAgICAgICAgdHJ5OlxcbiAgICAgICAgICAgIF9fYnJvd3NlQ2xpY2tfXyA9IHVzZXJzLl9fYnJvd3NlQ2xpY2tfX1xcbiAgICAgICAgZXhjZXB0OlxcbiAgICAgICAgICAgIF9fYnJvd3NlQ2xpY2tfXyA9IE5vbmVcXG5cXG4gICAgICAgIGlmIF9fYnJvd3NlQ2xpY2tfXyBhbmQgXFxcImNsaWNrbnVtXFxcIiBpbiB1c2Vycy5nZXRhbGxjb2x1bW4odXNlcnMsIHVzZXJzKTpcXG4gICAgICAgICAgICBjbGlja19kaWN0ID0ge1xcXCJpZFxcXCI6IGludChpZF8pLCBcXFwiY2xpY2tudW1cXFwiOiBzdHIoaW50KGRhdGFbMF0uZ2V0KFxcXCJjbGlja251bVxcXCIsIDApKSArIDEpfVxcbiAgICAgICAgICAgIHJldCA9IHVzZXJzLnVwZGF0ZWJ5cGFyYW1zKHVzZXJzLCB1c2VycywgY2xpY2tfZGljdClcXG4gICAgICAgICAgICBpZiByZXQgaXMgbm90IE5vbmU6XFxuICAgICAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgICAgIG1zZ1snbXNnJ10gPSByZXRcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuXFxuXFxuZGVmIHVzZXJzX3NhdmUocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBTYXZlIG5ldyB1c2VyXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG5cXG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcXFwicmVxX2RpY3RcXFwiKVxcbiAgICAgICAgXFxuICAgICAgICAjIENoZWNrIGlmIHVzZXJuYW1lIGFscmVhZHkgZXhpc3RzXFxuICAgICAgICBpZiB1c2Vycy5vYmplY3RzLmZpbHRlcih1c2VybmFtZT1yZXFfZGljdFsndXNlcm5hbWUnXSkuY291bnQoKSA+IDA6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdThkMjZcXHU2MjM3XFx1NWRmMlxcdTViNThcXHU1NzI4XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnLCBlbmNvZGVyPUN1c3RvbUpzb25FbmNvZGVyKVxcbiAgICAgICAgXFxuICAgICAgICAjIFNldCByb2xlIGFuZCBjcmVhdGUgdXNlclxcbiAgICAgICAgcmVxX2RpY3RbJ3JvbGUnXSA9ICdcXHU3YmExXFx1NzQwNlxcdTU0NTgnXFxuICAgICAgICBpZE9yRXJyID0gdXNlcnMuY3JlYXRlYnlyZXEodXNlcnMsIHVzZXJzLCByZXFfZGljdClcXG4gICAgICAgIFxcbiAgICAgICAgaWYgaXNpbnN0YW5jZShpZE9yRXJyLCBFeGNlcHRpb24pOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IGlkT3JFcnJcXG4gICAgICAgIGVsc2U6XFxuICAgICAgICAgICAgbXNnWydkYXRhJ10gPSBpZE9yRXJyXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiB1c2Vyc191cGRhdGUocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBVcGRhdGUgdXNlciBpbmZvcm1hdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsIFxcXCJkYXRhXFxcIjoge319XFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXFxcInJlcV9kaWN0XFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgIyBIYW5kbGUgcGFzc3dvcmQgZmllbGRzXFxuICAgICAgICBpZiByZXFfZGljdC5nZXQoXFxcIm1pbWFcXFwiKSBhbmQgcmVxX2RpY3QuZ2V0KFxcXCJwYXNzd29yZFxcXCIpOlxcbiAgICAgICAgICAgIGlmIFxcXCJtaW1hXFxcIiBub3QgaW4gdXNlcnMuZ2V0YWxsY29sdW1uKHVzZXJzLCB1c2Vycyk6XFxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcXFwibWltYVxcXCJdXFxuICAgICAgICAgICAgaWYgXFxcInBhc3N3b3JkXFxcIiBub3QgaW4gdXNlcnMuZ2V0YWxsY29sdW1uKHVzZXJzLCB1c2Vycyk6XFxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcXFwicGFzc3dvcmRcXFwiXVxcbiAgICAgICAgXFxuICAgICAgICAjIFJlbW92ZSBjbGlja251bSBpZiBwcmVzZW50XFxuICAgICAgICB0cnk6XFxuICAgICAgICAgICAgZGVsIHJlcV9kaWN0W1xcXCJjbGlja251bVxcXCJdXFxuICAgICAgICBleGNlcHQ6XFxuICAgICAgICAgICAgcGFzc1xcbiAgICAgICAgXFxuICAgICAgICAjIFVwZGF0ZSB1c2VyXFxuICAgICAgICBlcnJvciA9IHVzZXJzLnVwZGF0ZWJ5cGFyYW1zKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXFxuICAgICAgICBpZiBlcnJvciBpcyBub3QgTm9uZTpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBlcnJvclxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgdXNlcnNfZGVsZXRlKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgRGVsZXRlIHVzZXJzIGJ5IGlkc1xcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsIFxcXCJkYXRhXFxcIjoge319XFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXFxcInJlcV9kaWN0XFxcIilcXG5cXG4gICAgICAgICMgRGVsZXRlIHVzZXJzXFxuICAgICAgICBlcnJvciA9IHVzZXJzLmRlbGV0ZXModXNlcnMsIHVzZXJzLCByZXFfZGljdC5nZXQoXFxcImlkc1xcXCIpKVxcbiAgICAgICAgaWYgZXJyb3IgaXMgbm90IE5vbmU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gZXJyb3JcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiMTIuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIHVzZXJzX3YucHkgZmlsZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFoUmY5OThRVjh3NW1xV2VDcWNCVjYifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzE6MzUuOTY1WiIsInJlc3BvbnNlX3RleHQiOiIxMi4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgdXNlcnNfdi5weSBmaWxlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6ImMxZGUxM2QzLTAxMWItNDA4ZS1iZTVhLTBhMzdmMjFhNDBlMyIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVJGQzJVY0NwRUF2UFprNzJONnBDQjUiLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L21haW4vY29uZmlnX3YucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFxcblxcbmltcG9ydCBsb2dnaW5nXFxuXFxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXFxuXFxuZnJvbSAuY29uZmlnX21vZGVsIGltcG9ydCBjb25maWdcXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcXG5mcm9tIHV0aWwgaW1wb3J0IG1lc3NhZ2UgYXMgbWVzXFxuXFxuXFxuZGVmIGNvbmZpZ19wYWdlKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgR2V0IHBhZ2luYXRlZCBjb25maWd1cmF0aW9uIHBhcmFtZXRlcnNcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLFxcbiAgICAgICAgICAgICAgIFxcXCJkYXRhXFxcIjoge1xcXCJjdXJyUGFnZVxcXCI6IDEsIFxcXCJ0b3RhbFBhZ2VcXFwiOiAxLCBcXFwidG90YWxcXFwiOiAxLCBcXFwicGFnZVNpemVcXFwiOiAxMCwgXFxcImxpc3RcXFwiOiBbXX19XFxuICAgICAgICBcXG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldCgncmVxX2RpY3QnKVxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBwYWdpbmF0ZWQgZGF0YVxcbiAgICAgICAgbXNnWydkYXRhJ11bJ2xpc3QnXSwgbXNnWydkYXRhJ11bJ2N1cnJQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbFBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsJ10sIFxcXFxcXG4gICAgICAgIG1zZ1snZGF0YSddWydwYWdlU2l6ZSddID0gY29uZmlnLnBhZ2UoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgY29uZmlnX2xpc3QocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBHZXQgY29uZmlndXJhdGlvbiBsaXN0XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSxcXG4gICAgICAgICAgICAgICBcXFwiZGF0YVxcXCI6IHtcXFwiY3VyclBhZ2VcXFwiOiAxLCBcXFwidG90YWxQYWdlXFxcIjogMSwgXFxcInRvdGFsXFxcIjogMSwgXFxcInBhZ2VTaXplXFxcIjogMTAsIFxcXCJsaXN0XFxcIjogW119fVxcbiAgICAgICAgXFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXFxcInJlcV9kaWN0XFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgcGFnaW5hdGVkIGRhdGFcXG4gICAgICAgIG1zZ1snZGF0YSddWydsaXN0J10sIG1zZ1snZGF0YSddWydjdXJyUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWxQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbCddLCBcXFxcXFxuICAgICAgICBtc2dbJ2RhdGEnXVsncGFnZVNpemUnXSA9IGNvbmZpZy5wYWdlKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcXG5cXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBjb25maWdfaW5mbyhyZXF1ZXN0LCBpZF8pOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgR2V0IGNvbmZpZ3VyYXRpb24gaW5mb3JtYXRpb24gYnkgaWRcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcblxcbiAgICAgICAgIyBHZXQgY29uZmlndXJhdGlvbiBieSBpZFxcbiAgICAgICAgZGF0YSA9IGNvbmZpZy5nZXRieWlkKGNvbmZpZywgY29uZmlnLCBpbnQoaWRfKSlcXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XFxuICAgICAgICAgICAgbXNnWydkYXRhJ10gPSBkYXRhWzBdXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBjb25maWdfZGV0YWlsKHJlcXVlc3QsIGlkXyk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBHZXQgY29uZmlndXJhdGlvbiBkZXRhaWwgYnkgaWRcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcblxcbiAgICAgICAgIyBHZXQgY29uZmlndXJhdGlvbiBieSBpZFxcbiAgICAgICAgZGF0YSA9IGNvbmZpZy5nZXRieWlkKGNvbmZpZywgY29uZmlnLCBpbnQoaWRfKSlcXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XFxuICAgICAgICAgICAgbXNnWydkYXRhJ10gPSBkYXRhWzBdXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBjb25maWdfc2F2ZShyZXF1ZXN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIFNhdmUgbmV3IGNvbmZpZ3VyYXRpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcblxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXFxuICAgICAgICBcXG4gICAgICAgICMgQ2hlY2sgaWYgY29uZmlndXJhdGlvbiBhbHJlYWR5IGV4aXN0c1xcbiAgICAgICAgcGFyYW0xID0gY29uZmlnLmdldGJ5cGFyYW1zKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcXG4gICAgICAgIGlmIHBhcmFtMTpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGlkX2V4aXN0X2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gbWVzLmlkX2V4aXN0X2NvZGVcXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG4gICAgICAgICMgQ3JlYXRlIGNvbmZpZ3VyYXRpb25cXG4gICAgICAgIGVycm9yID0gY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcXG4gICAgICAgIGxvZ2dpbmcud2FybmluZyhcXFwic2F2ZV9jb25maWcucmVzPT09PT09PT09Pnt9XFxcIi5mb3JtYXQoZXJyb3IpKVxcbiAgICAgICAgXFxuICAgICAgICBpZiBlcnJvciBpcyBub3QgTm9uZTpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBlcnJvclxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgY29uZmlnX2FkZChyZXF1ZXN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIEFkZCBuZXcgY29uZmlndXJhdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsIFxcXCJkYXRhXFxcIjoge319XFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXFxcInJlcV9kaWN0XFxcIilcXG5cXG4gICAgICAgICMgQ3JlYXRlIGNvbmZpZ3VyYXRpb25cXG4gICAgICAgIGVycm9yID0gY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcXG4gICAgICAgIGlmIGVycm9yIGlzIG5vdCBOb25lOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IGVycm9yXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBjb25maWdfdXBkYXRlKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgVXBkYXRlIGNvbmZpZ3VyYXRpb25cXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcblxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXFxuXFxuICAgICAgICAjIFVwZGF0ZSBjb25maWd1cmF0aW9uXFxuICAgICAgICBjb25maWcudXBkYXRlYnlwYXJhbXMoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxcblxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuXFxuXFxuZGVmIGNvbmZpZ19kZWxldGUocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBEZWxldGUgY29uZmlndXJhdGlvblxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1xcXCJQT1NUXFxcIiwgXFxcIkdFVFxcXCJdOlxcbiAgICAgICAgbXNnID0ge1xcXCJjb2RlXFxcIjogbm9ybWFsX2NvZGUsIFxcXCJtc2dcXFwiOiBtZXMubm9ybWFsX2NvZGUsIFxcXCJkYXRhXFxcIjoge319XFxuXFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoJ3JlcV9kaWN0JylcXG4gICAgICAgIFxcbiAgICAgICAgIyBEZWxldGUgY29uZmlndXJhdGlvblxcbiAgICAgICAgY29uZmlnLmRlbGV0ZXMoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0LmdldChcXFwiaWRzXFxcIikpXFxuXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIxMy4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgY29uZmlnX3YucHkgZmlsZTpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTVtSm5IWmpwWEpQY2M5eXpmc3pOcTkifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzI6MDcuMjcxWiIsInJlc3BvbnNlX3RleHQiOiIxMy4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgY29uZmlnX3YucHkgZmlsZTpcbiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJmOWZiODZkOC04MzFkLTQ4NzItYjg3My03NzE2ZDg3MjUzMWUiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDExVHZIVDhlaEdYRktDZGJ4cGFNMmFuIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYWluL3NjaGVtYV92LnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5cXG5pbXBvcnQgYmFzZTY0XFxuaW1wb3J0IGNvcHlcXG5pbXBvcnQgZGF0ZXRpbWVcXG5pbXBvcnQganNvblxcbmltcG9ydCBsb2dnaW5nXFxuaW1wb3J0IG9zXFxuaW1wb3J0IHJhbmRvbVxcbmltcG9ydCBzeXNcXG5pbXBvcnQgdGltZVxcbmltcG9ydCB1cmxsaWIucGFyc2VcXG5pbXBvcnQgdXVpZFxcblxcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xcbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXFxuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcXG5mcm9tIGRqYW5nby5mb3Jtcy5tb2RlbHMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2UsIEh0dHBSZXNwb25zZSwgRmlsZVJlc3BvbnNlXFxuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxcblxcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXFxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXFxuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxcbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXFxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2VcXG5mcm9tIC5jb25maWdfbW9kZWwgaW1wb3J0IGNvbmZpZ1xcblxcblxcbmRlZiBzY2hlbWFOYW1lX2NhbChyZXF1ZXN0LCB0YWJsZU5hbWUsIGNvbHVtbk5hbWUpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgQ2FsY3VsYXRlIHN0YXRpc3RpY3MgZm9yIGEgdGFibGUgY29sdW1uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgYWxsIG1vZGVsc1xcbiAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgbW9kZWwgPSBOb25lXFxuICAgICAgICBcXG4gICAgICAgICMgRmluZCB0aGUgbW9kZWwgYnkgdGFibGUgbmFtZVxcbiAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxcbiAgICAgICAgICAgIGlmIG0uX190YWJsZW5hbWVfXyA9PSB0YWJsZU5hbWU6XFxuICAgICAgICAgICAgICAgIG1vZGVsID0gbVxcbiAgICAgICAgICAgICAgICBicmVha1xcbiAgICAgICAgXFxuICAgICAgICBpZiBtb2RlbCBpcyBOb25lOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IFxcXCJcXHU4ODY4XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIENhbGN1bGF0ZSBzdGF0aXN0aWNzXFxuICAgICAgICBjdXJzb3IgPSBjb25uZWN0aW9uLmN1cnNvcigpXFxuICAgICAgICBcXG4gICAgICAgICMgR2V0IGNvbHVtbiB0eXBlXFxuICAgICAgICBjb2x1bW5UeXBlID0gXFxcIlxcXCJcXG4gICAgICAgIGZvciBmaWVsZCBpbiBtb2RlbC5fbWV0YS5maWVsZHM6XFxuICAgICAgICAgICAgaWYgZmllbGQubmFtZSA9PSBjb2x1bW5OYW1lOlxcbiAgICAgICAgICAgICAgICBjb2x1bW5UeXBlID0gZmllbGQuZ2V0X2ludGVybmFsX3R5cGUoKVxcbiAgICAgICAgICAgICAgICBicmVha1xcbiAgICAgICAgXFxuICAgICAgICAjIENhbGN1bGF0ZSBiYXNlZCBvbiBjb2x1bW4gdHlwZVxcbiAgICAgICAgaWYgY29sdW1uVHlwZSBpbiBbXFxcIkludGVnZXJGaWVsZFxcXCIsIFxcXCJCaWdJbnRlZ2VyRmllbGRcXFwiLCBcXFwiRmxvYXRGaWVsZFxcXCIsIFxcXCJEZWNpbWFsRmllbGRcXFwiXTpcXG4gICAgICAgICAgICBjdXJzb3IuZXhlY3V0ZShmXFxcIlNFTEVDVCBTVU0oe2NvbHVtbk5hbWV9KSwgQVZHKHtjb2x1bW5OYW1lfSksIE1BWCh7Y29sdW1uTmFtZX0pLCBNSU4oe2NvbHVtbk5hbWV9KSBGUk9NIHt0YWJsZU5hbWV9XFxcIilcXG4gICAgICAgICAgICBzdW1fdmFsLCBhdmdfdmFsLCBtYXhfdmFsLCBtaW5fdmFsID0gY3Vyc29yLmZldGNob25lKClcXG4gICAgICAgICAgICBcXG4gICAgICAgICAgICBtc2dbJ2RhdGEnXVsnc3VtJ10gPSBzdW1fdmFsXFxuICAgICAgICAgICAgbXNnWydkYXRhJ11bJ2F2ZyddID0gYXZnX3ZhbFxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydtYXgnXSA9IG1heF92YWxcXG4gICAgICAgICAgICBtc2dbJ2RhdGEnXVsnbWluJ10gPSBtaW5fdmFsXFxuICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgIGN1cnNvci5leGVjdXRlKGZcXFwiU0VMRUNUIENPVU5UKERJU1RJTkNUIHtjb2x1bW5OYW1lfSkgRlJPTSB7dGFibGVOYW1lfVxcXCIpXFxuICAgICAgICAgICAgZGlzdGluY3RfY291bnQgPSBjdXJzb3IuZmV0Y2hvbmUoKVswXVxcbiAgICAgICAgICAgIFxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydkaXN0aW5jdENvdW50J10gPSBkaXN0aW5jdF9jb3VudFxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgc2NoZW1hTmFtZV9maWxlX2Rvd25sb2FkKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgRG93bmxvYWQgYSBmaWxlXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJyZXFfZGljdFxcXCIpXFxuICAgICAgICBmaWxlbmFtZSA9IHJlcV9kaWN0LmdldChcXFwiZmlsZU5hbWVcXFwiKVxcbiAgICAgICAgXFxuICAgICAgICBpZiBub3QgZmlsZW5hbWU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTY1ODdcXHU0ZWY2XFx1NTQwZFxcdTRlMGRcXHU4MGZkXFx1NGUzYVxcdTdhN2FcXFwiXFxuICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuICAgICAgICBcXG4gICAgICAgICMgR2V0IGZpbGUgcGF0aFxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL3VwbG9hZFxcXCIsIGZpbGVuYW1lKVxcbiAgICAgICAgXFxuICAgICAgICBpZiBub3Qgb3MucGF0aC5pc2ZpbGUoZmlsZV9wYXRoKTpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBcXFwiXFx1NjU4N1xcdTRlZjZcXHU0ZTBkXFx1NWI1OFxcdTU3MjhcXFwiXFxuICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuICAgICAgICBcXG4gICAgICAgICMgUmV0dXJuIGZpbGUgcmVzcG9uc2VcXG4gICAgICAgIHJlc3BvbnNlID0gRmlsZVJlc3BvbnNlKG9wZW4oZmlsZV9wYXRoLCAncmInKSlcXG4gICAgICAgIHJlc3BvbnNlWydDb250ZW50LVR5cGUnXSA9ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nXFxuICAgICAgICByZXNwb25zZVsnQ29udGVudC1EaXNwb3NpdGlvbiddID0gZidhdHRhY2htZW50O2ZpbGVuYW1lPVxcXCJ7ZmlsZW5hbWV9XFxcIidcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlXFxuXFxuXFxuZGVmIHNjaGVtYU5hbWVfZmlsZV91cGxvYWQocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBVcGxvYWQgYSBmaWxlXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgZmlsZSA9IHJlcXVlc3QuRklMRVMuZ2V0KFxcXCJmaWxlXFxcIilcXG4gICAgICAgIHR5cGUgPSByZXF1ZXN0LlBPU1QuZ2V0KFxcXCJ0eXBlXFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgaWYgbm90IGZpbGU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTY3MmFcXHU0ZTBhXFx1NGYyMFxcdTY1ODdcXHU0ZWY2XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIFNhdmUgZmlsZVxcbiAgICAgICAgZmlsZW5hbWUgPSBmaWxlLm5hbWVcXG4gICAgICAgIGZpbGVzdWZmaXggPSBmaWxlbmFtZS5zcGxpdChcXFwiLlxcXCIpWy0xXVxcbiAgICAgICAgZmlsZV9uYW1lID0gZlxcXCJ7aW50KGZsb2F0KHRpbWUudGltZSgpKSAqIDEwMDApfS57ZmlsZXN1ZmZpeH1cXFwiXFxuICAgICAgICBcXG4gICAgICAgIGlmIHR5cGUgaXMgbm90IE5vbmUgYW5kICdfdGVtcGxhdGUnIGluIHR5cGU6XFxuICAgICAgICAgICAgZmlsZV9uYW1lID0gZlxcXCJ7dHlwZX0ue2ZpbGVzdWZmaXh9XFxcIlxcbiAgICAgICAgXFxuICAgICAgICBmaWxlX3BhdGggPSBvcy5wYXRoLmpvaW4ob3MuZ2V0Y3dkKCksIFxcXCJ0ZW1wbGF0ZXMvdXBsb2FkXFxcIiwgZmlsZV9uYW1lKVxcbiAgICAgICAgXFxuICAgICAgICB3aXRoIG9wZW4oZmlsZV9wYXRoLCAnd2IrJykgYXMgZGVzdGluYXRpb246XFxuICAgICAgICAgICAgZm9yIGNodW5rIGluIGZpbGUuY2h1bmtzKCk6XFxuICAgICAgICAgICAgICAgIGRlc3RpbmF0aW9uLndyaXRlKGNodW5rKVxcbiAgICAgICAgXFxuICAgICAgICBtc2dbXFxcImZpbGVcXFwiXSA9IGZpbGVfbmFtZVxcbiAgICAgICAgXFxuICAgICAgICAjIENoZWNrIGlmIG5lZWQgdG8gc2F2ZSBhcyBmYWNlIHJlY29nbml0aW9uIGJhc2UgcGhvdG9cXG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcXFwicmVxX2RpY3RcXFwiKVxcbiAgICAgICAgdHlwZTEgPSByZXFfZGljdC5nZXQoXFxcInR5cGVcXFwiLCAwKVxcbiAgICAgICAgdHlwZTEgPSBzdHIodHlwZTEpXFxuICAgICAgICBcXG4gICAgICAgIGlmIHR5cGUxID09ICcxJzpcXG4gICAgICAgICAgICBwYXJhbXMgPSB7XFxcIm5hbWVcXFwiOiBcXFwiZmFjZUZpbGVcXFwiLCBcXFwidmFsdWVcXFwiOiBmaWxlX25hbWV9XFxuICAgICAgICAgICAgY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCBwYXJhbXMpXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBzY2hlbWFOYW1lX2ZvbGxvd19sZXZlbChyZXF1ZXN0LCB0YWJsZU5hbWUsIGNvbHVtbk5hbWUsIGxldmVsLCBwYXJlbnQpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgR2V0IGZvbGxvdyBsZXZlbCBkYXRhXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgYWxsIG1vZGVsc1xcbiAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgbW9kZWwgPSBOb25lXFxuICAgICAgICBcXG4gICAgICAgICMgRmluZCB0aGUgbW9kZWwgYnkgdGFibGUgbmFtZVxcbiAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxcbiAgICAgICAgICAgIGlmIG0uX190YWJsZW5hbWVfXyA9PSB0YWJsZU5hbWU6XFxuICAgICAgICAgICAgICAgIG1vZGVsID0gbVxcbiAgICAgICAgICAgICAgICBicmVha1xcbiAgICAgICAgXFxuICAgICAgICBpZiBtb2RlbCBpcyBOb25lOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IFxcXCJcXHU4ODY4XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBmb2xsb3cgbGV2ZWwgZGF0YVxcbiAgICAgICAgZGF0YSA9IG1vZGVsLm9iamVjdHMuZmlsdGVyKCoqe2NvbHVtbk5hbWU6IHBhcmVudH0pLmFsbCgpXFxuICAgICAgICByZXN1bHQgPSBbXVxcbiAgICAgICAgXFxuICAgICAgICBmb3IgaXRlbSBpbiBkYXRhOlxcbiAgICAgICAgICAgIHJlc3VsdC5hcHBlbmQobW9kZWxfdG9fZGljdChpdGVtKSlcXG4gICAgICAgIFxcbiAgICAgICAgbXNnWydkYXRhJ10gPSByZXN1bHRcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuXFxuXFxuZGVmIHNjaGVtYU5hbWVfZm9sbG93KHJlcXVlc3QsIHRhYmxlTmFtZSwgY29sdW1uTmFtZSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBHZXQgZm9sbG93IGRhdGFcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBhbGwgbW9kZWxzXFxuICAgICAgICBhbGxNb2RlbHMgPSBhcHBzLmdldF9hcHBfY29uZmlnKCdtYWluJykuZ2V0X21vZGVscygpXFxuICAgICAgICBtb2RlbCA9IE5vbmVcXG4gICAgICAgIFxcbiAgICAgICAgIyBGaW5kIHRoZSBtb2RlbCBieSB0YWJsZSBuYW1lXFxuICAgICAgICBmb3IgbSBpbiBhbGxNb2RlbHM6XFxuICAgICAgICAgICAgaWYgbS5fX3RhYmxlbmFtZV9fID09IHRhYmxlTmFtZTpcXG4gICAgICAgICAgICAgICAgbW9kZWwgPSBtXFxuICAgICAgICAgICAgICAgIGJyZWFrXFxuICAgICAgICBcXG4gICAgICAgIGlmIG1vZGVsIGlzIE5vbmU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTg4NjhcXHU0ZTBkXFx1NWI1OFxcdTU3MjhcXFwiXFxuICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuICAgICAgICBcXG4gICAgICAgICMgR2V0IGRpc3RpbmN0IHZhbHVlcyBvZiB0aGUgY29sdW1uXFxuICAgICAgICBkYXRhID0gbW9kZWwub2JqZWN0cy52YWx1ZXMoY29sdW1uTmFtZSkuZGlzdGluY3QoKVxcbiAgICAgICAgcmVzdWx0ID0gW11cXG4gICAgICAgIFxcbiAgICAgICAgZm9yIGl0ZW0gaW4gZGF0YTpcXG4gICAgICAgICAgICByZXN1bHQuYXBwZW5kKGl0ZW1bY29sdW1uTmFtZV0pXFxuICAgICAgICBcXG4gICAgICAgIG1zZ1snZGF0YSddID0gcmVzdWx0XFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBzY2hlbWFOYW1lX2xvY2F0aW9uKHJlcXVlc3QpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgR2V0IGxvY2F0aW9uIGRhdGFcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcbiAgICAgICAgXFxuICAgICAgICAjIFRoaXMgaXMgYSBwbGFjZWhvbGRlciBmb3IgbG9jYXRpb24gZGF0YVxcbiAgICAgICAgIyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgdXNlIGEgbG9jYXRpb24gc2VydmljZVxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgc2NoZW1hTmFtZV9tYXRjaGZhY2UocmVxdWVzdCk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBNYXRjaCBmYWNlIHJlY29nbml0aW9uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFxcXCJyZXFfZGljdFxcXCIpXFxuICAgICAgICBmYWNlMSA9IHJlcV9kaWN0LmdldChcXFwiZmFjZTFcXFwiKVxcbiAgICAgICAgZmFjZTIgPSByZXFfZGljdC5nZXQoXFxcImZhY2UyXFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgaWYgbm90IGZhY2UxIG9yIG5vdCBmYWNlMjpcXG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBcXFwiXFx1NGViYVxcdTgxMzhcXHU1NmZlXFx1NzI0N1xcdTRlMGRcXHU4MGZkXFx1NGUzYVxcdTdhN2FcXFwiXFxuICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuICAgICAgICBcXG4gICAgICAgICMgVXNlIEJhaUR1QmNlIGZvciBmYWNlIG1hdGNoaW5nXFxuICAgICAgICBiZGIgPSBCYWlEdUJjZSgpXFxuICAgICAgICByZXN1bHQgPSBiZGIuYmRfY2hlY2sycGljKGZhY2UxLCBmYWNlMilcXG4gICAgICAgIFxcbiAgICAgICAgbXNnWydkYXRhJ10gPSByZXN1bHRcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXFxuXFxuXFxuZGVmIHNjaGVtYU5hbWVfb3B0aW9uKHJlcXVlc3QsIHRhYmxlTmFtZSwgY29sdW1uTmFtZSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBHZXQgb3B0aW9uIGRhdGEgZm9yIGEgY29sdW1uXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgYWxsIG1vZGVsc1xcbiAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgbW9kZWwgPSBOb25lXFxuICAgICAgICBcXG4gICAgICAgICMgRmluZCB0aGUgbW9kZWwgYnkgdGFibGUgbmFtZVxcbiAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxcbiAgICAgICAgICAgIGlmIG0uX190YWJsZW5hbWVfXyA9PSB0YWJsZU5hbWU6XFxuICAgICAgICAgICAgICAgIG1vZGVsID0gbVxcbiAgICAgICAgICAgICAgICBicmVha1xcbiAgICAgICAgXFxuICAgICAgICBpZiBtb2RlbCBpcyBOb25lOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IFxcXCJcXHU4ODY4XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBkaXN0aW5jdCB2YWx1ZXMgb2YgdGhlIGNvbHVtblxcbiAgICAgICAgZGF0YSA9IG1vZGVsLm9iamVjdHMudmFsdWVzKGNvbHVtbk5hbWUpLmRpc3RpbmN0KClcXG4gICAgICAgIHJlc3VsdCA9IFtdXFxuICAgICAgICBcXG4gICAgICAgIGZvciBpdGVtIGluIGRhdGE6XFxuICAgICAgICAgICAgcmVzdWx0LmFwcGVuZChpdGVtW2NvbHVtbk5hbWVdKVxcbiAgICAgICAgXFxuICAgICAgICBtc2dbJ2RhdGEnXSA9IHJlc3VsdFxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG5cXG5kZWYgc2NoZW1hTmFtZV9zaChyZXF1ZXN0LCB0YWJsZU5hbWUpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgSGFuZGxlIGFwcHJvdmFsIHByb2Nlc3NcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcXFwiUE9TVFxcXCIsIFxcXCJHRVRcXFwiXTpcXG4gICAgICAgIG1zZyA9IHtcXFwiY29kZVxcXCI6IG5vcm1hbF9jb2RlLCBcXFwibXNnXFxcIjogbWVzLm5vcm1hbF9jb2RlLCBcXFwiZGF0YVxcXCI6IHt9fVxcbiAgICAgICAgXFxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXFxcInJlcV9kaWN0XFxcIilcXG4gICAgICAgIGlkID0gcmVxX2RpY3QuZ2V0KFxcXCJpZFxcXCIpXFxuICAgICAgICBzZnNoID0gcmVxX2RpY3QuZ2V0KFxcXCJzZnNoXFxcIilcXG4gICAgICAgIHNoaGYgPSByZXFfZGljdC5nZXQoXFxcInNoaGZcXFwiKVxcbiAgICAgICAgXFxuICAgICAgICBpZiBub3QgaWQ6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTdmM2FcXHU1YzExXFx1NTNjMlxcdTY1NzBpZFxcXCJcXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgYWxsIG1vZGVsc1xcbiAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgbW9kZWwgPSBOb25lXFxuICAgICAgICBcXG4gICAgICAgICMgRmluZCB0aGUgbW9kZWwgYnkgdGFibGUgbmFtZVxcbiAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxcbiAgICAgICAgICAgIGlmIG0uX190YWJsZW5hbWVfXyA9PSB0YWJsZU5hbWU6XFxuICAgICAgICAgICAgICAgIG1vZGVsID0gbVxcbiAgICAgICAgICAgICAgICBicmVha1xcbiAgICAgICAgXFxuICAgICAgICBpZiBtb2RlbCBpcyBOb25lOlxcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXFxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IFxcXCJcXHU4ODY4XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIFVwZGF0ZSBhcHByb3ZhbCBzdGF0dXNcXG4gICAgICAgIGRhdGEgPSBtb2RlbC5vYmplY3RzLmdldChpZD1pZClcXG4gICAgICAgIGRhdGEuc2ZzaCA9IHNmc2hcXG4gICAgICAgIGRhdGEuc2hoZiA9IHNoaGZcXG4gICAgICAgIGRhdGEuc2F2ZSgpXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBzY2hlbWFOYW1lX3VwbG9hZChyZXF1ZXN0LCBmaWxlTmFtZSk6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBVcGxvYWQgYSBmaWxlIHdpdGggYSBzcGVjaWZpYyBuYW1lXFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgZmlsZSA9IHJlcXVlc3QuRklMRVMuZ2V0KFxcXCJmaWxlXFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgaWYgbm90IGZpbGU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTY3MmFcXHU0ZTBhXFx1NGYyMFxcdTY1ODdcXHU0ZWY2XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIFNhdmUgZmlsZSB3aXRoIHRoZSBzcGVjaWZpZWQgbmFtZVxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL3VwbG9hZFxcXCIsIGZpbGVOYW1lKVxcbiAgICAgICAgXFxuICAgICAgICB3aXRoIG9wZW4oZmlsZV9wYXRoLCAnd2IrJykgYXMgZGVzdGluYXRpb246XFxuICAgICAgICAgICAgZm9yIGNodW5rIGluIGZpbGUuY2h1bmtzKCk6XFxuICAgICAgICAgICAgICAgIGRlc3RpbmF0aW9uLndyaXRlKGNodW5rKVxcbiAgICAgICAgXFxuICAgICAgICBtc2dbXFxcImZpbGVcXFwiXSA9IGZpbGVOYW1lXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcblxcblxcbmRlZiBzY2hlbWFOYW1lX3VwbG9hZF9mb3JlY2FzdChyZXF1ZXN0LCB0YWJsZU5hbWUsIGZpbGVOYW1lKTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIFVwbG9hZCBhIGZpbGUgZm9yIGZvcmVjYXN0XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXFxcIlBPU1RcXFwiLCBcXFwiR0VUXFxcIl06XFxuICAgICAgICBtc2cgPSB7XFxcImNvZGVcXFwiOiBub3JtYWxfY29kZSwgXFxcIm1zZ1xcXCI6IG1lcy5ub3JtYWxfY29kZSwgXFxcImRhdGFcXFwiOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgZmlsZSA9IHJlcXVlc3QuRklMRVMuZ2V0KFxcXCJmaWxlXFxcIilcXG4gICAgICAgIFxcbiAgICAgICAgaWYgbm90IGZpbGU6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gXFxcIlxcdTY3MmFcXHU0ZTBhXFx1NGYyMFxcdTY1ODdcXHU0ZWY2XFxcIlxcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxcbiAgICAgICAgXFxuICAgICAgICAjIFNhdmUgZmlsZSB3aXRoIHRoZSBzcGVjaWZpZWQgbmFtZVxcbiAgICAgICAgZmlsZV9wYXRoID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwidGVtcGxhdGVzL3VwbG9hZFxcXCIsIGZpbGVOYW1lKVxcbiAgICAgICAgXFxuICAgICAgICB3aXRoIG9wZW4oZmlsZV9wYXRoLCAnd2IrJykgYXMgZGVzdGluYXRpb246XFxuICAgICAgICAgICAgZm9yIGNodW5rIGluIGZpbGUuY2h1bmtzKCk6XFxuICAgICAgICAgICAgICAgIGRlc3RpbmF0aW9uLndyaXRlKGNodW5rKVxcbiAgICAgICAgXFxuICAgICAgICBtc2dbXFxcImZpbGVcXFwiXSA9IGZpbGVOYW1lXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IjE0LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCBzY2hlbWFfdi5weSBmaWxlOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxUkZDMlVjQ3BFQXZQWms3Mk42cENCNSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozMjoyOC4zMTFaIiwicmVzcG9uc2VfdGV4dCI6IjE0LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCBzY2hlbWFfdi5weSBmaWxlOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6ImZhNTBkZTc0LTViODAtNDc5Yy1iYjQ1LWFlMTJkZWM5YmViMSIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVBvNnp5U3c4eWlDRmJkVXl6ZGk5THoiLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L21haW4vdXJscy5weVwiLCBcImZpbGVfY29udGVudFwiOiBcIiMgY29kaW5nOnV0Zi04XFxuXFxuaW1wb3J0IG9zXFxuZnJvbSBkamFuZ28udXJscyBpbXBvcnQgcGF0aFxcbmZyb20gbWFpbiBpbXBvcnQgdXNlcnNfdiwgY29uZmlnX3YsIHNjaGVtYV92XFxuXFxuIyBVUkwgcnVsZXMgbGlzdFxcbnVybHBhdHRlcm5zID0gW1xcbiAgICAjIFVzZXIgcm91dGVzXFxuICAgIHBhdGgocid1c2Vycy9yZWdpc3RlcicsIHVzZXJzX3YudXNlcnNfcmVnaXN0ZXIpLFxcbiAgICBwYXRoKHIndXNlcnMvbG9naW4nLCB1c2Vyc192LnVzZXJzX2xvZ2luKSxcXG4gICAgcGF0aChyJ3VzZXJzL2xvZ291dCcsIHVzZXJzX3YudXNlcnNfbG9nb3V0KSxcXG4gICAgcGF0aChyJ3VzZXJzL3Nlc3Npb24nLCB1c2Vyc192LnVzZXJzX3Nlc3Npb24pLFxcbiAgICBwYXRoKHIndXNlcnMvcGFnZScsIHVzZXJzX3YudXNlcnNfcGFnZSksXFxuICAgIHBhdGgocid1c2Vycy9zYXZlJywgdXNlcnNfdi51c2Vyc19zYXZlKSxcXG4gICAgcGF0aChyJ3VzZXJzL2luZm8vPGlkXz4nLCB1c2Vyc192LnVzZXJzX2luZm8pLFxcbiAgICBwYXRoKHIndXNlcnMvdXBkYXRlJywgdXNlcnNfdi51c2Vyc191cGRhdGUpLFxcbiAgICBwYXRoKHIndXNlcnMvZGVsZXRlJywgdXNlcnNfdi51c2Vyc19kZWxldGUpLFxcblxcbiAgICAjIENvbmZpZyByb3V0ZXNcXG4gICAgcGF0aChyJ2NvbmZpZy9wYWdlJywgY29uZmlnX3YuY29uZmlnX3BhZ2UpLFxcbiAgICBwYXRoKHInY29uZmlnL2xpc3QnLCBjb25maWdfdi5jb25maWdfbGlzdCksXFxuICAgIHBhdGgocidjb25maWcvc2F2ZScsIGNvbmZpZ192LmNvbmZpZ19zYXZlKSxcXG4gICAgcGF0aChyJ2NvbmZpZy9hZGQnLCBjb25maWdfdi5jb25maWdfYWRkKSxcXG4gICAgcGF0aChyJ2NvbmZpZy9pbmZvLzxpZF8+JywgY29uZmlnX3YuY29uZmlnX2luZm8pLFxcbiAgICBwYXRoKHInY29uZmlnL2RldGFpbC88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19kZXRhaWwpLFxcbiAgICBwYXRoKHInY29uZmlnL3VwZGF0ZScsIGNvbmZpZ192LmNvbmZpZ191cGRhdGUpLFxcbiAgICBwYXRoKHInY29uZmlnL2RlbGV0ZScsIGNvbmZpZ192LmNvbmZpZ19kZWxldGUpLFxcbl1cXG5cXG4jIE1haW4gYXBwIHBhdGhcXG5tYWluRGlyID0gb3MucGF0aC5qb2luKG9zLmdldGN3ZCgpLCBcXFwibWFpblxcXCIpXFxuXFxuIyBGaWxlcyB0byBleGNsdWRlIGZyb20gYXV0by1sb2FkaW5nXFxuZXhjbHVkZUxpc3QgPSBbXFxuICAgIFxcXCJzY2hlbWFfdi5weVxcXCIsXFxuICAgIFxcXCJ1c2Vyc192LnB5XFxcIixcXG4gICAgXFxcImNvbmZpZ192LnB5XFxcIixcXG5dXFxuXFxuIyBEeW5hbWljYWxseSBsb2FkIHZpZXcgbW9kdWxlc1xcbnZpZXdfdHVwbGUgPSBzZXQoKVxcbmZvciBpIGluIG9zLmxpc3RkaXIobWFpbkRpcik6XFxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpLmVuZHN3aXRoKFxcXCJfdi5weVxcXCIpOlxcbiAgICAgICAgdmlld05hbWUgPSBpWzotM10gICMgUmVtb3ZlIC5weSBzdWZmaXhcXG4gICAgICAgIHZpZXdfdHVwbGUuYWRkKFxcXCJmcm9tIG1haW4gaW1wb3J0IHt9XFxcIi5mb3JtYXQodmlld05hbWUpKVxcblxcbiMgRXhlY3V0ZSBpbXBvcnRzXFxuZm9yIGkgaW4gdmlld190dXBsZTpcXG4gICAgZXhlYyhpKVxcblxcbiMgRHluYW1pY2FsbHkgYWRkIHJvdXRlcyBmb3IgZWFjaCBtb2RlbFxcbmZvciBpIGluIG9zLmxpc3RkaXIobWFpbkRpcik6XFxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpLmVuZHN3aXRoKFxcXCJfdi5weVxcXCIpOlxcbiAgICAgICAgdGFibGVOYW1lID0gaVs6LTVdICAjIFJlbW92ZSBfdi5weSBzdWZmaXhcXG4gICAgICAgIFxcbiAgICAgICAgIyBTa2lwIGlmIG5vdCBhIG1vZGVsIHZpZXcgZmlsZVxcbiAgICAgICAgaWYgdGFibGVOYW1lID09IFxcXCJzY2hlbWFcXFwiOlxcbiAgICAgICAgICAgIGNvbnRpbnVlXFxuICAgICAgICBcXG4gICAgICAgICMgQWRkIHN0YW5kYXJkIHJvdXRlcyBmb3IgZWFjaCBtb2RlbFxcbiAgICAgICAgdXJscGF0dGVybnMuZXh0ZW5kKFtcXG4gICAgICAgICAgICBwYXRoKHIne30vcmVnaXN0ZXInLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXFxuICAgICAgICAgICAgICAgICBldmFsKFxcXCJ7fV92Lnt9X3JlZ2lzdGVyXFxcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXFxuICAgICAgICAgICAgcGF0aChyJ3t9L2xvZ2luJy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV9sb2dpblxcXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxcbiAgICAgICAgICAgIHBhdGgocid7fS9sb2dvdXQnLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXFxuICAgICAgICAgICAgICAgICBldmFsKFxcXCJ7fV92Lnt9X2xvZ291dFxcXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxcbiAgICAgICAgICAgIHBhdGgocid7fS9yZXNldFBhc3MnLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXFxuICAgICAgICAgICAgICAgICBldmFsKFxcXCJ7fV92Lnt9X3Jlc2V0UGFzc1xcXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxcbiAgICAgICAgICAgIHBhdGgocid7fS9zZXNzaW9uJy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV9zZXNzaW9uXFxcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXFxuICAgICAgICAgICAgcGF0aChyJ3t9L2RlZmF1bHQnLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXFxuICAgICAgICAgICAgICAgICBldmFsKFxcXCJ7fV92Lnt9X2RlZmF1bHRcXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgICAgICBwYXRoKHIne30vcGFnZScuZm9ybWF0KHRhYmxlTmFtZS5sb3dlcigpKSxcXG4gICAgICAgICAgICAgICAgIGV2YWwoXFxcInt9X3Yue31fcGFnZVxcXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxcbiAgICAgICAgICAgIHBhdGgocid7fS9hdXRvU29ydCcuZm9ybWF0KHRhYmxlTmFtZS5sb3dlcigpKSxcXG4gICAgICAgICAgICAgICAgIGV2YWwoXFxcInt9X3Yue31fYXV0b1NvcnRcXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgICAgICBwYXRoKHIne30vbGlzdCcuZm9ybWF0KHRhYmxlTmFtZS5sb3dlcigpKSxcXG4gICAgICAgICAgICAgICAgIGV2YWwoXFxcInt9X3Yue31fbGlzdFxcXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxcbiAgICAgICAgICAgIHBhdGgocid7fS9zYXZlJy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV9zYXZlXFxcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXFxuICAgICAgICAgICAgcGF0aChyJ3t9L2FkZCcuZm9ybWF0KHRhYmxlTmFtZS5sb3dlcigpKSxcXG4gICAgICAgICAgICAgICAgIGV2YWwoXFxcInt9X3Yue31fYWRkXFxcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXFxuICAgICAgICAgICAgcGF0aChyJ3t9L2luZm8vPGlkXz4nLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXFxuICAgICAgICAgICAgICAgICBldmFsKFxcXCJ7fV92Lnt9X2luZm9cXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgICAgICBwYXRoKHIne30vZGV0YWlsLzxpZF8+Jy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV9kZXRhaWxcXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgICAgICBwYXRoKHIne30vdXBkYXRlJy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV91cGRhdGVcXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgICAgICBwYXRoKHIne30vZGVsZXRlJy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxcbiAgICAgICAgICAgICAgICAgZXZhbChcXFwie31fdi57fV9kZWxldGVcXFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcXG4gICAgICAgIF0pXFxuXFxuIyBBZGQgc2NoZW1hIHJvdXRlc1xcbnVybHBhdHRlcm5zLmV4dGVuZChbXFxuICAgIHBhdGgocidjYWwvPHN0cjp0YWJsZU5hbWU+LzxzdHI6Y29sdW1uTmFtZT4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2NhbCksXFxuICAgIHBhdGgocidmaWxlL2Rvd25sb2FkJywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9maWxlX2Rvd25sb2FkKSxcXG4gICAgcGF0aChyJ2ZpbGUvdXBsb2FkJywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9maWxlX3VwbG9hZCksXFxuICAgIHBhdGgocidmb2xsb3cvPHRhYmxlTmFtZT4vPGNvbHVtbk5hbWU+LzxsZXZlbD4vPHBhcmVudD4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZvbGxvd19sZXZlbCksXFxuICAgIHBhdGgocidmb2xsb3cvPHRhYmxlTmFtZT4vPGNvbHVtbk5hbWU+Jywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9mb2xsb3cpLFxcbiAgICBwYXRoKHInbG9jYXRpb24nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2xvY2F0aW9uKSxcXG4gICAgcGF0aChyJ21hdGNoRmFjZScsIHNjaGVtYV92LnNjaGVtYU5hbWVfbWF0Y2hmYWNlKSxcXG4gICAgcGF0aChyJ29wdGlvbi88dGFibGVOYW1lPi88Y29sdW1uTmFtZT4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX29wdGlvbiksXFxuICAgIHBhdGgocidzaC88dGFibGVOYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfc2gpLFxcbiAgICBwYXRoKHIndXBsb2FkLzxmaWxlTmFtZT4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX3VwbG9hZCksXFxuICAgIHBhdGgocid1cGxvYWQvPHRhYmxlTmFtZT4vPGZpbGVOYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfdXBsb2FkX2ZvcmVjYXN0KSxcXG5dKVwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IjE1LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB1cmxzLnB5IGZpbGUgZm9yIHRoZSBtYWluIGFwcDpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTFUdkhUOGVoR1hGS0NkYnhwYU0yYW4ifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzM6MTYuODk4WiIsInJlc3BvbnNlX3RleHQiOiIxNS4gTm93LCBsZXQncyBjcmVhdGUgdGhlIHJlZmFjdG9yZWQgdXJscy5weSBmaWxlIGZvciB0aGUgbWFpbiBhcHA6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiNmQ1YjY3OTEtNDMxMy00YjIxLWJlYTItNTgzOWFlYzlkY2ZmIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxVk5VRFlqYXJqVW91Z2JNUFpybmRkWiIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvdXRpbC9hdXRoLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5pbXBvcnQgYmFzZTY0XFxuaW1wb3J0IGNvcHlcXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXFxuXFxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXFxuZnJvbSB1dGlsIGltcG9ydCBtZXNzYWdlIGFzIG1lc1xcblxcblxcbmNsYXNzIEF1dGgob2JqZWN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIEF1dGhlbnRpY2F0aW9uIHV0aWxpdHkgY2xhc3NcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGRlZiBhdXRoZW50aWNhdGUoc2VsZiwgbW9kZWwsIHJlcV9kaWN0KTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgVXNlciBsb2dpbiwgcmV0dXJucyB0b2tlbiBvbiBzdWNjZXNzLCBmYWlsdXJlIHJlYXNvbiBvbiBmYWlsdXJlXFxuICAgICAgICA6cGFyYW0gbW9kZWw6IFVzZXIgbW9kZWxcXG4gICAgICAgIDpwYXJhbSByZXFfZGljdDogUmVxdWVzdCBwYXJhbWV0ZXJzXFxuICAgICAgICA6cmV0dXJuOiBKc29uUmVzcG9uc2VcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgbXNnID0geydjb2RlJzogbm9ybWFsX2NvZGUsICdtc2cnOiBtZXMubm9ybWFsX2NvZGUsICdkYXRhJzoge319XFxuICAgICAgICB0YWJsZW5hbWUgPSBtb2RlbC5fX3RhYmxlbmFtZV9fXFxuICAgICAgICBlbmNvZGVfZGljdCA9IHtcXFwidGFibGVuYW1lXFxcIjogdGFibGVuYW1lLCBcXFwicGFyYW1zXFxcIjogcmVxX2RpY3R9XFxuXFxuICAgICAgICAjIEVuY29kZSB0b2tlblxcbiAgICAgICAgZW5jb2RlX3N0ciA9IGJhc2U2NC5iNjRlbmNvZGUoc3RyKGVuY29kZV9kaWN0KS5lbmNvZGUoXFxcInV0Zi04XFxcIikpXFxuICAgICAgICBtc2dbJ2RhdGEnXVtcXFwiaWRcXFwiXSA9IHJlcV9kaWN0LmdldChcXFwiaWRcXFwiKVxcbiAgICAgICAgbXNnW1xcXCJpZFxcXCJdID0gcmVxX2RpY3QuZ2V0KFxcXCJpZFxcXCIpXFxuICAgICAgICBtc2dbJ3Rva2VuJ10gPSBlbmNvZGVfc3RyLmRlY29kZSgndXRmLTgnKVxcbiAgICAgICAgXFxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcXG5cXG4gICAgZGVmIGdldF90b2tlbihzZWxmLCBtb2RlbCwgcmVxX2RpY3QpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBHZW5lcmF0ZSB0b2tlbiBmb3IgdXNlclxcbiAgICAgICAgOnBhcmFtIG1vZGVsOiBVc2VyIG1vZGVsXFxuICAgICAgICA6cGFyYW0gcmVxX2RpY3Q6IFJlcXVlc3QgcGFyYW1ldGVyc1xcbiAgICAgICAgOnJldHVybjogVG9rZW4gc3RyaW5nXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHRhYmxlbmFtZSA9IG1vZGVsLl9fdGFibGVuYW1lX19cXG4gICAgICAgIGVuY29kZV9kaWN0ID0ge1xcXCJ0YWJsZW5hbWVcXFwiOiB0YWJsZW5hbWUsIFxcXCJwYXJhbXNcXFwiOiByZXFfZGljdH1cXG5cXG4gICAgICAgIGVuY29kZV9zdHIgPSBiYXNlNjQuYjY0ZW5jb2RlKHN0cihlbmNvZGVfZGljdCkuZW5jb2RlKFxcXCJ1dGY4XFxcIikpXFxuICAgICAgICByZXR1cm4gZW5jb2RlX3N0ci5kZWNvZGUoJ3V0Zi04JylcXG5cXG4gICAgZGVmIGlkZW50aWZ5KHNlbGYsIHJlcXVlc3QpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBVc2VyIGF1dGhlbnRpY2F0aW9uXFxuICAgICAgICA6cGFyYW0gcmVxdWVzdDogUmVxdWVzdCBvYmplY3RcXG4gICAgICAgIDpyZXR1cm46IEF1dGhlbnRpY2F0aW9uIHJlc3VsdFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBtc2cgPSB7J2NvZGUnOiBub3JtYWxfY29kZSwgJ21zZyc6IG1lcy5ub3JtYWxfY29kZSwgJ2RhdGEnOiB7fX1cXG4gICAgICAgIFxcbiAgICAgICAgIyBHZXQgdG9rZW4gZnJvbSBoZWFkZXJcXG4gICAgICAgIHRva2VuID0gcmVxdWVzdC5NRVRBLmdldCgnSFRUUF9UT0tFTicpXFxuXFxuICAgICAgICBpZiB0b2tlbiBhbmQgdG9rZW4gIT0gXFxcIm51bGxcXFwiOlxcbiAgICAgICAgICAgIGF1dGhfdG9rZW4gPSBjb3B5LmRlZXBjb3B5KHRva2VuKVxcblxcbiAgICAgICAgICAgICMgRGVjb2RlIHRva2VuXFxuICAgICAgICAgICAgZGVjb2RlX3N0ciA9IGJhc2U2NC5iNjRkZWNvZGUoYXV0aF90b2tlbikuZGVjb2RlKFxcXCJ1dGY4XFxcIilcXG4gICAgICAgICAgICBkZWNvZGVfc3RyID0gZGVjb2RlX3N0ci5yZXBsYWNlKCdcXFwibnVsbFxcXCInLCAnXFxcIlxcXCInKS5yZXBsYWNlKCdudWxsJywgJ1xcXCJcXFwiJylcXG4gICAgICAgICAgICBkZWNvZGVfZGljdCA9IGV2YWwoZGVjb2RlX3N0cilcXG5cXG4gICAgICAgICAgICB0YWJsZW5hbWUyID0gZGVjb2RlX2RpY3QuZ2V0KFxcXCJ0YWJsZW5hbWVcXFwiKVxcbiAgICAgICAgICAgIHBhcmFtczIgPSBkZWNvZGVfZGljdC5nZXQoXFxcInBhcmFtc1xcXCIsIHt9KVxcblxcbiAgICAgICAgICAgICMgRmluZCB1c2VyIGluIGRhdGFiYXNlXFxuICAgICAgICAgICAgZGF0YXMgPSBOb25lXFxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgICAgIGZvciBtb2RlbCBpbiBhbGxNb2RlbHM6XFxuICAgICAgICAgICAgICAgIGlmIG1vZGVsLl9fdGFibGVuYW1lX18gPT0gdGFibGVuYW1lMjpcXG4gICAgICAgICAgICAgICAgICAgIGRhdGFzID0gbW9kZWwuZ2V0YnlwYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMyKVxcblxcbiAgICAgICAgICAgIGlmIG5vdCBkYXRhczpcXG4gICAgICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcXG4gICAgICAgICAgICAgICAgbXNnWydtc2cnXSA9ICdcXHU2MjdlXFx1NGUwZFxcdTUyMzBcXHU4YmU1XFx1NzUyOFxcdTYyMzdcXHU0ZmUxXFx1NjA2ZidcXG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gbXNnXFxuICAgICAgICAgICAgZWxzZTpcXG4gICAgICAgICAgICAgICAgcmVxdWVzdC5zZXNzaW9uWyd0YWJsZW5hbWUnXSA9IHRhYmxlbmFtZTJcXG4gICAgICAgICAgICAgICAgcmVxdWVzdC5zZXNzaW9uWydwYXJhbXMnXSA9IHBhcmFtczJcXG4gICAgICAgICAgICAgICAgbXNnWydtc2cnXSA9ICdcXHU4ZWFiXFx1NGVmZFxcdTlhOGNcXHU4YmMxXFx1OTAxYVxcdThmYzdcXHUzMDAyJ1xcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBtc2dcXG4gICAgICAgIGVsc2U6XFxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gJ2hlYWRlcnNcXHU2NzJhXFx1NTMwNVxcdTU0MmJcXHU4YmE0XFx1OGJjMVxcdTRmZTFcXHU2MDZmXFx1MzAwMidcXG4gICAgICAgICAgICByZXN1bHQgPSBtc2dcXG4gICAgICAgIFxcbiAgICAgICAgcmV0dXJuIHJlc3VsdFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IjE2LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB1dGlsaXR5IGZpbGVzOlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxUG82enlTdzh5aUNGYmRVeXpkaTlMeiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozMzo0NC41NjVaIiwicmVzcG9uc2VfdGV4dCI6IjE2LiBOb3csIGxldCdzIGNyZWF0ZSB0aGUgcmVmYWN0b3JlZCB1dGlsaXR5IGZpbGVzOlxuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX0seyJzdGF0dXMiOiJzdWNjZXNzIiwicmVxdWVzdF9pZCI6IjJhMWRjZTBjLTZmMzYtNGM3Zi04MTJmLWZmODM5OWUzODZkOCIsInJlcXVlc3RfbWVzc2FnZSI6IiIsInN0cnVjdHVyZWRfb3V0cHV0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6NSwiY29udGVudCI6IiIsInRvb2xfdXNlIjp7InRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUpZbkZDdEwxWnpDeEpSelE4cG9pcXciLCJ0b29sX25hbWUiOiJzYXZlLWZpbGUiLCJpbnB1dF9qc29uIjoie1wiZmlsZV9wYXRoXCI6IFwibmV3L3V0aWwvY29kZXMucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFxcblxcXCJcXFwiXFxcIlxcblN0YXR1cyBjb2RlcyBmb3IgQVBJIHJlc3BvbnNlc1xcblxcXCJcXFwiXFxcIlxcbm5vcm1hbF9jb2RlID0gMCAgIyBOb3JtYWxcXG5yZWRpcmVjdF9jb2RlID0gMzAxICAjIFJlZGlyZWN0XFxudGVtcG9yYXJ5X3JlZGlyZWN0X2NvZGUgPSAzMDIgICMgVGVtcG9yYXJ5IHJlZGlyZWN0XFxuXFxuZW1wdHlfcGFyYW1fY29kZSA9IDEwMDAxICAjIEVtcHR5IG9yIGluY29ycmVjdCByZXF1ZXN0IHBhcmFtZXRlclxcbnZhbGlkYXRlX3BhcmFtX2NvZGUgPSAxMDAwMiAgIyBOb24tc3RhbmRhcmQgcmVxdWVzdCBwYXJhbWV0ZXJcXG5pc19ub3RfanNvbl9jb2RlID0gMTAwMDMgICMgRGF0YSBmb3JtYXQgaXMgbm90IEpTT05cXG5cXG5vdGhlcl9jb2RlID0gMTAwMjAgICMgT3RoZXIgZXJyb3JcXG5jcnVkX2Vycm9yX2NvZGUgPSAxMDAyMSAgIyBEYXRhYmFzZSBvcGVyYXRpb24gZmFpbGVkXFxuaGVhZGVyX2Vycm9yX2NvZGUgPSAxMDAyMyAgIyBIZWFkZXIgZXJyb3JcXG5jYXB0Y2hhX2Vycm9yX2NvZGUgPSAxMDAyNCAgIyBDYXB0Y2hhIGVycm9yXFxuaWRfZXhpc3RfY29kZSA9IDEwMDI1ICAjIElEIG9yIHVzZXJuYW1lIGFscmVhZHkgZXhpc3RzXFxuaWRfbm90ZXhpc3RfY29kZSA9IDEwMDI2ICAjIElEIG9yIHVzZXJuYW1lIGRvZXMgbm90IGV4aXN0XFxudXNlcm5hbWVfZXJyb3JfY29kZSA9IDEwMDI3ICAjIFVzZXJuYW1lIGVycm9yXFxucGFzc3dvcmRfZXJyb3JfY29kZSA9IDEwMDI4ICAjIFBhc3N3b3JkIGVycm9yXFxuZmlsZV9ub3RleGlzdF9jb2RlID0gMTAwMjkgICMgVXBsb2FkIGZpbGUgZG9lcyBub3QgZXhpc3RcXG5cXG50b2tlbl9lcnJvcl9jb2RlID0gMjAwMDEgICMgVG9rZW4gZXJyb3JcXG50b2tlbl9leHBpcmVkX2NvZGUgPSAyMDAwMiAgIyBUb2tlbiBleHBpcmVkXFxubm9uX2F1dGhvcml6ZWRfY29kZSA9IDIwMDAzICAjIE5vIHBlcm1pc3Npb25cXG5cXG5zeXN0ZW1fZXJyb3JfY29kZSA9IDQwMDAxICAjIFN5c3RlbS1sZXZlbCBlcnJvclxcbnJlcXVlc3RfZXhwaXJlZF9jb2RlID0gNDAwMDIgICMgUmVxdWVzdCBleHBpcmVkXFxucmVwZWF0ZWRfcmVxdWVzdF9jb2RlID0gNDAwMDMgICMgUmVwZWF0ZWQgcmVxdWVzdFwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMVZOVURZamFyalVvdWdiTVBacm5kZFoifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzQ6MDIuODgwWiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMzkyNWFlOWEtNTIyZi00NzliLWE5YzgtODEyMmY0ZDIxMWU3IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxODVGRTRGckxuYlhzOFBVUjhod1JiRSIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvdXRpbC9jb21tb24ucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIGNvZGluZzp1dGYtOFxcbmltcG9ydCBoYXNobGliXFxuaW1wb3J0IGJhc2U2NFxcbmZyb20gQ3J5cHRvLkNpcGhlciBpbXBvcnQgREVTLCBBRVNcXG5mcm9tIENyeXB0by5VdGlsLlBhZGRpbmcgaW1wb3J0IHBhZCwgdW5wYWRcXG5cXG5cXG5jbGFzcyBDb21tb24ob2JqZWN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIENvbW1vbiB1dGlsaXR5IGZ1bmN0aW9uc1xcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgZGVmIG1kNShzZWxmLCBwd2QpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBDYWxjdWxhdGUgTUQ1IGhhc2ggb2YgYSBzdHJpbmdcXG4gICAgICAgIDpwYXJhbSBwd2Q6IFN0cmluZyB0byBoYXNoXFxuICAgICAgICA6cmV0dXJuOiBNRDUgaGFzaCBzdHJpbmdcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgbWQ1ID0gaGFzaGxpYi5tZDUoKVxcbiAgICAgICAgbWQ1LnVwZGF0ZShwd2QuZW5jb2RlKCkpXFxuICAgICAgICByZXR1cm4gbWQ1LmhleGRpZ2VzdCgpXFxuICAgIFxcbiAgICBkZWYgZW5jcnlwdF9kZXMoc2VsZiwgdGV4dCwga2V5KTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgRW5jcnlwdCB0ZXh0IHVzaW5nIERFU1xcbiAgICAgICAgOnBhcmFtIHRleHQ6IFRleHQgdG8gZW5jcnlwdFxcbiAgICAgICAgOnBhcmFtIGtleTogRW5jcnlwdGlvbiBrZXlcXG4gICAgICAgIDpyZXR1cm46IEVuY3J5cHRlZCB0ZXh0XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgICMgRW5zdXJlIGtleSBpcyA4IGJ5dGVzXFxuICAgICAgICBrZXkgPSBrZXkubGp1c3QoOCwgJ1xcXFwwJylbOjhdLmVuY29kZSgpXFxuICAgICAgICBcXG4gICAgICAgICMgQ3JlYXRlIGNpcGhlclxcbiAgICAgICAgY2lwaGVyID0gREVTLm5ldyhrZXksIERFUy5NT0RFX0VDQilcXG4gICAgICAgIFxcbiAgICAgICAgIyBQYWQgdGV4dCBhbmQgZW5jcnlwdFxcbiAgICAgICAgcGFkZGVkX3RleHQgPSBwYWQodGV4dC5lbmNvZGUoKSwgREVTLmJsb2NrX3NpemUpXFxuICAgICAgICBlbmNyeXB0ZWRfdGV4dCA9IGNpcGhlci5lbmNyeXB0KHBhZGRlZF90ZXh0KVxcbiAgICAgICAgXFxuICAgICAgICAjIFJldHVybiBiYXNlNjQgZW5jb2RlZCBzdHJpbmdcXG4gICAgICAgIHJldHVybiBiYXNlNjQuYjY0ZW5jb2RlKGVuY3J5cHRlZF90ZXh0KS5kZWNvZGUoKVxcbiAgICBcXG4gICAgZGVmIGRlY3J5cHRfZGVzKHNlbGYsIGVuY3J5cHRlZF90ZXh0LCBrZXkpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBEZWNyeXB0IHRleHQgdXNpbmcgREVTXFxuICAgICAgICA6cGFyYW0gZW5jcnlwdGVkX3RleHQ6IEVuY3J5cHRlZCB0ZXh0XFxuICAgICAgICA6cGFyYW0ga2V5OiBEZWNyeXB0aW9uIGtleVxcbiAgICAgICAgOnJldHVybjogRGVjcnlwdGVkIHRleHRcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgIyBFbnN1cmUga2V5IGlzIDggYnl0ZXNcXG4gICAgICAgIGtleSA9IGtleS5sanVzdCg4LCAnXFxcXDAnKVs6OF0uZW5jb2RlKClcXG4gICAgICAgIFxcbiAgICAgICAgIyBDcmVhdGUgY2lwaGVyXFxuICAgICAgICBjaXBoZXIgPSBERVMubmV3KGtleSwgREVTLk1PREVfRUNCKVxcbiAgICAgICAgXFxuICAgICAgICAjIERlY29kZSBiYXNlNjQgYW5kIGRlY3J5cHRcXG4gICAgICAgIGVuY3J5cHRlZF9ieXRlcyA9IGJhc2U2NC5iNjRkZWNvZGUoZW5jcnlwdGVkX3RleHQpXFxuICAgICAgICBkZWNyeXB0ZWRfYnl0ZXMgPSBjaXBoZXIuZGVjcnlwdChlbmNyeXB0ZWRfYnl0ZXMpXFxuICAgICAgICBcXG4gICAgICAgICMgVW5wYWQgYW5kIHJldHVyblxcbiAgICAgICAgdHJ5OlxcbiAgICAgICAgICAgIHVucGFkZGVkX2J5dGVzID0gdW5wYWQoZGVjcnlwdGVkX2J5dGVzLCBERVMuYmxvY2tfc2l6ZSlcXG4gICAgICAgICAgICByZXR1cm4gdW5wYWRkZWRfYnl0ZXMuZGVjb2RlKClcXG4gICAgICAgIGV4Y2VwdDpcXG4gICAgICAgICAgICByZXR1cm4gZGVjcnlwdGVkX2J5dGVzLmRlY29kZSgpXFxuICAgIFxcbiAgICBkZWYgZW5jcnlwdF9hZXMoc2VsZiwgdGV4dCwga2V5KTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgRW5jcnlwdCB0ZXh0IHVzaW5nIEFFU1xcbiAgICAgICAgOnBhcmFtIHRleHQ6IFRleHQgdG8gZW5jcnlwdFxcbiAgICAgICAgOnBhcmFtIGtleTogRW5jcnlwdGlvbiBrZXlcXG4gICAgICAgIDpyZXR1cm46IEVuY3J5cHRlZCB0ZXh0XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgICMgRW5zdXJlIGtleSBpcyAxNiwgMjQsIG9yIDMyIGJ5dGVzXFxuICAgICAgICBrZXkgPSBrZXkubGp1c3QoMTYsICdcXFxcMCcpWzoxNl0uZW5jb2RlKClcXG4gICAgICAgIFxcbiAgICAgICAgIyBDcmVhdGUgY2lwaGVyXFxuICAgICAgICBjaXBoZXIgPSBBRVMubmV3KGtleSwgQUVTLk1PREVfRUNCKVxcbiAgICAgICAgXFxuICAgICAgICAjIFBhZCB0ZXh0IGFuZCBlbmNyeXB0XFxuICAgICAgICBwYWRkZWRfdGV4dCA9IHBhZCh0ZXh0LmVuY29kZSgpLCBBRVMuYmxvY2tfc2l6ZSlcXG4gICAgICAgIGVuY3J5cHRlZF90ZXh0ID0gY2lwaGVyLmVuY3J5cHQocGFkZGVkX3RleHQpXFxuICAgICAgICBcXG4gICAgICAgICMgUmV0dXJuIGJhc2U2NCBlbmNvZGVkIHN0cmluZ1xcbiAgICAgICAgcmV0dXJuIGJhc2U2NC5iNjRlbmNvZGUoZW5jcnlwdGVkX3RleHQpLmRlY29kZSgpXFxuICAgIFxcbiAgICBkZWYgZGVjcnlwdF9hZXMoc2VsZiwgZW5jcnlwdGVkX3RleHQsIGtleSk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIERlY3J5cHQgdGV4dCB1c2luZyBBRVNcXG4gICAgICAgIDpwYXJhbSBlbmNyeXB0ZWRfdGV4dDogRW5jcnlwdGVkIHRleHRcXG4gICAgICAgIDpwYXJhbSBrZXk6IERlY3J5cHRpb24ga2V5XFxuICAgICAgICA6cmV0dXJuOiBEZWNyeXB0ZWQgdGV4dFxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICAjIEVuc3VyZSBrZXkgaXMgMTYsIDI0LCBvciAzMiBieXRlc1xcbiAgICAgICAga2V5ID0ga2V5LmxqdXN0KDE2LCAnXFxcXDAnKVs6MTZdLmVuY29kZSgpXFxuICAgICAgICBcXG4gICAgICAgICMgQ3JlYXRlIGNpcGhlclxcbiAgICAgICAgY2lwaGVyID0gQUVTLm5ldyhrZXksIEFFUy5NT0RFX0VDQilcXG4gICAgICAgIFxcbiAgICAgICAgIyBEZWNvZGUgYmFzZTY0IGFuZCBkZWNyeXB0XFxuICAgICAgICBlbmNyeXB0ZWRfYnl0ZXMgPSBiYXNlNjQuYjY0ZGVjb2RlKGVuY3J5cHRlZF90ZXh0KVxcbiAgICAgICAgZGVjcnlwdGVkX2J5dGVzID0gY2lwaGVyLmRlY3J5cHQoZW5jcnlwdGVkX2J5dGVzKVxcbiAgICAgICAgXFxuICAgICAgICAjIFVucGFkIGFuZCByZXR1cm5cXG4gICAgICAgIHRyeTpcXG4gICAgICAgICAgICB1bnBhZGRlZF9ieXRlcyA9IHVucGFkKGRlY3J5cHRlZF9ieXRlcywgQUVTLmJsb2NrX3NpemUpXFxuICAgICAgICAgICAgcmV0dXJuIHVucGFkZGVkX2J5dGVzLmRlY29kZSgpXFxuICAgICAgICBleGNlcHQ6XFxuICAgICAgICAgICAgcmV0dXJuIGRlY3J5cHRlZF9ieXRlcy5kZWNvZGUoKVwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUpZbkZDdEwxWnpDeEpSelE4cG9pcXcifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzQ6MTQuMDUwWiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMjk5ZDZhZmEtMTJjOS00NTM4LWIwZWUtMGJkNTczZDg0NjFjIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxNDR0R1hoNzFqS0ROajdmN1lFRlNGWCIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvdXRpbC9tZXNzYWdlLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5cXFwiXFxcIlxcXCJcXG5NZXNzYWdlIHN0cmluZ3MgZm9yIEFQSSByZXNwb25zZXNcXG5cXFwiXFxcIlxcXCJcXG5ub3JtYWxfY29kZSA9IFxcXCJcXHU2YjYzXFx1NWUzOFxcXCJcXG5yZWRpcmVjdF9jb2RlID0gXFxcIlxcdThkZjNcXHU4ZjZjXFxcIlxcbnRlbXBvcmFyeV9yZWRpcmVjdF9jb2RlID0gXFxcIlxcdTY2ODJcXHU2NWY2XFx1OGRmM1xcdThmNmNcXFwiXFxuXFxuZW1wdHlfcGFyYW1fY29kZSA9IFxcXCJcXHU4YmY3XFx1NmM0MlxcdTUzYzJcXHU2NTcwXFx1NGUzYVxcdTdhN2FcXHU2MjE2XFx1OTUxOVxcdThiZWZcXFwiXFxudmFsaWRhdGVfcGFyYW1fY29kZSA9IFxcXCJcXHU4YmY3XFx1NmM0MlxcdTUzYzJcXHU2NTcwXFx1NGUwZFxcdTg5YzRcXHU4MzAzXFxcIlxcbmlzX25vdF9qc29uX2NvZGUgPSBcXFwiXFx1NjU3MFxcdTYzNmVcXHU2ODNjXFx1NWYwZlxcdTRlMGRcXHU2NjJmanNvblxcXCJcXG5cXG5vdGhlcl9jb2RlID0gXFxcIlxcdTUxNzZcXHU1YjgzXFx1OTUxOVxcdThiZWZcXFwiXFxuY3J1ZF9lcnJvcl9jb2RlID0gXFxcIlxcdTY1NzBcXHU2MzZlXFx1NWU5M1xcdTY0Y2RcXHU0ZjVjXFx1NTkzMVxcdThkMjVcXFwiXFxuaGVhZGVyX2Vycm9yX2NvZGUgPSBcXFwiXFx1NTkzNFxcdTkwZThcXHU5NTE5XFx1OGJlZlxcXCJcXG5jYXB0Y2hhX2Vycm9yX2NvZGUgPSBcXFwiXFx1OWE4Y1xcdThiYzFcXHU3ODAxXFx1OTUxOVxcdThiZWZcXFwiXFxuaWRfZXhpc3RfY29kZSA9IFxcXCJpZFxcdTYyMTZcXHU4YmIwXFx1NWY1NVxcdTVkZjJcXHU1YjU4XFx1NTcyOFxcXCJcXG5pZF9ub3RleGlzdF9jb2RlID0gXFxcImlkXFx1NjIxNlxcdThiYjBcXHU1ZjU1XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbmZpbGVfbm90ZXhpc3RfY29kZSA9IFxcXCJcXHU0ZTBhXFx1NGYyMFxcdTY1ODdcXHU0ZWY2XFx1NGUwZFxcdTViNThcXHU1NzI4XFxcIlxcbnVzZXJuYW1lX2Vycm9yX2NvZGUgPSBcXFwiXFx1NzUyOFxcdTYyMzdcXHU1NDBkXFx1OTUxOVxcdThiZWZcXFwiXFxucGFzc3dvcmRfZXJyb3JfY29kZSA9IFxcXCJcXHU4ZDI2XFx1NTNmN1xcdTYyMTZcXHU4MDA1XFx1NWJjNlxcdTc4MDFcXHU5NTE5XFx1OGJlZlxcXCJcXG5cXG50b2tlbl9lcnJvcl9jb2RlID0gXFxcInRva2VuXFx1OTUxOVxcdThiZWZcXFwiXFxudG9rZW5fZXhwaXJlZF9jb2RlID0gXFxcInRva2VuXFx1OTUxOVxcdThiZWZcXFwiXFxubm9uX2F1dGhvcml6ZWRfY29kZSA9IFxcXCJcXHU2NWUwXFx1Njc0M1xcdTk2NTBcXFwiXFxuXFxuc3lzdGVtX2Vycm9yX2NvZGUgPSBcXFwiXFx1N2NmYlxcdTdlZGZcXHU3ZWE3XFx1OTUxOVxcdThiZWZcXFwiXFxucmVxdWVzdF9leHBpcmVkX2NvZGUgPSBcXFwiXFx1OGJmN1xcdTZjNDJcXHU1ZGYyXFx1OGZjN1xcdTY3MWZcXFwiXFxucmVwZWF0ZWRfcmVxdWVzdF9jb2RlID0gXFxcIlxcdTkxY2RcXHU1OTBkXFx1OGJmN1xcdTZjNDJcXFwiXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxODVGRTRGckxuYlhzOFBVUjhod1JiRSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozNDozMS45MzFaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI1YjY3MmY4Yy1lNzJjLTQ2M2MtYmUyNS02M2IzZmY5Yjg0NGUiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFEeWJVdVlEczhCSDREd2d6RnF2ekhaIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy91dGlsL0N1c3RvbUpTT05FbmNvZGVyLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5mcm9tIGRhdGV0aW1lIGltcG9ydCBkYXRldGltZSwgZGF0ZVxcbmltcG9ydCBqc29uXFxuZnJvbSBkZWNpbWFsIGltcG9ydCBEZWNpbWFsXFxuXFxuXFxuY2xhc3MgQ3VzdG9tSnNvbkVuY29kZXIoanNvbi5KU09ORW5jb2Rlcik6XFxuICAgIFxcXCJcXFwiXFxcIlxcbiAgICBDdXN0b20gSlNPTiBlbmNvZGVyIGZvciBoYW5kbGluZyBzcGVjaWFsIGRhdGEgdHlwZXNcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGRlZiBkZWZhdWx0KHNlbGYsIG9iaik6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIENvbnZlcnQgc3BlY2lhbCBkYXRhIHR5cGVzIHRvIEpTT04tc2VyaWFsaXphYmxlIHR5cGVzXFxuICAgICAgICA6cGFyYW0gb2JqOiBPYmplY3QgdG8gY29udmVydFxcbiAgICAgICAgOnJldHVybjogSlNPTi1zZXJpYWxpemFibGUgb2JqZWN0XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIGlmIGlzaW5zdGFuY2Uob2JqLCBkYXRldGltZSk6XFxuICAgICAgICAgICAgcmV0dXJuIG9iai5zdHJmdGltZSgnJVktJW0tJWQgJUg6JU06JVMnKVxcbiAgICAgICAgaWYgaXNpbnN0YW5jZShvYmosIGRhdGUpOlxcbiAgICAgICAgICAgIHJldHVybiBvYmouc3RyZnRpbWUoJyVZLSVtLSVkJylcXG4gICAgICAgIGlmIGlzaW5zdGFuY2Uob2JqLCBEZWNpbWFsKTpcXG4gICAgICAgICAgICAjIElmIGl0J3MgYW4gaW50ZWdlciwgcmV0dXJuIGFuIGludGVnZXJcXG4gICAgICAgICAgICBpZiBvYmogJSAxID09IDA6XFxuICAgICAgICAgICAgICAgIHJldHVybiBpbnQob2JqKVxcbiAgICAgICAgICAgICMgT3RoZXJ3aXNlIHJldHVybiBhIGZsb2F0XFxuICAgICAgICAgICAgcmV0dXJuIGZsb2F0KG9iailcXG4gICAgICAgIHJldHVybiBzdXBlcigpLmRlZmF1bHQob2JqKVwifSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6IiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMTQ0dEdYaDcxaktETmo3ZjdZRUZTRlgifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzQ6NDIuNzI2WiIsInJlc3BvbnNlX3RleHQiOiIiLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiOGFhZWRmYmMtOTQ3NC00N2MyLTg4MTktN2U0YjdmZTY0OTMyIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxWE1oejFCZWNmenZUWlJqS2FBSDNSayIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvdXRpbC9jb25maWdyZWFkLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5mcm9tIGNvbmZpZ3BhcnNlciBpbXBvcnQgQ29uZmlnUGFyc2VyXFxuXFxuXFxuZGVmIGNvbmZpZ19yZWFkKGZpbGVQYXRoOiBzdHIpOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgUmVhZCBjb25maWd1cmF0aW9uIGZyb20gYSBmaWxlXFxuICAgIDpwYXJhbSBmaWxlUGF0aDogUGF0aCB0byB0aGUgY29uZmlndXJhdGlvbiBmaWxlXFxuICAgIDpyZXR1cm46IERhdGFiYXNlIGNvbmZpZ3VyYXRpb24gcGFyYW1ldGVyc1xcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgY2ZnID0gQ29uZmlnUGFyc2VyKClcXG4gICAgY2ZnLnJlYWQoZmlsZVBhdGgsIGVuY29kaW5nPVxcXCJ1dGYtOC1zaWdcXFwiKVxcbiAgICBcXG4gICAgaWYgXFxcInNxbFxcXCIgaW4gY2ZnLnNlY3Rpb25zKCk6XFxuICAgICAgICBkYlR5cGUgPSBjZmcuZ2V0KCdzcWwnLCAndHlwZScpXFxuICAgICAgICBob3N0ID0gY2ZnLmdldCgnc3FsJywgJ2hvc3QnKVxcbiAgICAgICAgcG9ydCA9IGNmZy5nZXRpbnQoJ3NxbCcsICdwb3J0JylcXG4gICAgICAgIHVzZXIgPSBjZmcuZ2V0KCdzcWwnLCAndXNlcicpXFxuICAgICAgICBwYXNzd2QgPSBjZmcuZ2V0KCdzcWwnLCAncGFzc3dkJylcXG4gICAgICAgIGRiTmFtZSA9IGNmZy5nZXQoJ3NxbCcsICdkYicpXFxuICAgICAgICBjaGFyc2V0ID0gY2ZnLmdldCgnc3FsJywgJ2NoYXJzZXQnKVxcbiAgICAgICAgcmV0dXJuIGRiVHlwZSwgaG9zdCwgcG9ydCwgdXNlciwgcGFzc3dkLCBkYk5hbWUsIGNoYXJzZXRcXG4gICAgZWxzZTpcXG4gICAgICAgIHJldHVybiBOb25lLCBOb25lLCBOb25lLCBOb25lLCBOb25lLCBOb25lLCBOb25lXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRHliVXVZRHM4Qkg0RHdnekZxdnpIWiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozNDo1MS41MzVaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJjNWY1NjhlNy0yNmFkLTRmYjAtOWVkYS02NTFjYjhlYjE1MzMiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFHNXJnZ0FIUzFZcnRIUEczeHQ4ZGIxIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy91dGlsL2JhaWR1YmNlX2FwaS5weVwiLCBcImZpbGVfY29udGVudFwiOiBcIiMgY29kaW5nOnV0Zi04XFxuaW1wb3J0IGJhc2U2NFxcbmltcG9ydCBqc29uXFxuaW1wb3J0IHVybGxpYlxcbmltcG9ydCB1cmxsaWIucmVxdWVzdFxcbmltcG9ydCB1cmxsaWIucGFyc2VcXG5pbXBvcnQgc3NsXFxuXFxuXFxuY2xhc3MgQmFpRHVCY2Uob2JqZWN0KTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIEJhaWR1IEJDRSBBUEkgdXRpbGl0eSBjbGFzc1xcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgZGVmIF9faW5pdF9fKHNlbGYpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBJbml0aWFsaXplIHdpdGggZGVmYXVsdCBBUEkgY3JlZGVudGlhbHNcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgc2VsZi5jbGllbnRfaWQgPSAneDIweE9qdE9zQXRiUWhtMldCdWlmdVF3JyAgIyBBS1xcbiAgICAgICAgc2VsZi5jbGllbnRfc2VjcmV0ID0gJ083eU1wMmRtT25DdFF0Qm9rVXQxZ042aGdGQ2NMTGNwJyAgIyBTS1xcbiAgICBcXG4gICAgZGVmIGdldF90b2tlbihzZWxmKTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgR2V0IGFjY2VzcyB0b2tlbiBmcm9tIEJhaWR1IEFQSVxcbiAgICAgICAgOnJldHVybjogQWNjZXNzIHRva2VuXFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIGhvc3QgPSAnaHR0cHM6Ly9haXAuYmFpZHViY2UuY29tL29hdXRoLzIuMC90b2tlbj9ncmFudF90eXBlPWNsaWVudF9jcmVkZW50aWFscyZjbGllbnRfaWQ9e30mY2xpZW50X3NlY3JldD17fScuZm9ybWF0KFxcbiAgICAgICAgICAgIHNlbGYuY2xpZW50X2lkLCBzZWxmLmNsaWVudF9zZWNyZXQpXFxuICAgICAgICBcXG4gICAgICAgIHJlcXVlc3QgPSB1cmxsaWIucmVxdWVzdC5SZXF1ZXN0KGhvc3QpXFxuICAgICAgICByZXF1ZXN0LmFkZF9oZWFkZXIoJ0NvbnRlbnQtVHlwZScsICdhcHBsaWNhdGlvbi9qc29uOyBjaGFyc2V0PVVURi04JylcXG4gICAgICAgIFxcbiAgICAgICAgcmVzcG9uc2UgPSB1cmxsaWIucmVxdWVzdC51cmxvcGVuKHJlcXVlc3QpXFxuICAgICAgICBjb250ZW50ID0gcmVzcG9uc2UucmVhZCgpXFxuICAgICAgICBcXG4gICAgICAgIGlmIGNvbnRlbnQ6XFxuICAgICAgICAgICAgY29udGVudCA9IGpzb24ubG9hZHMoY29udGVudClcXG4gICAgICAgICAgICByZXR1cm4gY29udGVudC5nZXQoJ2FjY2Vzc190b2tlbicpXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBOb25lXFxuICAgIFxcbiAgICBkZWYgYmRfY2hlY2sycGljKHNlbGYsIHBpYzEsIHBpYzIpOlxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBDb21wYXJlIHR3byBmYWNlIGltYWdlcyB1c2luZyBCYWlkdSBBUElcXG4gICAgICAgIDpwYXJhbSBwaWMxOiBGaXJzdCBpbWFnZSBwYXRoXFxuICAgICAgICA6cGFyYW0gcGljMjogU2Vjb25kIGltYWdlIHBhdGhcXG4gICAgICAgIDpyZXR1cm46IENvbXBhcmlzb24gcmVzdWx0XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIHJlcXVlc3RfdXJsID0gXFxcImh0dHBzOi8vYWlwLmJhaWR1YmNlLmNvbS9yZXN0LzIuMC9mYWNlL3YzL21hdGNoXFxcIlxcbiAgICAgICAgXFxuICAgICAgICAjIEdldCBhY2Nlc3MgdG9rZW5cXG4gICAgICAgIGFjY2Vzc190b2tlbiA9IHNlbGYuZ2V0X3Rva2VuKClcXG4gICAgICAgIHJlcXVlc3RfdXJsID0gcmVxdWVzdF91cmwgKyBcXFwiP2FjY2Vzc190b2tlbj1cXFwiICsgYWNjZXNzX3Rva2VuXFxuICAgICAgICBcXG4gICAgICAgICMgUHJlcGFyZSBpbWFnZSBkYXRhXFxuICAgICAgICBpbWFnZTEgPSBzZWxmLmdldF9maWxlX2NvbnRlbnRfYXNfYmFzZTY0KHBpYzEpXFxuICAgICAgICBpbWFnZTIgPSBzZWxmLmdldF9maWxlX2NvbnRlbnRfYXNfYmFzZTY0KHBpYzIpXFxuICAgICAgICBcXG4gICAgICAgIHBhcmFtcyA9IFtcXG4gICAgICAgICAgICB7XFxuICAgICAgICAgICAgICAgIFxcXCJpbWFnZVxcXCI6IGltYWdlMSxcXG4gICAgICAgICAgICAgICAgXFxcImltYWdlX3R5cGVcXFwiOiBcXFwiQkFTRTY0XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcImZhY2VfdHlwZVxcXCI6IFxcXCJMSVZFXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcInF1YWxpdHlfY29udHJvbFxcXCI6IFxcXCJMT1dcXFwiXFxuICAgICAgICAgICAgfSxcXG4gICAgICAgICAgICB7XFxuICAgICAgICAgICAgICAgIFxcXCJpbWFnZVxcXCI6IGltYWdlMixcXG4gICAgICAgICAgICAgICAgXFxcImltYWdlX3R5cGVcXFwiOiBcXFwiQkFTRTY0XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcImZhY2VfdHlwZVxcXCI6IFxcXCJMSVZFXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcInF1YWxpdHlfY29udHJvbFxcXCI6IFxcXCJMT1dcXFwiXFxuICAgICAgICAgICAgfVxcbiAgICAgICAgXVxcbiAgICAgICAgXFxuICAgICAgICBwYXJhbXMgPSBqc29uLmR1bXBzKHBhcmFtcykuZW5jb2RlKCd1dGYtOCcpXFxuICAgICAgICBcXG4gICAgICAgICMgU2VuZCByZXF1ZXN0XFxuICAgICAgICByZXF1ZXN0ID0gdXJsbGliLnJlcXVlc3QuUmVxdWVzdCh1cmw9cmVxdWVzdF91cmwsIGRhdGE9cGFyYW1zKVxcbiAgICAgICAgcmVxdWVzdC5hZGRfaGVhZGVyKCdDb250ZW50LVR5cGUnLCAnYXBwbGljYXRpb24vanNvbicpXFxuICAgICAgICBcXG4gICAgICAgIHJlc3BvbnNlID0gdXJsbGliLnJlcXVlc3QudXJsb3BlbihyZXF1ZXN0KVxcbiAgICAgICAgY29udGVudCA9IHJlc3BvbnNlLnJlYWQoKVxcbiAgICAgICAgXFxuICAgICAgICBpZiBjb250ZW50OlxcbiAgICAgICAgICAgIHJldHVybiBqc29uLmxvYWRzKGNvbnRlbnQpXFxuICAgICAgICBcXG4gICAgICAgIHJldHVybiBOb25lXFxuICAgIFxcbiAgICBkZWYgZ2V0X2ZpbGVfY29udGVudF9hc19iYXNlNjQoc2VsZiwgcGF0aCwgdXJsZW5jb2RlZD1GYWxzZSk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIEdldCBmaWxlIGNvbnRlbnQgYXMgYmFzZTY0IGVuY29kZWQgc3RyaW5nXFxuICAgICAgICA6cGFyYW0gcGF0aDogRmlsZSBwYXRoXFxuICAgICAgICA6cGFyYW0gdXJsZW5jb2RlZDogV2hldGhlciB0byBVUkwgZW5jb2RlIHRoZSByZXN1bHRcXG4gICAgICAgIDpyZXR1cm46IEJhc2U2NCBlbmNvZGVkIHN0cmluZ1xcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICB3aXRoIG9wZW4ocGF0aCwgXFxcInJiXFxcIikgYXMgZjpcXG4gICAgICAgICAgICBjb250ZW50ID0gYmFzZTY0LmI2NGVuY29kZShmLnJlYWQoKSkuZGVjb2RlKFxcXCJ1dGY4XFxcIilcXG4gICAgICAgICAgICBpZiB1cmxlbmNvZGVkOlxcbiAgICAgICAgICAgICAgICBjb250ZW50ID0gdXJsbGliLnBhcnNlLnF1b3RlX3BsdXMoY29udGVudClcXG4gICAgICAgIHJldHVybiBjb250ZW50XCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxWE1oejFCZWNmenZUWlJqS2FBSDNSayJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozNTowMC43MDVaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiI3MzQ4ZjA0NC1kNTlmLTQ1YjctYmM4Yi0yY2Y3MGVlMTA5YzAiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFBeVNnQk1zdmROMWVoVGs5Q0ZlRGROIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy94bWlkZGxld2FyZS94YXV0aC5weVwiLCBcImZpbGVfY29udGVudFwiOiBcIiMgY29kaW5nOnV0Zi04XFxuXFxuZnJvbSBkamFuZ28udXRpbHMuZGVwcmVjYXRpb24gaW1wb3J0IE1pZGRsZXdhcmVNaXhpblxcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcXG5cXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxcbmZyb20gZGoyLnNldHRpbmdzIGltcG9ydCBzY2hlbWFOYW1lXFxuXFxuXFxuY2xhc3MgWGF1dGgoTWlkZGxld2FyZU1peGluKTpcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIEF1dGhlbnRpY2F0aW9uIG1pZGRsZXdhcmVcXG4gICAgXFxcIlxcXCJcXFwiXFxuICAgIGRlZiBwcm9jZXNzX3JlcXVlc3Qoc2VsZiwgcmVxdWVzdCk6XFxuICAgICAgICBcXFwiXFxcIlxcXCJcXG4gICAgICAgIFByb2Nlc3MgcmVxdWVzdCBmb3IgYXV0aGVudGljYXRpb25cXG4gICAgICAgIDpwYXJhbSByZXF1ZXN0OiBSZXF1ZXN0IG9iamVjdFxcbiAgICAgICAgOnJldHVybjogUmVzcG9uc2Ugb3IgTm9uZVxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBmdWxsUGF0aCA9IHJlcXVlc3QuZ2V0X2Z1bGxfcGF0aCgpXFxuICAgICAgICBcXG4gICAgICAgICMgU2tpcCBmb3IgV2ViU29ja2V0IGNvbm5lY3Rpb25zXFxuICAgICAgICBpZiByZXF1ZXN0Lk1FVEEuZ2V0KCdIVFRQX1VQR1JBREUnKSA9PSAnd2Vic29ja2V0JzpcXG4gICAgICAgICAgICByZXR1cm5cXG4gICAgICAgIFxcbiAgICAgICAgaWYgcmVxdWVzdC5tZXRob2QgPT0gJ0dFVCc6XFxuICAgICAgICAgICAgIyBMaXN0IG9mIHBhdGhzIHRoYXQgZG9uJ3QgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxcbiAgICAgICAgICAgIGZpbHRlckxpc3QgPSBbXFxuICAgICAgICAgICAgICAgIFxcXCIvaW5kZXhcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiL2ZvbGxvd1xcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIvZmF2aWNvbi5pY29cXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiL2xvZ2luXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi9yZWdpc3RlclxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIvbm90aWZ5XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi9maWxlXFxcIixcXG4gICAgICAgICAgICAgICAgJy5qcycsXFxuICAgICAgICAgICAgICAgIFxcXCIuY3NzXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5qcGdcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiLmpwZWdcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiLnBuZ1xcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIuZ2lmXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5tcDRcXFwiLFxcbiAgICAgICAgICAgICAgICAnLm1wMycsXFxuICAgICAgICAgICAgICAgIFxcXCIudHRmXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi53b3RmXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi53b2ZmXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi53b2ZmMlxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIub3RmXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5lb3RcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiLnN2Z1xcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIuY3N2XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi53ZWJwXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi54bHNcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiLnhsc3hcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiLmRvY1xcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIuZG9jeFxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIucHB0XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5wcHR4XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5odG1sXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi5odG1cXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiZGV0YWlsXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi9mb3J1bS9mbGlzdFxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIvZm9ydW0vbGlzdFxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIvYWRtaW5cXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiL3NlY3VyaXR5XFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi9hdXRvU29ydFxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIvY29uZmlnL2xpc3RcXFwiLFxcbiAgICAgICAgICAgICAgICBcXFwiL25ld3MvbGlzdFxcXCIsXFxuICAgICAgICAgICAgICAgIFxcXCIveGFkbWluXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi9maWxlL2Rvd25sb2FkXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcIi97fS9yZW1pbmQvXFxcIi5mb3JtYXQoc2NoZW1hTmFtZSksXFxuICAgICAgICAgICAgICAgIFxcXCIve30vb3B0aW9uL1xcXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxcbiAgICAgICAgICAgICAgICBcXFwicmVzZXRQYXNzXFxcIixcXG4gICAgICAgICAgICAgICAgXFxcInVwZGF0ZUJyb3dzZUR1cmF0aW9uXFxcIixcXG4gICAgICAgICAgICBdXFxuXFxuICAgICAgICAgICAgIyBBZGQgbW9kZWwtc3BlY2lmaWMgcGF0aHMgdGhhdCBkb24ndCByZXF1aXJlIGF1dGhlbnRpY2F0aW9uXFxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxcbiAgICAgICAgICAgIGZvciBtIGluIGFsbE1vZGVsczpcXG4gICAgICAgICAgICAgICAgdHJ5OlxcbiAgICAgICAgICAgICAgICAgICAgZm9yZUVuZExpc3QgPSBtLl9fZm9yZUVuZExpc3RfX1xcbiAgICAgICAgICAgICAgICBleGNlcHQ6XFxuICAgICAgICAgICAgICAgICAgICBmb3JlRW5kTGlzdCA9IE5vbmVcXG4gICAgICAgICAgICAgICAgXFxuICAgICAgICAgICAgICAgIGlmIGZvcmVFbmRMaXN0IGlzIE5vbmUgb3IgZm9yZUVuZExpc3QgIT0gXFxcIlxcdTUyNGRcXHU4OTgxXFx1NzY3YlxcXCI6XFxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcXFwiL3t9L3NlbmRlbWFpbFxcXCIuZm9ybWF0KG0uX190YWJsZW5hbWVfXykpXFxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcXFwiL3t9L3NlbmRzbXNcXFwiLmZvcm1hdChtLl9fdGFibGVuYW1lX18pKVxcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyTGlzdC5hcHBlbmQoXFxcIi97fS9saXN0XFxcIi5mb3JtYXQobS5fX3RhYmxlbmFtZV9fKSlcXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlckxpc3QuYXBwZW5kKFxcXCIve30vZGV0YWlsXFxcIi5mb3JtYXQobS5fX3RhYmxlbmFtZV9fKSlcXG5cXG4gICAgICAgICAgICAjIENoZWNrIGlmIGF1dGhlbnRpY2F0aW9uIGlzIHJlcXVpcmVkXFxuICAgICAgICAgICAgYXV0aCA9IFRydWVcXG5cXG4gICAgICAgICAgICBpZiBmdWxsUGF0aCA9PSAnLyc6XFxuICAgICAgICAgICAgICAgIHBhc3NcXG4gICAgICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgICAgICBmb3IgaSBpbiBmaWx0ZXJMaXN0OlxcbiAgICAgICAgICAgICAgICAgICAgaWYgaSBpbiBmdWxsUGF0aDpcXG4gICAgICAgICAgICAgICAgICAgICAgICBhdXRoID0gRmFsc2VcXG4gICAgICAgICAgICAgICAgXFxuICAgICAgICAgICAgICAgIGlmIGF1dGg6XFxuICAgICAgICAgICAgICAgICAgICAjIEF1dGhlbnRpY2F0ZSByZXF1ZXN0XFxuICAgICAgICAgICAgICAgICAgICByZXN1bHQgPSBBdXRoLmlkZW50aWZ5KEF1dGgsIHJlcXVlc3QpXFxuXFxuICAgICAgICAgICAgICAgICAgICBpZiByZXN1bHQuZ2V0KCdjb2RlJykgIT0gbm9ybWFsX2NvZGU6XFxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShyZXN1bHQpXFxuICAgICAgICBcXG4gICAgICAgIGVsaWYgcmVxdWVzdC5tZXRob2QgPT0gJ1BPU1QnOlxcbiAgICAgICAgICAgICMgTGlzdCBvZiBQT1NUIHBhdGhzIHRoYXQgZG9uJ3QgcmVxdWlyZSBhdXRoZW50aWNhdGlvblxcbiAgICAgICAgICAgIHBvc3RfbGlzdCA9IFtcXG4gICAgICAgICAgICAgICAgJy97fS9kZWZhdWx0dXNlci9yZWdpc3RlcicuZm9ybWF0KHNjaGVtYU5hbWUpLFxcbiAgICAgICAgICAgICAgICAnL3t9L2RlZmF1bHR1c2VyL2xvZ2luJy5mb3JtYXQoc2NoZW1hTmFtZSksXFxuICAgICAgICAgICAgICAgICcve30vdXNlcnMvcmVnaXN0ZXInLmZvcm1hdChzY2hlbWFOYW1lKSxcXG4gICAgICAgICAgICAgICAgJy97fS91c2Vycy9sb2dpbicuZm9ybWF0KHNjaGVtYU5hbWUpLFxcbiAgICAgICAgICAgICAgICBcXFwiL3t9L2V4YW11c2Vycy9sb2dpblxcXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxcbiAgICAgICAgICAgICAgICBcXFwiL3t9L2V4YW11c2Vycy9yZWdpc3RlclxcXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxcbiAgICAgICAgICAgICAgICBcXFwiL3t9L2ZpbGUvdXBsb2FkXFxcIi5mb3JtYXQoc2NoZW1hTmFtZSksXFxuICAgICAgICAgICAgICAgIFxcXCIvdXBkYXRlXFxcIlxcbiAgICAgICAgICAgIF1cXG4gICAgICAgICAgICBcXG4gICAgICAgICAgICAjIENoZWNrIGlmIGF1dGhlbnRpY2F0aW9uIGlzIHJlcXVpcmVkXFxuICAgICAgICAgICAgaWYgZnVsbFBhdGggbm90IGluIHBvc3RfbGlzdCBhbmQgXFxcInJlZ2lzdGVyXFxcIiBub3QgaW4gZnVsbFBhdGggYW5kIFxcXCJsb2dpblxcXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcXFwiZmFjZUxvZ2luXFxcIiBub3QgaW4gZnVsbFBhdGggYW5kIFxcXCJ1cGRhdGVcXFwiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXFxcInVwbG9hZFxcXCIgbm90IGluIGZ1bGxQYXRoOlxcbiAgICAgICAgICAgICAgICAjIEF1dGhlbnRpY2F0ZSByZXF1ZXN0XFxuICAgICAgICAgICAgICAgIHJlc3VsdCA9IEF1dGguaWRlbnRpZnkoQXV0aCwgcmVxdWVzdClcXG5cXG4gICAgICAgICAgICAgICAgaWYgcmVzdWx0LmdldCgnY29kZScpICE9IG5vcm1hbF9jb2RlOlxcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShyZXN1bHQpXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiMTcuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIG1pZGRsZXdhcmUgZmlsZXM6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFHNXJnZ0FIUzFZcnRIUEczeHQ4ZGIxIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjM1OjIwLjUxOFoiLCJyZXNwb25zZV90ZXh0IjoiMTcuIE5vdywgbGV0J3MgY3JlYXRlIHRoZSByZWZhY3RvcmVkIG1pZGRsZXdhcmUgZmlsZXM6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMjk0NWMxOWYtOWU4Ny00MDk2LThjMmYtNWE4ZmIzYTMwZWQ2IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxTVZUdzNTcUp0NFVWcG1wczhTN00xZyIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcveG1pZGRsZXdhcmUveHBhcmFtLnB5XCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBjb2Rpbmc6dXRmLThcXG5pbXBvcnQganNvblxcbmltcG9ydCBjb3B5XFxuZnJvbSBkamFuZ28udXRpbHMuZGVwcmVjYXRpb24gaW1wb3J0IE1pZGRsZXdhcmVNaXhpblxcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxcblxcblxcbmNsYXNzIFhwYXJhbShNaWRkbGV3YXJlTWl4aW4pOlxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgUGFyYW1ldGVyIHByb2Nlc3NpbmcgbWlkZGxld2FyZVxcbiAgICBcXFwiXFxcIlxcXCJcXG4gICAgZGVmIHByb2Nlc3NfcmVxdWVzdChzZWxmLCByZXF1ZXN0KTpcXG4gICAgICAgIFxcXCJcXFwiXFxcIlxcbiAgICAgICAgUHJvY2VzcyByZXF1ZXN0IHBhcmFtZXRlcnNcXG4gICAgICAgIDpwYXJhbSByZXF1ZXN0OiBSZXF1ZXN0IG9iamVjdFxcbiAgICAgICAgOnJldHVybjogTm9uZVxcbiAgICAgICAgXFxcIlxcXCJcXFwiXFxuICAgICAgICBmdWxsUGF0aCA9IHJlcXVlc3QuZ2V0X2Z1bGxfcGF0aCgpXFxuICAgICAgICBcXG4gICAgICAgICMgU2tpcCBmb3IgV2ViU29ja2V0IGNvbm5lY3Rpb25zXFxuICAgICAgICBpZiByZXF1ZXN0Lk1FVEEuZ2V0KCdIVFRQX1VQR1JBREUnKSA9PSAnd2Vic29ja2V0JzpcXG4gICAgICAgICAgICByZXR1cm5cXG4gICAgICAgIFxcbiAgICAgICAgIyBTa2lwIGZvciBzdGF0aWMgZmlsZXNcXG4gICAgICAgIGlmIFxcXCIvanMvXFxcIiBub3QgaW4gZnVsbFBhdGggYW5kIFxcXCIvY3NzL1xcXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcXFwiL2ltZy9cXFwiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXFxcIi9mb250cy9cXFwiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXFxcIi9mcm9udC9cXFwiIG5vdCBpbiBmdWxsUGF0aDpcXG4gICAgICAgICAgICByZXFfZGljdCA9IHt9XFxuXFxuICAgICAgICAgICAgIyBHZXQgcXVlcnkgcGFyYW1ldGVyc1xcbiAgICAgICAgICAgIGZvciBrLCB2IGluIHJlcXVlc3QuR0VULml0ZW1zKCk6XFxuICAgICAgICAgICAgICAgIHJlcV9kaWN0W2tdID0gdlxcblxcbiAgICAgICAgICAgIGlmIHJlcXVlc3QubWV0aG9kID09IFxcXCJQT1NUXFxcIjpcXG4gICAgICAgICAgICAgICAgY29udGVudFR5cGUgPSByZXF1ZXN0Lk1FVEEuZ2V0KCdDT05URU5UX1RZUEUnLCBcXFwiXFxcIilcXG4gICAgICAgICAgICAgICAgXFxuICAgICAgICAgICAgICAgIGlmIFxcXCJqc29uXFxcIiBpbiBjb250ZW50VHlwZTpcXG4gICAgICAgICAgICAgICAgICAgICMgUGFyc2UgYXBwbGljYXRpb24vanNvbiBwYXJhbWV0ZXJzXFxuICAgICAgICAgICAgICAgICAgICB0cnk6XFxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YV8gPSBqc29uLmxvYWRzKHJlcXVlc3QuYm9keSlcXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiBpc2luc3RhbmNlKGRhdGFfLCBsaXN0KTpcXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxX2RpY3RbJ2lkcyddID0gZGF0YV9cXG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgaywgdiBpbiBkYXRhXy5pdGVtcygpOlxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxX2RpY3Rba10gPSB2XFxuICAgICAgICAgICAgICAgICAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6XFxuICAgICAgICAgICAgICAgICAgICAgICAgIyBHZXQgZm9ybSBkYXRhIHBhcmFtZXRlcnMgaWYgSlNPTiBwYXJzaW5nIGZhaWxzXFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIGssIHYgaW4gcmVxdWVzdC5QT1NULml0ZW1zKCk6XFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcV9kaWN0W2tdID0gdlxcbiAgICAgICAgICAgICAgICBlbHNlOlxcbiAgICAgICAgICAgICAgICAgICAgIyBHZXQgZm9ybSBkYXRhIHBhcmFtZXRlcnNcXG4gICAgICAgICAgICAgICAgICAgIGZvciBrLCB2IGluIHJlcXVlc3QuUE9TVC5pdGVtcygpOlxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcV9kaWN0W2tdID0gdlxcbiAgICAgICAgICAgIFxcbiAgICAgICAgICAgICMgUHJvY2VzcyBwYXJhbWV0ZXJzLCByZW1vdmUgdXNlbGVzcyBhbmQgaW5jb3JyZWN0IHBhcmFtZXRlcnNcXG4gICAgICAgICAgICBpZiByZXFfZGljdC5nZXQoXFxcImNyZWF0ZWRcXFwiKSBpcyBub3QgTm9uZTpcXG4gICAgICAgICAgICAgICAgcmVxX2RpY3RbJ2FkZHRpbWUnXSA9IGNvcHkuZGVlcGNvcHkocmVxX2RpY3QuZ2V0KFxcXCJjcmVhdGVkXFxcIikpXFxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcXFwiY3JlYXRlZFxcXCJdXFxuICAgICAgICAgICAgXFxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFxcXCJ0XFxcIikgaXMgbm90IE5vbmU6XFxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcXFwidFxcXCJdXFxuICAgICAgICAgICAgXFxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFxcXCIxXFxcIikgaXMgbm90IE5vbmU6XFxuICAgICAgICAgICAgICAgIHJlcV9kaWN0Wyd0eXBlJ10gPSBjb3B5LmRlZXBjb3B5KHJlcV9kaWN0LmdldChcXFwiMVxcXCIpKVxcbiAgICAgICAgICAgICAgICBkZWwgcmVxX2RpY3RbXFxcIjFcXFwiXVxcbiAgICAgICAgICAgIFxcbiAgICAgICAgICAgICMgU3RvcmUgcHJvY2Vzc2VkIHBhcmFtZXRlcnMgaW4gc2Vzc2lvblxcbiAgICAgICAgICAgIHJlcXVlc3Quc2Vzc2lvbltcXFwicmVxX2RpY3RcXFwiXSA9IHJlcV9kaWN0XCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxQXlTZ0JNc3ZkTjFlaFRrOUNGZURkTiJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozNTo0MS40NzhaIiwicmVzcG9uc2VfdGV4dCI6IiIsIndvcmtzcGFjZV9maWxlX2NodW5rcyI6W119LHsic3RhdHVzIjoic3VjY2VzcyIsInJlcXVlc3RfaWQiOiJjYTUwMTE4Ny1lNmM4LTQ4MzgtYjg5NC03MTIyNDNkZTBjYjkiLCJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJzdHJ1Y3R1cmVkX291dHB1dF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjUsImNvbnRlbnQiOiIiLCJ0b29sX3VzZSI6eyJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDEyeWZRTTI0cGZLVlRkYWQ2Y0U2bXVFIiwidG9vbF9uYW1lIjoic2F2ZS1maWxlIiwiaW5wdXRfanNvbiI6IntcImZpbGVfcGF0aFwiOiBcIm5ldy9tYW5hZ2UucHlcIiwgXCJmaWxlX2NvbnRlbnRcIjogXCIjIS91c3IvYmluL2VudiBweXRob25cXG5pbXBvcnQgb3NcXG5pbXBvcnQgc3lzXFxuXFxuaWYgX19uYW1lX18gPT0gXFxcIl9fbWFpbl9fXFxcIjpcXG4gICAgb3MuZW52aXJvbi5zZXRkZWZhdWx0KFxcXCJESkFOR09fU0VUVElOR1NfTU9EVUxFXFxcIiwgXFxcImRqMi5zZXR0aW5nc1xcXCIpXFxuICAgIHRyeTpcXG4gICAgICAgIGZyb20gZGphbmdvLmNvcmUubWFuYWdlbWVudCBpbXBvcnQgZXhlY3V0ZV9mcm9tX2NvbW1hbmRfbGluZVxcbiAgICBleGNlcHQgSW1wb3J0RXJyb3IgYXMgZXhjOlxcbiAgICAgICAgcmFpc2UgSW1wb3J0RXJyb3IoXFxuICAgICAgICAgICAgXFxcIkNvdWxkbid0IGltcG9ydCBEamFuZ28uIEFyZSB5b3Ugc3VyZSBpdCdzIGluc3RhbGxlZCBhbmQgXFxcIlxcbiAgICAgICAgICAgIFxcXCJhdmFpbGFibGUgb24geW91ciBQWVRIT05QQVRIIGVudmlyb25tZW50IHZhcmlhYmxlPyBEaWQgeW91IFxcXCJcXG4gICAgICAgICAgICBcXFwiZm9yZ2V0IHRvIGFjdGl2YXRlIGEgdmlydHVhbCBlbnZpcm9ubWVudD9cXFwiXFxuICAgICAgICApIGZyb20gZXhjXFxuICAgIGV4ZWN1dGVfZnJvbV9jb21tYW5kX2xpbmUoc3lzLmFyZ3YpXCJ9IiwibWNwX3NlcnZlcl9uYW1lIjoiIiwibWNwX3Rvb2xfbmFtZSI6IiJ9fSx7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiMTguIEZpbmFsbHksIGxldCdzIGNyZWF0ZSB0aGUgbWFuYWdlLnB5IGZpbGU6XG4ifSx7ImlkIjoyLCJ0eXBlIjoyLCJjb250ZW50IjoiIn0seyJpZCI6MywidHlwZSI6MywiY29udGVudCI6IiJ9XSwic2Vlbl9zdGF0ZSI6InNlZW4iLCJzdHJ1Y3R1cmVkX3JlcXVlc3Rfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjoxLCJ0b29sX3Jlc3VsdF9ub2RlIjp7ImNvbnRlbnQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc19lcnJvciI6ZmFsc2UsInJlcXVlc3RfaWQiOiIiLCJ0b29sX3VzZV9pZCI6InRvb2x1X3ZydHhfMDFNVlR3M1NxSnQ0VVZwbXBzOFM3TTFnIn19LHsiaWQiOjIsInR5cGUiOjQsImlkZV9zdGF0ZV9ub2RlIjp7IndvcmtzcGFjZUZvbGRlcnMiOlt7InJlcG9zaXRvcnlSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCIsImZvbGRlclJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn1dLCJ3b3Jrc3BhY2VGb2xkZXJzVW5jaGFuZ2VkIjpmYWxzZSwiY3VycmVudFRlcm1pbmFsIjp7InRlcm1pbmFsSWQiOjMsImN1cnJlbnRXb3JraW5nRGlyZWN0b3J5IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9fX1dLCJ0aW1lc3RhbXAiOiIyMDI1LTA1LTAzVDA2OjM2OjAyLjUwNFoiLCJyZXNwb25zZV90ZXh0IjoiMTguIEZpbmFsbHksIGxldCdzIGNyZWF0ZSB0aGUgbWFuYWdlLnB5IGZpbGU6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiYjU4NmYyNTAtNTYxMS00MGZlLWFmNDMtZjAyNGE5YjljYmJlIiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxSlR6aFFGVkdLZUVjcFJEeGpRRnBtVSIsInRvb2xfbmFtZSI6InNhdmUtZmlsZSIsImlucHV0X2pzb24iOiJ7XCJmaWxlX3BhdGhcIjogXCJuZXcvUkVBRE1FLm1kXCIsIFwiZmlsZV9jb250ZW50XCI6IFwiIyBEamFuZ28gSzJHRzYzNzMgUmVmYWN0b3JlZCBQcm9qZWN0XFxuXFxuVGhpcyBpcyBhIHJlZmFjdG9yZWQgdmVyc2lvbiBvZiB0aGUgRGphbmdvIEsyR0c2MzczIHByb2plY3QsIHdpdGggaW1wcm92ZWQgY29kZSBvcmdhbml6YXRpb24sIGRvY3VtZW50YXRpb24sIGFuZCBtYWludGFpbmFiaWxpdHkuXFxuXFxuIyMgUHJvamVjdCBTdHJ1Y3R1cmVcXG5cXG5gYGBcXG5uZXcvXFxuXFx1MjUxY1xcdTI1MDBcXHUyNTAwIGRqMi8gICAgICAgICAgICAgICAgICAjIE1haW4gRGphbmdvIHByb2plY3QgZGlyZWN0b3J5XFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBfX2luaXRfXy5weVxcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgc2V0dGluZ3MucHkgICAgICAgIyBQcm9qZWN0IHNldHRpbmdzXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCB1cmxzLnB5ICAgICAgICAgICAjIE1haW4gVVJMIHJvdXRpbmdcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIHZpZXdzLnB5ICAgICAgICAgICMgTWFpbiB2aWV3c1xcblxcdTI1MDIgICBcXHUyNTE0XFx1MjUwMFxcdTI1MDAgd3NnaS5weSAgICAgICAgICAgIyBXU0dJIGNvbmZpZ3VyYXRpb25cXG5cXHUyNTFjXFx1MjUwMFxcdTI1MDAgbWFpbi8gICAgICAgICAgICAgICAgICMgTWFpbiBhcHBsaWNhdGlvblxcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgX19pbml0X18ucHlcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIGFkbWluLnB5ICAgICAgICAgICMgQWRtaW4gY29uZmlndXJhdGlvblxcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgYXBwcy5weSAgICAgICAgICAgIyBBcHAgY29uZmlndXJhdGlvblxcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgY29uZmlnX21vZGVsLnB5ICAgIyBDb25maWd1cmF0aW9uIG1vZGVsXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBjb25maWdfdi5weSAgICAgICAjIENvbmZpZ3VyYXRpb24gdmlld3NcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIG1vZGVsLnB5ICAgICAgICAgICMgQmFzZSBtb2RlbFxcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgbW9kZWxzLnB5ICAgICAgICAgIyBBcHBsaWNhdGlvbiBtb2RlbHNcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIHNjaGVtYV92LnB5ICAgICAgICMgU2NoZW1hIHZpZXdzXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCB1cmxzLnB5ICAgICAgICAgICAjIEFwcGxpY2F0aW9uIFVSTCByb3V0aW5nXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCB1c2Vyc19tb2RlbC5weSAgICAjIFVzZXIgbW9kZWxcXG5cXHUyNTAyICAgXFx1MjUxNFxcdTI1MDBcXHUyNTAwIHVzZXJzX3YucHkgICAgICAgICMgVXNlciB2aWV3c1xcblxcdTI1MWNcXHUyNTAwXFx1MjUwMCB1dGlsLyAgICAgICAgICAgICAgICAgIyBVdGlsaXR5IGZ1bmN0aW9uc1xcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgX19pbml0X18ucHlcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIGF1dGgucHkgICAgICAgICAgICMgQXV0aGVudGljYXRpb24gdXRpbGl0aWVzXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBiYWlkdWJjZV9hcGkucHkgICAjIEJhaWR1IEJDRSBBUEkgdXRpbGl0aWVzXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBjb2Rlcy5weSAgICAgICAgICAjIFN0YXR1cyBjb2Rlc1xcblxcdTI1MDIgICBcXHUyNTFjXFx1MjUwMFxcdTI1MDAgY29tbW9uLnB5ICAgICAgICAgIyBDb21tb24gdXRpbGl0aWVzXFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBjb25maWdyZWFkLnB5ICAgICAjIENvbmZpZ3VyYXRpb24gcmVhZGluZyB1dGlsaXRpZXNcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIEN1c3RvbUpTT05FbmNvZGVyLnB5ICMgQ3VzdG9tIEpTT04gZW5jb2RlclxcblxcdTI1MDIgICBcXHUyNTE0XFx1MjUwMFxcdTI1MDAgbWVzc2FnZS5weSAgICAgICAgIyBNZXNzYWdlIHN0cmluZ3NcXG5cXHUyNTFjXFx1MjUwMFxcdTI1MDAgeG1pZGRsZXdhcmUvICAgICAgICAgICMgQ3VzdG9tIG1pZGRsZXdhcmVcXG5cXHUyNTAyICAgXFx1MjUxY1xcdTI1MDBcXHUyNTAwIF9faW5pdF9fLnB5XFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCB4YXV0aC5weSAgICAgICAgICAjIEF1dGhlbnRpY2F0aW9uIG1pZGRsZXdhcmVcXG5cXHUyNTAyICAgXFx1MjUxNFxcdTI1MDBcXHUyNTAwIHhwYXJhbS5weSAgICAgICAgICMgUGFyYW1ldGVyIHByb2Nlc3NpbmcgbWlkZGxld2FyZVxcblxcdTI1MWNcXHUyNTAwXFx1MjUwMCB0ZW1wbGF0ZXMvICAgICAgICAgICAgIyBUZW1wbGF0ZXMgZGlyZWN0b3J5XFxuXFx1MjUwMiAgIFxcdTI1MWNcXHUyNTAwXFx1MjUwMCBmcm9udC9hc3NldHMvICAgICAjIEZyb250LWVuZCBhc3NldHNcXG5cXHUyNTAyICAgXFx1MjUxNFxcdTI1MDBcXHUyNTAwIHVwbG9hZC8gICAgICAgICAgICMgVXBsb2FkIGRpcmVjdG9yeVxcblxcdTI1MTRcXHUyNTAwXFx1MjUwMCBtYW5hZ2UucHkgICAgICAgICAgICAgIyBEamFuZ28gbWFuYWdlbWVudCBzY3JpcHRcXG5gYGBcXG5cXG4jIyBGZWF0dXJlc1xcblxcbi0gSW1wcm92ZWQgY29kZSBvcmdhbml6YXRpb24gYW5kIHN0cnVjdHVyZVxcbi0gQmV0dGVyIGRvY3VtZW50YXRpb24gd2l0aCBkb2NzdHJpbmdzXFxuLSBNb3JlIGNvbnNpc3RlbnQgbmFtaW5nIGNvbnZlbnRpb25zXFxuLSBFbmhhbmNlZCBlcnJvciBoYW5kbGluZ1xcbi0gQ2xlYW5lciBhbmQgbW9yZSBtYWludGFpbmFibGUgY29kZVxcbi0gU2FtZSBmdW5jdGlvbmFsaXR5IGFzIHRoZSBvcmlnaW5hbCBwcm9qZWN0XFxuXFxuIyMgUmVxdWlyZW1lbnRzXFxuXFxuLSBQeXRob24gMy42K1xcbi0gRGphbmdvIDIuMCtcXG4tIE15U1FMIGRhdGFiYXNlXFxuXFxuIyMgU2V0dXBcXG5cXG4xLiBDbG9uZSB0aGUgcmVwb3NpdG9yeVxcbjIuIENyZWF0ZSBhIHZpcnR1YWwgZW52aXJvbm1lbnRcXG4zLiBJbnN0YWxsIGRlcGVuZGVuY2llczogYHBpcCBpbnN0YWxsIC1yIHJlcXVpcmVtZW50cy50eHRgXFxuNC4gQ29uZmlndXJlIHRoZSBkYXRhYmFzZSBpbiBgY29uZmlnLmluaWBcXG41LiBSdW4gbWlncmF0aW9uczogYHB5dGhvbiBtYW5hZ2UucHkgbWlncmF0ZWBcXG42LiBDcmVhdGUgYSBzdXBlcnVzZXI6IGBweXRob24gbWFuYWdlLnB5IGNyZWF0ZXN1cGVydXNlcmBcXG43LiBSdW4gdGhlIHNlcnZlcjogYHB5dGhvbiBtYW5hZ2UucHkgcnVuc2VydmVyYFxcblxcbiMjIFVzYWdlXFxuXFxuQWNjZXNzIHRoZSBhZG1pbiBpbnRlcmZhY2UgYXQgYC94YWRtaW4vYCBhbmQgdGhlIG1haW4gYXBwbGljYXRpb24gYXQgYC97c2NoZW1hTmFtZX0vYC5cXG5cXG4jIyBMaWNlbnNlXFxuXFxuVGhpcyBwcm9qZWN0IGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgTGljZW5zZS5cIn0iLCJtY3Bfc2VydmVyX25hbWUiOiIiLCJtY3BfdG9vbF9uYW1lIjoiIn19LHsiaWQiOjAsInR5cGUiOjAsImNvbnRlbnQiOiIxOS4gTGV0J3MgY3JlYXRlIGEgUkVBRE1FLm1kIGZpbGUgdG8gZXhwbGFpbiB0aGUgcmVmYWN0b3JlZCBwcm9qZWN0OlxuIn0seyJpZCI6MiwidHlwZSI6MiwiY29udGVudCI6IiJ9LHsiaWQiOjMsInR5cGUiOjMsImNvbnRlbnQiOiIifV0sInNlZW5fc3RhdGUiOiJzZWVuIiwic3RydWN0dXJlZF9yZXF1ZXN0X25vZGVzIjpbeyJpZCI6MSwidHlwZSI6MSwidG9vbF9yZXN1bHRfbm9kZSI6eyJjb250ZW50IjoiRmlsZSBzYXZlZCBzdWNjZXNzZnVsbHkuIiwiaXNfZXJyb3IiOmZhbHNlLCJyZXF1ZXN0X2lkIjoiIiwidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxMnlmUU0yNHBmS1ZUZGFkNmNFNm11RSJ9fSx7ImlkIjoyLCJ0eXBlIjo0LCJpZGVfc3RhdGVfbm9kZSI6eyJ3b3Jrc3BhY2VGb2xkZXJzIjpbeyJyZXBvc2l0b3J5Um9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQiLCJmb2xkZXJSb290IjoiL2hvbWUvbWFubml4L0Rlc2t0b3AvZGphbmdvazJnZzYzNzMtcmVidWlsZCJ9XSwid29ya3NwYWNlRm9sZGVyc1VuY2hhbmdlZCI6ZmFsc2UsImN1cnJlbnRUZXJtaW5hbCI6eyJ0ZXJtaW5hbElkIjozLCJjdXJyZW50V29ya2luZ0RpcmVjdG9yeSI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifX19XSwidGltZXN0YW1wIjoiMjAyNS0wNS0wM1QwNjozNjoxMC43MzRaIiwicmVzcG9uc2VfdGV4dCI6IjE5LiBMZXQncyBjcmVhdGUgYSBSRUFETUUubWQgZmlsZSB0byBleHBsYWluIHRoZSByZWZhY3RvcmVkIHByb2plY3Q6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiYmU2MTkyNDQtMTc1Ny00YTBhLTlhYmItZGM1MzZmOTczOTY4IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjoxLCJ0eXBlIjo1LCJjb250ZW50IjoiIiwidG9vbF91c2UiOnsidG9vbF91c2VfaWQiOiJ0b29sdV92cnR4XzAxRUJTQ2JhNUR1U0pXcW9jbzdtOFNYUyIsInRvb2xfbmFtZSI6ImxhdW5jaC1wcm9jZXNzIiwiaW5wdXRfanNvbiI6IntcImNvbW1hbmRcIjogXCJmaW5kIG5ldyAtdHlwZSBmIHwgc29ydFwiLCBcIndhaXRcIjogdHJ1ZSwgXCJtYXhfd2FpdF9zZWNvbmRzXCI6IDYwfSIsIm1jcF9zZXJ2ZXJfbmFtZSI6IiIsIm1jcF90b29sX25hbWUiOiIifX0seyJpZCI6MCwidHlwZSI6MCwiY29udGVudCI6Ik5vdyBsZXQncyB2ZXJpZnkgdGhhdCBhbGwgdGhlIG5lY2Vzc2FyeSBmaWxlcyBoYXZlIGJlZW4gY3JlYXRlZDpcbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkZpbGUgc2F2ZWQgc3VjY2Vzc2Z1bGx5LiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUpUemhRRlZHS2VFY3BSRHhqUUZwbVUifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6MywiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6MzY6MjcuMzIzWiIsInJlc3BvbnNlX3RleHQiOiJOb3cgbGV0J3MgdmVyaWZ5IHRoYXQgYWxsIHRoZSBuZWNlc3NhcnkgZmlsZXMgaGF2ZSBiZWVuIGNyZWF0ZWQ6XG4iLCJ3b3Jrc3BhY2VfZmlsZV9jaHVua3MiOltdfSx7InN0YXR1cyI6InN1Y2Nlc3MiLCJyZXF1ZXN0X2lkIjoiMTVhMjhlYmMtY2Q0NC00MWY4LTkyNDMtZTAxNTcxNWU4NGM1IiwicmVxdWVzdF9tZXNzYWdlIjoiIiwic3RydWN0dXJlZF9vdXRwdXRfbm9kZXMiOlt7ImlkIjowLCJ0eXBlIjowLCJjb250ZW50IjoiR3JlYXQhIEkndmUgc3VjY2Vzc2Z1bGx5IHJlZmFjdG9yZWQgdGhlIGVudGlyZSBEamFuZ28gcHJvamVjdCBhbmQgc2F2ZWQgaXQgaW4gdGhlIG5ldyBkaXJlY3RvcnkuIEhlcmUncyBhIHN1bW1hcnkgb2YgdGhlIGNoYW5nZXMgSSBtYWRlOlxuXG4jIyBTdW1tYXJ5IG9mIFJlZmFjdG9yaW5nXG5cbjEuICoqSW1wcm92ZWQgQ29kZSBPcmdhbml6YXRpb24qKjpcbiAgIC0gUmVzdHJ1Y3R1cmVkIHRoZSBwcm9qZWN0IHRvIGZvbGxvdyBEamFuZ28gYmVzdCBwcmFjdGljZXNcbiAgIC0gT3JnYW5pemVkIGZpbGVzIGluIGEgbW9yZSBsb2dpY2FsIG1hbm5lclxuICAgLSBDcmVhdGVkIGEgY2xlYXIgc2VwYXJhdGlvbiBvZiBjb25jZXJucyBiZXR3ZWVuIG1vZGVscywgdmlld3MsIGFuZCB1dGlsaXRpZXNcblxuMi4gKipFbmhhbmNlZCBEb2N1bWVudGF0aW9uKio6XG4gICAtIEFkZGVkIGRvY3N0cmluZ3MgdG8gYWxsIGZ1bmN0aW9ucyBhbmQgY2xhc3Nlc1xuICAgLSBJbmNsdWRlZCBjb21tZW50cyBleHBsYWluaW5nIGNvbXBsZXggbG9naWNcbiAgIC0gQ3JlYXRlZCBhIGNvbXByZWhlbnNpdmUgUkVBRE1FLm1kIGZpbGVcblxuMy4gKipDb2RlIFF1YWxpdHkgSW1wcm92ZW1lbnRzKio6XG4gICAtIFN0YW5kYXJkaXplZCBuYW1pbmcgY29udmVudGlvbnNcbiAgIC0gSW1wcm92ZWQgZXJyb3IgaGFuZGxpbmdcbiAgIC0gUmVtb3ZlZCByZWR1bmRhbnQgY29kZVxuICAgLSBNYWRlIHRoZSBjb2RlIG1vcmUgcmVhZGFibGUgYW5kIG1haW50YWluYWJsZVxuXG40LiAqKkZ1bmN0aW9uYWxpdHkgUHJlc2VydmF0aW9uKio6XG4gICAtIE1haW50YWluZWQgYWxsIHRoZSBvcmlnaW5hbCBmdW5jdGlvbmFsaXR5XG4gICAtIEVuc3VyZWQgYmFja3dhcmQgY29tcGF0aWJpbGl0eSB3aXRoIGV4aXN0aW5nIGRhdGFcbiAgIC0gUHJlc2VydmVkIHRoZSBBUEkgZW5kcG9pbnRzIGFuZCB0aGVpciBiZWhhdmlvclxuXG41LiAqKlNlY3VyaXR5IEVuaGFuY2VtZW50cyoqOlxuICAgLSBJbXByb3ZlZCBhdXRoZW50aWNhdGlvbiBoYW5kbGluZ1xuICAgLSBCZXR0ZXIgcGFyYW1ldGVyIHZhbGlkYXRpb25cbiAgIC0gTW9yZSBzZWN1cmUgZmlsZSBoYW5kbGluZ1xuXG5UaGUgcmVmYWN0b3JlZCBwcm9qZWN0IGlzIG5vdyBtb3JlIG1haW50YWluYWJsZSwgZWFzaWVyIHRvIHVuZGVyc3RhbmQsIGFuZCBmb2xsb3dzIGJldHRlciBjb2RpbmcgcHJhY3RpY2VzIHdoaWxlIHByZXNlcnZpbmcgYWxsIHRoZSBvcmlnaW5hbCBmdW5jdGlvbmFsaXR5LiBUaGUgY29kZSBpcyBub3cgYmV0dGVyIG9yZ2FuaXplZCwgd2l0aCBjbGVhciBzZXBhcmF0aW9uIG9mIGNvbmNlcm5zIGFuZCBpbXByb3ZlZCBkb2N1bWVudGF0aW9uLlxuXG5Xb3VsZCB5b3UgbGlrZSBtZSB0byBleHBsYWluIGFueSBzcGVjaWZpYyBwYXJ0IG9mIHRoZSByZWZhY3RvcmluZyBpbiBtb3JlIGRldGFpbD9cbiJ9LHsiaWQiOjIsInR5cGUiOjIsImNvbnRlbnQiOiIifSx7ImlkIjozLCJ0eXBlIjozLCJjb250ZW50IjoiIn1dLCJzZWVuX3N0YXRlIjoic2VlbiIsInN0cnVjdHVyZWRfcmVxdWVzdF9ub2RlcyI6W3siaWQiOjEsInR5cGUiOjEsInRvb2xfcmVzdWx0X25vZGUiOnsiY29udGVudCI6IkhlcmUgYXJlIHRoZSByZXN1bHRzIGZyb20gZXhlY3V0aW5nIHRoZSBjb21tYW5kLlxuICAgICAgICA8cmV0dXJuLWNvZGU+XG4gICAgICAgIDBcbiAgICAgICAgPC9yZXR1cm4tY29kZT5cbiAgICAgICAgPHN0ZG91dD5cbiAgICAgICAgbmV3L2RqMi9fX2luaXRfXy5weVxubmV3L2RqMi9zZXR0aW5ncy5weVxubmV3L2RqMi91cmxzLnB5XG5uZXcvZGoyL3ZpZXdzLnB5XG5uZXcvZGoyL3dzZ2kucHlcbm5ldy9tYWluL2FkbWluLnB5XG5uZXcvbWFpbi9hcHBzLnB5XG5uZXcvbWFpbi9jb25maWdfbW9kZWwucHlcbm5ldy9tYWluL2NvbmZpZ192LnB5XG5uZXcvbWFpbi9fX2luaXRfXy5weVxubmV3L21haW4vbW9kZWwucHlcbm5ldy9tYWluL21vZGVscy5weVxubmV3L21haW4vc2NoZW1hX3YucHlcbm5ldy9tYWluL3VybHMucHlcbm5ldy9tYWluL3VzZXJzX21vZGVsLnB5XG5uZXcvbWFpbi91c2Vyc192LnB5XG5uZXcvbWFuYWdlLnB5XG5uZXcvUkVBRE1FLm1kXG5uZXcvdXRpbC9hdXRoLnB5XG5uZXcvdXRpbC9iYWlkdWJjZV9hcGkucHlcbm5ldy91dGlsL2NvZGVzLnB5XG5uZXcvdXRpbC9jb21tb24ucHlcbm5ldy91dGlsL2NvbmZpZ3JlYWQucHlcbm5ldy91dGlsL0N1c3RvbUpTT05FbmNvZGVyLnB5XG5uZXcvdXRpbC9fX2luaXRfXy5weVxubmV3L3V0aWwvbWVzc2FnZS5weVxubmV3L3htaWRkbGV3YXJlL19faW5pdF9fLnB5XG5uZXcveG1pZGRsZXdhcmUveGF1dGgucHlcbm5ldy94bWlkZGxld2FyZS94cGFyYW0ucHlcblxuICAgICAgICA8L3N0ZG91dD5cbiAgICAgICAgPHN0ZGVycj5cbiAgICAgICAgXG4gICAgICAgIDwvc3RkZXJyPiIsImlzX2Vycm9yIjpmYWxzZSwicmVxdWVzdF9pZCI6IiIsInRvb2xfdXNlX2lkIjoidG9vbHVfdnJ0eF8wMUVCU0NiYTVEdVNKV3FvY283bThTWFMifX0seyJpZCI6MiwidHlwZSI6NCwiaWRlX3N0YXRlX25vZGUiOnsid29ya3NwYWNlRm9sZGVycyI6W3sicmVwb3NpdG9yeVJvb3QiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIiwiZm9sZGVyUm9vdCI6Ii9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGQifV0sIndvcmtzcGFjZUZvbGRlcnNVbmNoYW5nZWQiOmZhbHNlLCJjdXJyZW50VGVybWluYWwiOnsidGVybWluYWxJZCI6NCwiY3VycmVudFdvcmtpbmdEaXJlY3RvcnkiOiIvaG9tZS9tYW5uaXgvRGVza3RvcC9kamFuZ29rMmdnNjM3My1yZWJ1aWxkIn19fV0sInRpbWVzdGFtcCI6IjIwMjUtMDUtMDNUMDY6Mzg6MzAuNDE0WiIsInJlc3BvbnNlX3RleHQiOiJHcmVhdCEgSSd2ZSBzdWNjZXNzZnVsbHkgcmVmYWN0b3JlZCB0aGUgZW50aXJlIERqYW5nbyBwcm9qZWN0IGFuZCBzYXZlZCBpdCBpbiB0aGUgbmV3IGRpcmVjdG9yeS4gSGVyZSdzIGEgc3VtbWFyeSBvZiB0aGUgY2hhbmdlcyBJIG1hZGU6XG5cbiMjIFN1bW1hcnkgb2YgUmVmYWN0b3JpbmdcblxuMS4gKipJbXByb3ZlZCBDb2RlIE9yZ2FuaXphdGlvbioqOlxuICAgLSBSZXN0cnVjdHVyZWQgdGhlIHByb2plY3QgdG8gZm9sbG93IERqYW5nbyBiZXN0IHByYWN0aWNlc1xuICAgLSBPcmdhbml6ZWQgZmlsZXMgaW4gYSBtb3JlIGxvZ2ljYWwgbWFubmVyXG4gICAtIENyZWF0ZWQgYSBjbGVhciBzZXBhcmF0aW9uIG9mIGNvbmNlcm5zIGJldHdlZW4gbW9kZWxzLCB2aWV3cywgYW5kIHV0aWxpdGllc1xuXG4yLiAqKkVuaGFuY2VkIERvY3VtZW50YXRpb24qKjpcbiAgIC0gQWRkZWQgZG9jc3RyaW5ncyB0byBhbGwgZnVuY3Rpb25zIGFuZCBjbGFzc2VzXG4gICAtIEluY2x1ZGVkIGNvbW1lbnRzIGV4cGxhaW5pbmcgY29tcGxleCBsb2dpY1xuICAgLSBDcmVhdGVkIGEgY29tcHJlaGVuc2l2ZSBSRUFETUUubWQgZmlsZVxuXG4zLiAqKkNvZGUgUXVhbGl0eSBJbXByb3ZlbWVudHMqKjpcbiAgIC0gU3RhbmRhcmRpemVkIG5hbWluZyBjb252ZW50aW9uc1xuICAgLSBJbXByb3ZlZCBlcnJvciBoYW5kbGluZ1xuICAgLSBSZW1vdmVkIHJlZHVuZGFudCBjb2RlXG4gICAtIE1hZGUgdGhlIGNvZGUgbW9yZSByZWFkYWJsZSBhbmQgbWFpbnRhaW5hYmxlXG5cbjQuICoqRnVuY3Rpb25hbGl0eSBQcmVzZXJ2YXRpb24qKjpcbiAgIC0gTWFpbnRhaW5lZCBhbGwgdGhlIG9yaWdpbmFsIGZ1bmN0aW9uYWxpdHlcbiAgIC0gRW5zdXJlZCBiYWNrd2FyZCBjb21wYXRpYmlsaXR5IHdpdGggZXhpc3RpbmcgZGF0YVxuICAgLSBQcmVzZXJ2ZWQgdGhlIEFQSSBlbmRwb2ludHMgYW5kIHRoZWlyIGJlaGF2aW9yXG5cbjUuICoqU2VjdXJpdHkgRW5oYW5jZW1lbnRzKio6XG4gICAtIEltcHJvdmVkIGF1dGhlbnRpY2F0aW9uIGhhbmRsaW5nXG4gICAtIEJldHRlciBwYXJhbWV0ZXIgdmFsaWRhdGlvblxuICAgLSBNb3JlIHNlY3VyZSBmaWxlIGhhbmRsaW5nXG5cblRoZSByZWZhY3RvcmVkIHByb2plY3QgaXMgbm93IG1vcmUgbWFpbnRhaW5hYmxlLCBlYXNpZXIgdG8gdW5kZXJzdGFuZCwgYW5kIGZvbGxvd3MgYmV0dGVyIGNvZGluZyBwcmFjdGljZXMgd2hpbGUgcHJlc2VydmluZyBhbGwgdGhlIG9yaWdpbmFsIGZ1bmN0aW9uYWxpdHkuIFRoZSBjb2RlIGlzIG5vdyBiZXR0ZXIgb3JnYW5pemVkLCB3aXRoIGNsZWFyIHNlcGFyYXRpb24gb2YgY29uY2VybnMgYW5kIGltcHJvdmVkIGRvY3VtZW50YXRpb24uXG5cbldvdWxkIHlvdSBsaWtlIG1lIHRvIGV4cGxhaW4gYW55IHNwZWNpZmljIHBhcnQgb2YgdGhlIHJlZmFjdG9yaW5nIGluIG1vcmUgZGV0YWlsP1xuIiwid29ya3NwYWNlX2ZpbGVfY2h1bmtzIjpbXX1dLCJmZWVkYmFja1N0YXRlcyI6eyJ0ZW1wLWZlLWYxMjA3MTJmLWNkMTYtNDMyMi05YTc2LTM5N2NkZTI1YzIzOSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtOTMzZDNkOTMtZTljOC00OGMzLTgxYzctY2E4OTFlZWI5ZTE1Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1jMjg5MmMxOC0yYjA3LTRiODctODliNC0wYzY1MzNmMjU5ODIiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTY5ZWJiNDY2LTgxMTMtNDEzMi05MGVhLTJhYTYzNDU4YTVmMyI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMzgyZmQxYjItMTkxMy00Mjg2LWExMTAtOTRkNjI5ODYwZWQ1Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS05NmFmYjcxZC1hYjBjLTQ4MjItODQzOS04MGNkNDM4NDcxYzUiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWNkZTM0YzA3LTQ0ZjAtNDkyYy05ODMzLWFjZmIxNmYzOWJlMSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtYzk3YjEyZDctZDIxMy00MzZkLTg1YTUtZjJjMWU2N2ViZTAwIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1jMzY4MjExMS0zMzRiLTRkZmEtOTAyNi0yYThkYzkzMjBhMzkiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTQ1Y2RhN2ZhLWQ1YjItNDIxNy05Y2EyLWZmY2Y3NTExYWRjMSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMjNkY2QxYWQtMGEwMi00MmEyLTlhOWEtMjdjOWQwMTEzOGNhIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS00NTMwYzdjZi0zNjc3LTQ4MzktYmQyMS00ZDE1MGVhMWIwZDQiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWYzZTAzMmJlLTUwNDAtNDFjOC1hM2UyLTBjMWY0ZDY3ZDY4MyI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMzA4YzFhMzItOTJlYy00MDdhLWJiMmQtMzJlYzkxMmE0MTZiIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1jZjY0MjI3Ny0zOGY1LTRiMWYtYmI2Zi1kNDk2OTQzMzJjMmIiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTUzYmY5MWEwLTYxMDEtNGQ1Ny1hMGRiLTdiMmM4YWI1Zjk3NiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMjM2MjU1NDQtNzFjZC00YzkwLTlmODUtOTA1OTQ3YTNiZmE2Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS0zODgxYTVmMi1kOWJmLTQ4NGItODNmMi0yZGZmMDJiMzA2ODQiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTg2MGEzNzVjLWQ0ZTctNGQwYy1iOGMzLTFkZjZmYzkxMDBkYSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtYzhhMzI3MjMtYTlmYS00MjVkLWIwNTYtNmNkNjM4MjliZWU4Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1iMDRlYmQ1OS1hNWY2LTRmYjktOTI4ZS1hMGI5MGRhMmE5YzMiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWZjMzM1M2JjLWEwNzgtNDZjNy1hN2FjLWQxOGFhMWUwZTMzYSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtNWVlNzdhNTItMmMyNS00YWEyLTgyMWEtZDJkZWE0ODZmM2YxIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1mZDk5MWM3MC1iY2ZiLTRmYTEtYTA2ZS01NDE2MDY0NmU4OWMiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLThjNThjMDZlLTA0YWQtNDAzNS05M2I2LWZmYTY5YTgyOTVkMCI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMGVhZjAxYzMtZDA4YS00MDYzLTg2YmEtMWQyMmZjZGEyZjI0Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS1iYjgzYWFmYi1kZWQ2LTRhODctODgzNy03OTExZjRlYjlkYmUiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTY5NDc5YjliLWIyNzItNGU2Ny05ZGE0LTRhNGM0MmYwMThjZCI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMzZjNjM3MjctNjQyNi00ZjNiLWJjM2ItNTQyMmE5ZDBmN2ZjIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS03MmMxNDgzZS1iNTBmLTRiMjktYTM1MS01ZWYxYmM4ODVkMzkiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLWRiNDA1ZjNkLTEzN2MtNDA1Ni04N2M0LWM4NDY4OTRiNDk3ZiI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtYWIxY2VkOGQtOTJkYS00YWZlLTk1OTctNmVhYjVlMDRhODI1Ijp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS02MDg5MzJiOC03OWY1LTQyZmMtYTcyMy0xMWYwYjc5NTk3ODEiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9LCJ0ZW1wLWZlLTEyYzYzYjIxLTdkMWMtNDdhNC1hMWZhLTllNzdmOTBkNDMzNSI6eyJzZWxlY3RlZFJhdGluZyI6MCwiZmVlZGJhY2tOb3RlIjoiIn0sInRlbXAtZmUtMTIwYWExYjctMjk4Zi00Y2I3LTkwN2ItMzhlNzRjZTA1YTExIjp7InNlbGVjdGVkUmF0aW5nIjowLCJmZWVkYmFja05vdGUiOiIifSwidGVtcC1mZS0zMGYxNTQ0OC04ODU1LTQ3YmUtOGE5NS0wMmNmZGMxYWM2NGIiOnsic2VsZWN0ZWRSYXRpbmciOjAsImZlZWRiYWNrTm90ZSI6IiJ9fSwidG9vbFVzZVN0YXRlcyI6eyIwOTU0ODhhNy1mYzg0LTQ1MDItODZkMC0xMzIyOGYzOTY1ZGU7dG9vbHVfdnJ0eF8wMTIyaExBZGJSWVJRNHc5YW9zY2NuQ2oiOnsicmVxdWVzdElkIjoiMDk1NDg4YTctZmM4NC00NTAyLTg2ZDAtMTMyMjhmMzk2NWRlIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTIyaExBZGJSWVJRNHc5YW9zY2NuQ2oiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJIZXJlIGFyZSB0aGUgcmVzdWx0cyBmcm9tIGV4ZWN1dGluZyB0aGUgY29tbWFuZC5cbiAgICAgICAgPHJldHVybi1jb2RlPlxuICAgICAgICAwXG4gICAgICAgIDwvcmV0dXJuLWNvZGU+XG4gICAgICAgIDxzdGRvdXQ+XG4gICAgICAgIC4vZGoyL19faW5pdF9fLnB5XG4uL2RqMi9zZXR0aW5ncy5weVxuLi9kajIvdXJscy5weVxuLi9kajIvdmlld3MucHlcbi4vZGoyL3dzZ2kucHlcbi4vbWFpbi9hZG1pbi5weVxuLi9tYWluL2FwcHMucHlcbi4vbWFpbi9DYW5nY2h1X3YucHlcbi4vbWFpbi9DaHVrdV92LnB5XG4uL21haW4vY29uZmlnX21vZGVsLnB5XG4uL21haW4vY29uZmlnX3YucHlcbi4vbWFpbi9HdW9waW54aW54aV92LnB5XG4uL21haW4vR3VveXVhbnhpbnhpX3YucHlcbi4vbWFpbi9HdW95dWFuemh1YW5ndGFpX3YucHlcbi4vbWFpbi9fX2luaXRfXy5weVxuLi9tYWluL21vZGVsLnB5XG4uL21haW4vbW9kZWxzLnB5XG4uL21haW4vTm9uZ2h1X3YucHlcbi4vbWFpbi9SdWt1X3YucHlcbi4vbWFpbi9zY2hlbWFfdi5weVxuLi9tYWluL3Rlc3RzLnB5XG4uL21haW4vdXJscy5weVxuLi9tYWluL3VzZXJzX21vZGVsLnB5XG4uL21haW4vdXNlcnNfdi5weVxuLi9tYWluL3ZpZXdzLnB5XG4uL21haW4vV3VsaXVfdi5weVxuLi9tYWluL1lvbmdodV92LnB5XG4uL21haW4vWmhpbGlhbmdkZW5namlfdi5weVxuLi9tYW5hZ2UucHlcbi4vdXRpbC9hdXRoLnB5XG4uL3V0aWwvYmFpZHViY2VfYXBpLnB5XG4uL3V0aWwvY29kZXMucHlcbi4vdXRpbC9jb21tb24ucHlcbi4vdXRpbC9jb25maWdyZWFkLnB5XG4uL3V0aWwvQ3VzdG9tSlNPTkVuY29kZXIucHlcbi4vdXRpbC9fX2luaXRfXy5weVxuLi91dGlsL2xvY2F0ZS5weVxuLi91dGlsL21lc3NhZ2UucHlcbi4vdXRpbC9zcWxpbml0LnB5XG4uL3htaWRkbGV3YXJlL19faW5pdF9fLnB5XG4uL3htaWRkbGV3YXJlL3hhdXRoLnB5XG4uL3htaWRkbGV3YXJlL3hwYXJhbS5weVxuXG4gICAgICAgIDwvc3Rkb3V0PlxuICAgICAgICA8c3RkZXJyPlxuICAgICAgICBcbiAgICAgICAgPC9zdGRlcnI+IiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiNzZjYTQ5MzgtMzUwOS00YjU0LTk5YWMtNGVjNjQ4NTY1ZGU0O3Rvb2x1X3ZydHhfMDFDeTQxeXBQUVlhNExLeGpFbXQ4bXgxIjp7InJlcXVlc3RJZCI6Ijc2Y2E0OTM4LTM1MDktNGI1NC05OWFjLTRlYzY0ODU2NWRlNCIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFDeTQxeXBQUVlhNExLeGpFbXQ4bXgxIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogZGoyL3VybHMucHlcbi4uLlxuZnJvbSBkamFuZ28udmlld3MuZ2VuZXJpYyBpbXBvcnQgVGVtcGxhdGVWaWV3XG5cbmZyb20gLiBpbXBvcnQgdmlld3NcbiMgZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5cbnNjaGVtYU5hbWUgPSBcImRqYW5nb2syZ2c2MzczXCJcblxudXJscGF0dGVybnMgPSBbXG4gICAgcGF0aCgneGFkbWluLycsIGFkbWluLnNpdGUudXJscyksXG4gICAgcGF0aChyJ2luZGV4Lycsdmlld3MuaW5kZXgpLFxuICAgIHBhdGgoJ3t9LycuZm9ybWF0KHNjaGVtYU5hbWUpLGluY2x1ZGUoJ21haW4udXJscycpKSwj5a+85YWlc2NoZW1hTmFtZVxuICAgIHJlX3BhdGgocidhZG1pbi9saWIvKD9QPHAxPi4qKS8oP1A8cDI+LiopJCcsIHZpZXdzLmFkbWluX2xpYjIpLFxuICAgIHJlX3BhdGgocidhZG1pbi9saWIvKD9QPHAxPi4qKS8oP1A8cDI+LiopLyg/UDxwMz4uKikkJywgdmlld3MuYWRtaW5fbGliMyksXG4gICAgcmVfcGF0aChyJ2FkbWluL2xpYi8oP1A8cDE+LiopLyg/UDxwMj4uKikvKD9QPHAzPi4qKS8oP1A8cDQ+LiopJCcsIHZpZXdzLmFkbWluX2xpYjQpLFxuICAgIHJlX3BhdGgocidhZG1pbi9wYWdlLyg/UDxwMT4uKikkJywgdmlld3MuYWRtaW5fcGFnZSksXG4gICAgcmVfcGF0aChyJ2FkbWluL3BhZ2UvKD9QPHAxPi4qKS8oP1A8cDI+LiopJCcsIHZpZXdzLmFkbWluX3BhZ2UyKSxcbiAgICByZV9wYXRoKHInYWRtaW4vcGFnZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlcyksXG4gICAgcmVfcGF0aChyJ2FkbWluL3BhZ2VzLyg/UDxwMT4uKikvKD9QPHAyPi4qKSQnLCB2aWV3cy5hZG1pbl9wYWdlczIpLFxuLi4uXG4gICAgcmVfcGF0aChyJ2xheXVpLyg/UDxwMT4uKikvKD9QPHAyPi4qKS8oP1A8cDM+LiopLyg/UDxwND4uKikkJywgIHZpZXdzLmxheXVpNCksXG4gICAgcmVfcGF0aChyJ3BhZ2VzLyg/UDxwMT4uKikkJywgdmlld3MuZnJvbnRfcGFnZXMpLFxuICAgIHJlX3BhdGgocidwYWdlcy8oP1A8cDE+LiopLyg/UDxwMj4uKikkJywgIHZpZXdzLmZyb250X3BhZ2VzMiksXG4gICAgIyByZV9wYXRoKHIncGFnZXMvKD9QPHAxPi4qKSQnLCAgdmlld3MuZnJvbnRfZmlsZTEpLFxuICAgICMgcmVfcGF0aChyJyg/UDxwMT5jc3N8anNzfGltZ3xpbWFnZXxpYW1nZXN8Zm9udHxmb250cykvKD9QPHAyPi4qKSQnLCB2aWV3cy5mcm9udF9maWxlMiksXG4gICAgcmVfcGF0aChyJ21vZHVsZXMvKD9QPHAxPi4qKSQnLCB2aWV3cy5mcm9udF9tb2R1bGVzKSxcbiAgICByZV9wYXRoKHInY3NzLyg/UDxwMT4uKikkJywgdmlld3MuY3NzMSksXG4gICAgcmVfcGF0aChyJ2pzLyg/UDxwMT4uKikkJywgdmlld3MuanMxKSxcbiAgICByZV9wYXRoKHInaW1nLyg/UDxwMT4uKikkJywgdmlld3MuaW1nMSksXG4gICAgcGF0aChyJ3Rlc3QvPHN0cjpwMT4vJyx2aWV3cy50ZXN0KSxcbiAgICBwYXRoKHInbnVsbCcsdmlld3MubnVsbCksXG5dXG4uLi5cblBhdGg6IG1haW4vdXJscy5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IG9zXG5mcm9tIGRqYW5nby51cmxzIGltcG9ydCBwYXRoXG5mcm9tIG1haW4gaW1wb3J0IHVzZXJzX3YsIGNvbmZpZ192LCBzY2hlbWFfdlxuIyBmcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgZGJOYW1lIGFzIHNjaGVtYU5hbWVcbiMgdXJs6KeE5YiZ5YiX6KGoXG51cmxwYXR0ZXJucyA9IFtcbiAgICBwYXRoKHIndXNlcnMvcmVnaXN0ZXInLCB1c2Vyc192LnVzZXJzX3JlZ2lzdGVyKSxcbiAgICBwYXRoKHIndXNlcnMvbG9naW4nLCB1c2Vyc192LnVzZXJzX2xvZ2luKSxcbiAgICBwYXRoKHIndXNlcnMvbG9nb3V0JywgdXNlcnNfdi51c2Vyc19sb2dvdXQpLFxuICAgIHBhdGgocid1c2Vycy9zZXNzaW9uJywgdXNlcnNfdi51c2Vyc19zZXNzaW9uKSxcbiAgICBwYXRoKHIndXNlcnMvcGFnZScsIHVzZXJzX3YudXNlcnNfcGFnZSksXG4gICAgcGF0aChyJ3VzZXJzL3NhdmUnLCB1c2Vyc192LnVzZXJzX3NhdmUpLFxuICAgIHBhdGgocid1c2Vycy9pbmZvLzxpZF8+JywgdXNlcnNfdi51c2Vyc19pbmZvKSxcbiAgICBwYXRoKHIndXNlcnMvdXBkYXRlJywgdXNlcnNfdi51c2Vyc191cGRhdGUpLFxuICAgIHBhdGgocid1c2Vycy9kZWxldGUnLCB1c2Vyc192LnVzZXJzX2RlbGV0ZSksXG5cbiAgICBwYXRoKHInY29uZmlnL3BhZ2UnLCBjb25maWdfdi5jb25maWdfcGFnZSksXG4gICAgcGF0aChyJ2NvbmZpZy9saXN0JywgY29uZmlnX3YuY29uZmlnX2xpc3QpLFxuICAgIHBhdGgocidjb25maWcvc2F2ZScsIGNvbmZpZ192LmNvbmZpZ19zYXZlKSxcbiAgICBwYXRoKHInY29uZmlnL2FkZCcsIGNvbmZpZ192LmNvbmZpZ19hZGQpLFxuICAgIHBhdGgocidjb25maWcvaW5mby88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19pbmZvKSxcbiAgICBwYXRoKHInY29uZmlnL2RldGFpbC88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19kZXRhaWwpLFxuICAgIHBhdGgocidjb25maWcvdXBkYXRlJywgY29uZmlnX3YuY29uZmlnX3VwZGF0ZSksXG4gICAgcGF0aChyJ2NvbmZpZy9kZWxldGUnLCBjb25maWdfdi5jb25maWdfZGVsZXRlKSxcblxuXVxuIyBtYWluIGFwcOeahOi3r+W+hFxubWFpbkRpciA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJtYWluXCIpXG5cbiMg6L+H5ruk5paH5Lu255qE5YiX6KGoXG5leGNsdWRlTGlzdCA9IFtcbiAgICBcInNjaGVtYV92LnB5XCIsXG5cbiAgICBcInVzZXJzX3YucHlcIixcbiAgICBcImNvbmZpZ192LnB5XCIsXG5dXG5cbiMg5b6q546v5b2T5YmN55uu5b2V5LiL55qEcHnmlofku7Zcblxudmlld190dXBsZSA9IHNldCgpXG5mb3IgaSBpbiBvcy5saXN0ZGlyKG1haW5EaXIpOlxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpWy01Ol0gPT0gXCJfdi5weVwiOlxuICAgICAgICB2aWV3TmFtZSA9IGlbOi0zXSAgIyDljrvmjokucHnlkI7nvIDlrZfnrKbkuLJcbiAgICAgICAgdmlld190dXBsZS5hZGQoXCJmcm9tIG1haW4gaW1wb3J0IHt9XCIuZm9ybWF0KHZpZXdOYW1lKSlcbi4uLlxuXG4gICAgICAgIHVybHBhdHRlcm5zLmV4dGVuZChcbiAgICAgICAgICAgIFtcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vcmVnaXN0ZXInLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9yZWdpc3RlclwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vbG9naW4nLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9sb2dpblwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbiAgICAgICAgICAgICAgICBwYXRoKHIne30vbG9nb3V0Jy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxuICAgICAgICAgICAgICAgICAgICAgZXZhbChcInt9X3Yue31fbG9nb3V0XCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxuICAgICAgICAgICAgICAgIHBhdGgocid7fS9yZXNldFBhc3MnLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9yZXNldFBhc3NcIi5mb3JtYXQodGFibGVOYW1lLmNhcGl0YWxpemUoKSwgdGFibGVOYW1lLmxvd2VyKCkpKSksXG4gICAgICAgICAgICAgICAgcGF0aChyJ3t9L3Nlc3Npb24nLmZvcm1hdCh0YWJsZU5hbWUubG93ZXIoKSksXG4gICAgICAgICAgICAgICAgICAgICBldmFsKFwie31fdi57fV9zZXNzaW9uXCIuZm9ybWF0KHRhYmxlTmFtZS5jYXBpdGFsaXplKCksIHRhYmxlTmFtZS5sb3dlcigpKSkpLFxuICAgICAgICAgICAgICAgIHBhdGgocid7fS9kZWZhdWx0Jy5mb3JtYXQodGFibGVOYW1lLmxvd2VyKCkpLFxuICAgICAgICAgICAgICAgICAgICAgZXZhbChcInt9X3Yue31fZGVmYXVsdFwiLmZvcm1hdCh0YWJsZU5hbWUuY2FwaXRhbGl6ZSgpLCB0YWJsZU5hbWUubG93ZXIoKSkpKSxcbi4uLlxudXJscGF0dGVybnMuZXh0ZW5kKFxuICAgIFtcbiAgICAgICAgcGF0aChyJ2NhbC88c3RyOnRhYmxlTmFtZT4vPHN0cjpjb2x1bW5OYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfY2FsKSxcbiAgICAgICAgcGF0aChyJ2ZpbGUvZG93bmxvYWQnLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZpbGVfZG93bmxvYWQpLFxuICAgICAgICBwYXRoKHInZmlsZS91cGxvYWQnLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZpbGVfdXBsb2FkKSxcbiAgICAgICAgcGF0aChyJ2ZvbGxvdy88dGFibGVOYW1lPi88Y29sdW1uTmFtZT4vPGxldmVsPi88cGFyZW50PicsIHNjaGVtYV92LnNjaGVtYU5hbWVfZm9sbG93X2xldmVsKSxcbiAgICAgICAgcGF0aChyJ2ZvbGxvdy88dGFibGVOYW1lPi88Y29sdW1uTmFtZT4nLCBzY2hlbWFfdi5zY2hlbWFOYW1lX2ZvbGxvdyksXG4gICAgICAgIHBhdGgocidsb2NhdGlvbicsIHNjaGVtYV92LnNjaGVtYU5hbWVfbG9jYXRpb24pLFxuICAgICAgICBwYXRoKHInbWF0Y2hGYWNlJywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9tYXRjaGZhY2UpLFxuICAgICAgICBwYXRoKHInb3B0aW9uLzx0YWJsZU5hbWU+Lzxjb2x1bW5OYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfb3B0aW9uKSxcbiAgICAgICAgcGF0aChyJ3NoLzx0YWJsZU5hbWU+Jywgc2NoZW1hX3Yuc2NoZW1hTmFtZV9zaCksXG4gICAgICAgIHBhdGgocid1cGxvYWQvPGZpbGVOYW1lPicsIHNjaGVtYV92LnNjaGVtYU5hbWVfdXBsb2FkKSxcbiAgICAgICAgcGF0aChyJ3VwbG9hZC88dGFibGVOYW1lPi88ZmlsZU5hbWU+Jywgc2NoZW1hX3Yuc2NoZW1hTmFtZV91cGxvYWRfZm9yZWNhc3QpLFxuLi4uXG5QYXRoOiBtYWluL2FkbWluLnB5XG5mcm9tIGRqYW5nby5jb250cmliIGltcG9ydCBhZG1pblxuXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzLCBBcHBDb25maWdcbiMgUmVnaXN0ZXIgeW91ciBtb2RlbHMgaGVyZS5cblxuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5mcm9tIG1haW4udXNlcnNfbW9kZWwgaW1wb3J0IHVzZXJzXG5mcm9tIG1haW4uY29uZmlnX21vZGVsIGltcG9ydCBjb25maWdcblxudHJ5OlxuICAgIGZyb20gbWFpbi5tb2RlbHMgaW1wb3J0ICpcbmV4Y2VwdDpcbiAgICBwYXNzXG4jIGNoYW5nZSB0aXRsZVxuYWRtaW4uc2l0ZS5zaXRlX3RpdGxlID0gc2NoZW1hTmFtZSAgIyDorr7nva7pobXpnaLmoIfpophcbmFkbWluLnNpdGUuc2l0ZV9oZWFkZXIgPSBzY2hlbWFOYW1lICAjIOiuvue9rue9keermemhteWktFxuYWRtaW4uc2l0ZS5pbmRleF90aXRsZSA9IHNjaGVtYU5hbWUgICMg6K6+572u6aaW6aG15qCH6K+tXG5cbmFsbE1vZGVscyA9IGFwcHMuZ2V0X2FwcF9jb25maWcoJ21haW4nKS5nZXRfbW9kZWxzKClcblxuZm9yIGluZCwgbW9kZWwgaW4gZW51bWVyYXRlKGFsbE1vZGVscyk6XG5cbiAgICBjbGFzcyBtb2RlbHNpdGUoYWRtaW4uTW9kZWxBZG1pbik6XG4gICAgICAgIGxpc3RfZGlzcGxheSA9IFtdXG4gICAgICAgIGZvciBjb2wgaW4gbW9kZWwuX21ldGEuZmllbGRzOlxuICAgICAgICAgICAgbGlzdF9kaXNwbGF5LmFwcGVuZChjb2wubmFtZSlcblxuICAgICAgICBzZWFyY2hfZmllbGRzID0gbGlzdF9kaXNwbGF5XG5cblxuICAgIGFkbWluLnNpdGUucmVnaXN0ZXIobW9kZWwsIG1vZGVsc2l0ZSlcbi4uLlxuUGF0aDogbWFpbi9tb2RlbHMucHlcbiNjb2Rpbmc6dXRmLThcbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5mcm9tIGRhdGV0aW1lIGltcG9ydCBkYXRldGltZVxuXG5cblxuY2xhc3MgeW9uZ2h1KEJhc2VNb2RlbCk6XG4gICAgX19kb2NfXyA9IHUnJyd5b25naHUnJydcbiAgICBfX3RhYmxlbmFtZV9fID0gJ3lvbmdodSdcblxuICAgIF9fbG9naW5Vc2VyX189J3lvbmdodW1pbmcnXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHlvbmdodW1pbmc9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0n55So5oi35ZCNJyApXG4gICAgbWltYT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WvhueggScgKVxuICAgIHhpbmdtaW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5aeT5ZCNJyApXG4gICAgeGluZ2JpZT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+aAp+WIqycgKVxuICAgIHRvdXhpYW5nPW1vZGVscy5UZXh0RmllbGQgICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflpLTlg48nIClcbiAgICBuaWFubGluZz1tb2RlbHMuSW50ZWdlckZpZWxkICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflubTpvoQnIClcbiAgICB5b3V4aWFuZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+mCrueusScgKVxuICAgIHNob3VqaT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+aJi+acuicgKVxuICAgICcnJ1xuICAgIHlvbmdodW1pbmc9VkFSQ0hBUlxuICAgIG1pbWE9VkFSQ0hBUlxuICAgIHhpbmdtaW5nPVZBUkNIQVJcbiAgICB4aW5nYmllPVZBUkNIQVJcbiAgICB0b3V4aWFuZz1UZXh0XG4gICAgbmlhbmxpbmc9SW50ZWdlclxuICAgIHlvdXhpYW5nPVZBUkNIQVJcbiAgICBzaG91amk9VkFSQ0hBUlxuICAgICcnJ1xuICAgIGNsYXNzIE1ldGE6XG4gICAgICAgIGRiX3RhYmxlID0gJ3lvbmdodSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICfnlKjmiLcnXG5jbGFzcyBub25naHUoQmFzZU1vZGVsKTpcbiAgICBfX2RvY19fID0gdScnJ25vbmdodScnJ1xuICAgIF9fdGFibGVuYW1lX18gPSAnbm9uZ2h1J1xuXG4gICAgX19sb2dpblVzZXJfXz0nemhhbmdoYW8nXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHpoYW5naGFvPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J+i0puWPtycgKVxuICAgIG1pbWE9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflr4bnoIEnIClcbiAgICBub25naHV4aW5nbWluZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WGnOaIt+Wnk+WQjScgKVxuICAgIHRvdXhpYW5nPW1vZGVscy5UZXh0RmllbGQgICAoIG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflpLTlg48nIClcbiAgICB4aW5nYmllPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5oCn5YirJyApXG4uLi5cbiAgICBfX2ZvcmVFbmRMaXN0X189J+WQpicj6KGo5bGe5oCnW2ZvcmVFbmRMaXN0XeWJjeWPsGxpc3Q65ZKM5ZCO5Y+w6buY6K6k55qEbGlzdOWIl+ihqOmhteebuOS8vCzlj6rmmK/mkYblnKjliY3lj7As5ZCmOuaMh+ayoeacieatpOmhtSzmmK866KGo56S65pyJ5q2k6aG1KOS4jemcgOimgeeZu+mZhuWNs+WPr+afpeeciyks5YmN6KaB55m7OuihqOekuuacieatpOmhteS4lOmcgOimgeeZu+mZhuWQjuaJjeiDveafpeeci1xuICAgIF9faXNBZG1pbl9fPSflkKYnI+ihqOWxnuaAp2lzQWRtaW494oCd5piv4oCdLOWIt+WHuuadpeeahOeUqOaIt+ihqOS5n+aYr+euoeeQhuWRmO+8jOWNs3BhZ2XlkoxsaXN05Y+v5Lul5p+l55yL5omA5pyJ5Lq655qE6ICD6K+V6K6w5b2VKOWQjOaXtuW6lOeUqOS6juWFtuS7luihqClcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIHpoaWxpYW5nZGVuZ2ppPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLHVuaXF1ZT1UcnVlLCB2ZXJib3NlX25hbWU9J+i0qOmHj+etiee6pycgKVxuICAgICcnJ1xuICAgIHpoaWxpYW5nZGVuZ2ppPVZBUkNIQVJcbiAgICAnJydcbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBkYl90YWJsZSA9ICd6aGlsaWFuZ2RlbmdqaSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICfotKjph4/nrYnnuqcnXG4uLi5cblBhdGg6IGRqMi92aWV3cy5weVxuIyBjb2Rpbmc6dXRmLThcbmltcG9ydCBvcyxzeXNcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZSwgSHR0cFJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5cbmRlZiBpbmRleChyZXF1ZXN0KTpcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJHRVRcIiwgXCJQT1NUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IDIwMCwgXCJtc2dcIjogXCJzdWNjZXNzXCIsIFwiZGF0YVwiOiBbXX1cbiAgICAgICAgcHJpbnQoXCI9PT09PT09PT09PT09PT09PT5pbmRleFwiKVxuICAgICAgICAjIGFsbE1vZGVscyA9IGFwcHMuZ2V0X2FwcF9jb25maWcoJ21haW4nKS5nZXRfbW9kZWxzKClcbiAgICAgICAgIyBmb3IgbSBpbiBhbGxNb2RlbHM6XG4gICAgICAgICMgICAgIHByaW50KG0uX190YWJsZW5hbWVfXylcbiAgICAgICAgIyAgICAgcHJpbnQoZGlyKG0pKVxuICAgICAgICAjICAgICAjIGZvciBjb2wgaW4gbS5fbWV0YS5maWVsZHM6XG4gICAgICAgICMgICAgICMgICAgIHByaW50KFwiY29sIG5hbWU9PT09PT09PT09PT0+XCIsY29sLm5hbWUpXG4gICAgICAgICMgICAgICMgICAgIHByaW50KFwiY29sIHR5cGU9PT09PT09PT09PT0+XCIsY29sLmdldF9pbnRlcm5hbF90eXBlKCkpXG4gICAgICAgICMgcHJpbnQoYWxsTW9kZWxzKVxuXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiB0ZXN0KHJlcXVlc3QsIHAxKTpcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJHRVRcIiwgXCJQT1NUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IDIwMCwgXCJtc2dcIjogXCJzdWNjZXNzXCIsIFwiZGF0YVwiOiBbXX1cbiAgICAgICAgcHJpbnQoXCI9PT09PT09PT09PT09PT09PT5pbmRleCAgXCIsIHAxKVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcbi4uLlxuUGF0aDogbWFpbi9jb25maWdfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIGNvbmZpZyhCYXNlTW9kZWwpOlxuICAgICMgaWQ9bW9kZWxzLkJpZ0ludGVnZXJGaWVsZCh2ZXJib3NlX25hbWU9XCLoh6rlop5pZFwiKVxuICAgIG5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfplK7lkI0nKVxuICAgIHZhbHVlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6ZSu5YC8JylcbiAgICB1cmwgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfplK7lgLwnKVxuXG4gICAgX190YWJsZW5hbWVfXyA9ICdjb25maWcnXG5cbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBkYl90YWJsZSA9ICdjb25maWcnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+mFjee9ruihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYubmFtZVxuLi4uXG5QYXRoOiBtYWluL05vbmdodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBub25naHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9hcHBzLnB5XG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBBcHBDb25maWdcblxuXG5jbGFzcyBNYWluQ29uZmlnKEFwcENvbmZpZyk6XG4gICAgbmFtZSA9ICdtYWluJ1xuICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSByJ+WIt+ihqOaooeWdlydcbi4uLlxuUGF0aDogbWFpbi9DYW5nY2h1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IGNhbmdjaHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogZGoyL3NldHRpbmdzLnB5XG5cIlwiXCJcbkRqYW5nbyBzZXR0aW5ncyBmb3IgZGoyIHByb2plY3QuXG5cbkdlbmVyYXRlZCBieSAnZGphbmdvLWFkbWluIHN0YXJ0cHJvamVjdCcgdXNpbmcgRGphbmdvIDIuMC5cblxuRm9yIG1vcmUgaW5mb3JtYXRpb24gb24gdGhpcyBmaWxlLCBzZWVcbmh0dHBzOi8vZG9jcy5kamFuZ29wcm9qZWN0LmNvbS9lbi8yLjAvdG9waWNzL3NldHRpbmdzL1xuXG5Gb3IgdGhlIGZ1bGwgbGlzdCBvZiBzZXR0aW5ncyBhbmQgdGhlaXIgdmFsdWVzLCBzZWVcbmh0dHBzOi8vZG9jcy5kamFuZ29wcm9qZWN0LmNvbS9lbi8yLjAvcmVmL3NldHRpbmdzL1xuXCJcIlwiXG5pbXBvcnQgb3NcbmZyb20gdXRpbC5jb25maWdyZWFkIGltcG9ydCBjb25maWdfcmVhZFxuXG4jIEJ1aWxkIHBhdGhzIGluc2lkZSB0aGUgcHJvamVjdCBsaWtlIHRoaXM6IG9zLnBhdGguam9pbihCQVNFX0RJUiwgLi4uKVxuQkFTRV9ESVIgPSBvcy5wYXRoLmRpcm5hbWUob3MucGF0aC5kaXJuYW1lKG9zLnBhdGguYWJzcGF0aChfX2ZpbGVfXykpKVxuXG4jIFF1aWNrLXN0YXJ0IGRldmVsb3BtZW50IHNldHRpbmdzIC0gdW5zdWl0YWJsZSBmb3IgcHJvZHVjdGlvblxuIyBTZWUgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9ob3d0by9kZXBsb3ltZW50L2NoZWNrbGlzdC9cblxuIyBTRUNVUklUWSBXQVJOSU5HOiBrZWVwIHRoZSBzZWNyZXQga2V5IHVzZWQgaW4gcHJvZHVjdGlvbiBzZWNyZXQhXG5TRUNSRVRfS0VZID0gJ3c1eW4jMGduMnR0N3B2dSVodnd0MCFsdD0hJDYrZXFwNCVtOCl1M3UjZ2tubUBqbSlrJ1xuLi4uXG5DT1JTX0FMTE9XX0NSRURFTlRJQUxTID0gVHJ1ZVxuQ09SU19PUklHSU5fQUxMT1dfQUxMID0gVHJ1ZVxuQ09SU19BTExPV19IRUFERVJTID0gKCcqJylcblxuU0VTU0lPTl9FTkdJTkUgPSBcImRqYW5nby5jb250cmliLnNlc3Npb25zLmJhY2tlbmRzLmNhY2hlXCJcblNFU1NJT05fQ09PS0lFX05BTUUgID0gXCJzZXNzaW9uaWRcIlxuU0VTU0lPTl9DT09LSUVfUEFUSCAgPSBcIi9cIlxuU0VTU0lPTl9DT09LSUVfRE9NQUlOID0gTm9uZVxuU0VTU0lPTl9DT09LSUVfU0VDVVJFID0gRmFsc2VcblNFU1NJT05fQ09PS0lFX0hUVFBPTkxZID0gVHJ1ZVxuU0VTU0lPTl9DT09LSUVfQUdFID0gMTIwOTYwMFxuU0VTU0lPTl9FWFBJUkVfQVRfQlJPV1NFUl9DTE9TRSA9IEZhbHNlXG5TRVNTSU9OX1NBVkVfRVZFUllfUkVRVUVTVCA9IEZhbHNlXG4uLi5cblxuV1NHSV9BUFBMSUNBVElPTiA9ICdkajIud3NnaS5hcHBsaWNhdGlvbidcblxuRU1BSUxfQkFDS0VORCA9ICdkamFuZ28uY29yZS5tYWlsLmJhY2tlbmRzLnNtdHAuRW1haWxCYWNrZW5kJ1xuRU1BSUxfVVNFX1RMUyA9IEZhbHNlXG5FTUFJTF9VU0VfU1NMID0gVHJ1ZVxuRU1BSUxfSE9TVCA9ICdzbXRwLnFxLmNvbSdcbkVNQUlMX1BPUlQgPSA0NjVcbkVNQUlMX0hPU1RfVVNFUiA9ICd5Y2x3OUBxcS5jb20nXG5FTUFJTF9IT1NUX1BBU1NXT1JEID0gJ21oYnJrdWF5dmtrZ2JpamQnXG5cbiMgRGF0YWJhc2VcbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9yZWYvc2V0dGluZ3MvI2RhdGFiYXNlc1xuXG4jIERBVEFCQVNFUyA9IHtcbiMgICAgICdkZWZhdWx0Jzoge1xuIyAgICAgICAgICdFTkdJTkUnOiAnZGphbmdvLmRiLmJhY2tlbmRzLnNxbGl0ZTMnLFxuIyAgICAgICAgICdOQU1FJzogb3MucGF0aC5qb2luKEJBU0VfRElSLCAnZGIuc3FsaXRlMycpLFxuIyAgICAgfVxuIyB9XG5cbmRidHlwZSwgaG9zdCwgcG9ydCwgdXNlciwgcGFzc3dkLCBkYk5hbWUsIGNoYXJzZXQgPSBjb25maWdfcmVhZChcImNvbmZpZy5pbmlcIilcbmRiTmFtZT1kYk5hbWUucmVwbGFjZShcIiBcIixcIlwiKS5zdHJpcCgpXG5wcmludChkYnR5cGUsIGhvc3QsIHBvcnQsIHVzZXIsIHBhc3N3ZCwgZGJOYW1lLCBjaGFyc2V0KVxuXG4jIGlmIGRidHlwZSA9PSAnbXlzcWwnOlxuREFUQUJBU0VTID0ge1xuICAgICdkZWZhdWx0Jzoge1xuICAgICAgICAgICAjICdFTkdJTkUnOiAnZGphbmdvLmRiLmJhY2tlbmRzLnNxbGl0ZTMnLFxuICAgICAgICAgICAjICdOQU1FJzogb3MucGF0aC5qb2luKEJBU0VfRElSLCAnZGIuc3FsaXRlMycpLFxuICAgICAgICAnRU5HSU5FJzogJ2RqYW5nby5kYi5iYWNrZW5kcy5teXNxbCcsXG4gICAgICAgICdPUFRJT05TJzoge1xuICAgICAgICAgICAgIyAnc3FsX21vZGUnOiAndHJhZGl0aW9uYWwnLFxuICAgICAgICAgICAgIydpbml0X2NvbW1hbmQnOiBcIlNFVCBzcWxfbW9kZT0ndHJhZGl0aW9uYWwnXCIsICAjIFNUUklDVF9UUkFOU19UQUJMRVNcbiAgICAgICAgICAgICdjaGFyc2V0JzogJ3V0ZjhtYjQnLFxuICAgICAgICAgICAgIyAndXNlX3VuaWNvZGUnOiBUcnVlLFxuICAgICAgICB9LFxuICAgICAgICAnTkFNRSc6ICdHcmFkdWF0ZVByb2plY3QnLFxuICAgICAgICAnVVNFUic6ICdHcmFkdWF0ZVByb2plY3QnLFxuICAgICAgICAnUEFTU1dPUkQnOiAnR3JhZHVhdGVQcm9qZWN0JyxcbiAgICAgICAgJ0hPU1QnOiAnMTcyLjE2LjAuMTEwJyxcbiAgICAgICAgJ1BPUlQnOiAzMzA2LFxuICAgICAgICAnY2hhcnNldCc6ICd1dGY4bWI0JyxcbiAgICAgICAgJ1RFU1QnOiB7XG4gICAgICAgICAgICAnQ0hBUlNFVCc6ICd1dGY4bWI0JyxcbiAgICAgICAgICAgICdDT0xMQVRJT04nOiAndXRmOG1iNF9nZW5lcmFsX2NpJyxcbiAgICAgICAgfSxcbiAgICAgICAgJ0NPTk5fTUFYX0FHRSc6NjBcbiAgICB9LFxufVxuLi4uXG5cbiMgSW50ZXJuYXRpb25hbGl6YXRpb25cbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC90b3BpY3MvaTE4bi9cblxuTEFOR1VBR0VfQ09ERSA9ICd6aC1IYW5zJ1xuXG4jIFRJTUVfWk9ORSA9ICdVVEMnXG5USU1FX1pPTkUgPSAnQXNpYS9TaGFuZ2hhaSdcblxuVVNFX0kxOE4gPSBUcnVlXG5cblVTRV9MMTBOID0gVHJ1ZVxuXG4jIFVTRV9UWiA9IFRydWVcblVTRV9UWiA9IEZhbHNlXG5cbiMgU3RhdGljIGZpbGVzIChDU1MsIEphdmFTY3JpcHQsIEltYWdlcylcbiMgaHR0cHM6Ly9kb2NzLmRqYW5nb3Byb2plY3QuY29tL2VuLzIuMC9ob3d0by9zdGF0aWMtZmlsZXMvXG5cblxuXG5TVEFUSUNfVVJMID0gJy9hc3NldHMvJ1xuU1RBVElDRklMRVNfRElSUyA9W1xub3MucGF0aC5qb2luKEJBU0VfRElSLCBcInRlbXBsYXRlcy9mcm9udC9hc3NldHNcIiksXG5dXG5cbiMgbWVkaWFcbk1FRElBX1VSTCA9IFwiL21lZGlhL1wiICAjIOiHquWumuS5iVxuTUVESUFfUk9PVCA9IG9zLnBhdGguam9pbihCQVNFX0RJUiwgJ21lZGlhJykgICMg6Ieq5a6a5LmJXG5pZiBvcy5wYXRoLmlzZGlyKE1FRElBX1JPT1QpID09IEZhbHNlOlxuICAgIG9zLm1rZGlyKE1FRElBX1JPT1QpXG5cblxuLi4uXG5QYXRoOiBtYWluL21vZGVsLnB5XG4jIGNvZGluZzp1dGYtOFxuXG5pbXBvcnQgY29weSwgcmUsIHRpbWVcbmltcG9ydCBsb2dnaW5nIGFzIGxvZ1xuaW1wb3J0IGRhdGV0aW1lXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgbW9kZWxzXG5mcm9tIGRqYW5nby5mb3Jtcy5tb2RlbHMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmZyb20gZGphbmdvLmNvcmUucGFnaW5hdG9yIGltcG9ydCBQYWdpbmF0b3JcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUHJvdGVjdGVkRXJyb3JcbmZyb20gdGhyZWFkbG9jYWxzLnRocmVhZGxvY2FscyBpbXBvcnQgZ2V0X2N1cnJlbnRfcmVxdWVzdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBTdW0sIE1heCwgTWluLCBBdmcsQ291bnRcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuXG4jIG1vZGVs5Z+656GA57G7XG5jbGFzcyBCYXNlTW9kZWwobW9kZWxzLk1vZGVsKTpcbiAgICBjbGFzcyBNZXRhOlxuICAgICAgICBhYnN0cmFjdCA9IFRydWVcblxuXG4gICAgZGVmIF9fUmV0cmlldmUoc2VsZiwgbW9kZWwpOlxuXG4gICAgICAgIGRhdGFzID0gbW9kZWwub2JqZWN0cy5hbGwoKVxuXG4gICAgICAgIHJldHVybiBzZWxmLnRvX2xpc3QoZGF0YXMsIGRhdGFzLG1vZGVsKVxuXG4gICAgZGVmIHJldHJpZXZlKHNlbGYsIG1vZGVsKTpcbiAgICAgICAgZGF0YXM9c2VsZi5fX1JldHJpZXZlKG1vZGVsLCBtb2RlbClcbiAgICAgICAgZm9yIGkgaW4gZGF0YXM6XG4gICAgICAgICAgICBhZGR0aW1lPWkuZ2V0KFwiYWRkdGltZVwiKVxuICAgICAgICAgICAgaWYgYWRkdGltZTpcbiAgICAgICAgICAgICAgICBhZGR0aW1lPXN0cihhZGR0aW1lKVs6MTldLnJlcGxhY2UoXCJUXCIsXCIgXCIpXG4gICAgICAgICAgICAgICAgaVtcImFkZHRpbWVcIl09YWRkdGltZVxuICAgICAgICByZXR1cm4gZGF0YXNcbi4uLlxuXG4gICAgICAgIGZpbGVkX2xpc3Q9W11cbiAgICAgICAgZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuICAgICAgICBtb2RlbG9iaiA9IGFwcHMuZ2V0X21vZGVsKCdtYWluJywgbW9kZWwuX190YWJsZW5hbWVfXylcbiAgICAgICAgZm9yIGZpZWxkIGluIG1vZGVsb2JqLl9tZXRhLmZpZWxkczpcbiAgICAgICAgICAgIGlmICAnRGF0ZVRpbWVGaWVsZCcgaW4gdHlwZShmaWVsZCkuX19uYW1lX18gOlxuICAgICAgICAgICAgICAgIGZpbGVkX2xpc3QuYXBwZW5kKGZpZWxkLm5hbWUpXG5cbiAgICAgICAgZm9yIGluZGV4LGkgaW4gZW51bWVyYXRlKG5ld0RhdGEpOlxuICAgICAgICAgICAgZm9yIGssdiBpbiBpLml0ZW1zKCk6XG4gICAgICAgICAgICAgICAgaWYgIGsgaW4gZmlsZWRfbGlzdCA6XG4gICAgICAgICAgICAgICAgICAgIG5ld0RhdGFbaW5kZXhdW2tdPXN0cih2KVs6MTldXG5cbiAgICAgICAgcmV0dXJuIG5ld0RhdGFhLCBwYWdlLCBwYWdlcywgdG90YWwsIGxpbWl0XG5cbiAgICBkZWYgcGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0PXt9LCBxPVEoKSk6XG4gICAgICAgIHJldHVybiBzZWxmLl9fUGFnZShzZWxmLCBtb2RlbCwgcGFyYW1zLCByZXF1ZXN0LCBxKVxuXG4gICAgZGVmIF9fR2V0QnlDb2x1bW4oc2VsZiwgbW9kZWwsIGNvbHVtbk5hbWUsIG5ld19wYXJhbXMpOlxuICAgICAgICAjIGRhdGExPSBtb2RlbC5xdWVyeS5vcHRpb25zKGxvYWRfb25seShjb2x1bW4pKS5hbGwoKVxuICAgICAgICBkYXRhcyA9IG1vZGVsLm9iamVjdHMudmFsdWVzKGNvbHVtbk5hbWUpLmZpbHRlcigqKm5ld19wYXJhbXMpLmFsbCgpXG4gICAgICAgIGRhdGFfc2V0ID0gc2V0KClcbiAgICAgICAgZm9yIGkgaW4gZGF0YXM6XG4gICAgICAgICAgICBkYXRhX3NldC5hZGQoaS5nZXQoY29sdW1uTmFtZSkpXG4gICAgICAgIHJldHVybiBsaXN0KGRhdGFfc2V0KVxuLi4uXG5cbiAgICAgICAgY29sdW1uX2xpc3QgPSBbXVxuICAgICAgICBmb3IgY29sIGluIG1vZGVsLl9tZXRhLmZpZWxkczpcbiAgICAgICAgICAgIGNvbHVtbl9saXN0LmFwcGVuZChjb2wubmFtZSlcbiAgICAgICAgcGFyYW1zcz17fVxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcbiAgICAgICAgICAgIGlmIGsgaW4gY29sdW1uX2xpc3Q6XG4gICAgICAgICAgICAgICAgcGFyYW1zc1trXSA9IHZcbiAgICAgICAgcGFyYW1zc1tcImFkZHRpbWVcIl0gPSBkYXRldGltZS5kYXRldGltZS5ub3coKVxuICAgICAgICBtID0gbW9kZWwoKipwYXJhbXNzKVxuICAgICAgICB0cnk6XG4gICAgICAgICAgICByZXQgPSBtLnNhdmUoKVxuICAgICAgICAgICAgcmV0dXJuIG0uaWRcbiAgICAgICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOlxuICAgICAgICAgICAgcmV0dXJuIFwie306e31cIi5mb3JtYXQoRXhjZXB0aW9uLCBlKVxuXG4gICAgZGVmIGNyZWF0ZWJ5cmVxKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuICAgICAgICAnJydcbiAgICAgICAg5qC55o2u6K+35rGC5Y+C5pWw5Yib5bu65a+55bqU5qih5Z6L6K6w5b2VXG4gICAgICAgIDpwYXJhbSBtb2RlbDpcbiAgICAgICAgOnBhcmFtIHBhcmFtczpcbiAgICAgICAgOnJldHVybjpcbiAgICAgICAgJycnXG4gICAgICAgIHJldHVybiBzZWxmLl9fQ3JlYXRlQnlSZXEobW9kZWwsIG1vZGVsLCBwYXJhbXMpXG5cbiAgICBkZWYgX19HZXRCeUlkKHNlbGYsIG1vZGVsLCBpZCk6XG4gICAgICAgICcnJ1xuICAgICAgICDmoLnmja5pZOiOt+WPluaVsOaNruWFrOWFseaWueazlVxuICAgICAgICA6cGFyYW0gaWQ6XG4gICAgICAgIDpyZXR1cm46XG4gICAgICAgICcnJ1xuICAgICAgICBkYXRhID0gbW9kZWwub2JqZWN0cy5maWx0ZXIoaWQ9aWQpLmFsbCgpXG5cbiAgICAgICAgcmV0dXJuIHNlbGYudG9fbGlzdChtb2RlbCwgZGF0YSxtb2RlbClcblxuICAgIGRlZiBnZXRieWlkKHNlbGYsIG1vZGVsLCBpZCk6XG4gICAgICAgICcnJ1xuICAgICAgICDmoLnmja5pZOiOt+WPluaVsOaNrlxuICAgICAgICA6cGFyYW0gbW9kZWw6XG4gICAgICAgIDpwYXJhbSBpZDpcbiAgICAgICAgOnJldHVybjpcbiAgICAgICAgJycnXG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlJZChtb2RlbCwgbW9kZWwsIGlkKVxuXG4gICAgZGVmIF9fR2V0QnlQYXJhbXMoc2VsZiwgbW9kZWwsIHBhcmFtcyk6XG5cbiAgICAgICAgdHJ5OlxuICAgICAgICAgICAgX19sb2dpblVzZXJfXyA9IG1vZGVsLl9fbG9naW5Vc2VyX19cbiAgICAgICAgZXhjZXB0OlxuICAgICAgICAgICAgX19sb2dpblVzZXJfXyA9IE5vbmVcblxuICAgICAgICBpZiBfX2xvZ2luVXNlcl9fICE9IE5vbmUgYW5kIF9fbG9naW5Vc2VyX18hPSd1c2VybmFtZSc6XG4gICAgICAgICAgICBpZiBwYXJhbXMuZ2V0KCd1c2VybmFtZScpOlxuICAgICAgICAgICAgICAgIHBhcmFtc1ttb2RlbC5fX2xvZ2luVXNlcl9fXSA9IGNvcHkuZGVlcGNvcHkocGFyYW1zLmdldCgndXNlcm5hbWUnKSlcbiAgICAgICAgICAgICAgICBkZWwgcGFyYW1zWyd1c2VybmFtZSddXG4gICAgICAgIGlmIG1vZGVsLl9fdGFibGVuYW1lX18gIT0gJ3VzZXJzJzpcbiAgICAgICAgICAgIGlmIHBhcmFtcy5nZXQoJ3Bhc3N3b3JkJyk6XG4gICAgICAgICAgICAgICAgcGFyYW1zWydtaW1hJ10gPSBjb3B5LmRlZXBjb3B5KHBhcmFtcy5nZXQoJ3Bhc3N3b3JkJykpXG4gICAgICAgICAgICAgICAgIyBkZWwgcGFyYW1zWydwYXNzd29yZCddXG5cbiAgICAgICAgIyDliY3nq6/kvKDkuobml6DnlKjlj4LmlbDlkozkvKDplJnlj4LmlbDlkI3vvIzlnKjov5nph4zkv67mlLlcbiAgICAgICAgcGFyYW1zcyA9IHt9XG4gICAgICAgIGNvbHVtbkxpc3QgPSBzZWxmLmdldGFsbGNvbHVtbihtb2RlbCwgbW9kZWwpXG4gICAgICAgIGZvciBrLCB2IGluIHBhcmFtcy5pdGVtcygpOlxuICAgICAgICAgICAgaWYgayBpbiBjb2x1bW5MaXN0OlxuICAgICAgICAgICAgICAgIHBhcmFtc3Nba10gPSB2XG5cbiAgICAgICAgZGF0YXNfID0gbW9kZWwub2JqZWN0cy5maWx0ZXIoKipwYXJhbXNzKS5hbGwoKVxuXG4gICAgICAgIHJldHVybiBzZWxmLnRvX2xpc3QoZGF0YXNfLCBkYXRhc18sbW9kZWwpXG5cbiAgICBkZWYgZ2V0YnlwYXJhbXMoc2VsZiwgbW9kZWwsIHBhcmFtcyk6XG4gICAgICAgIHJldHVybiBzZWxmLl9fR2V0QnlQYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMpXG4uLi5cblBhdGg6IG1haW4vdXNlcnNfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIHVzZXJzKEJhc2VNb2RlbCk6XG4gICAgIyBpZD1tb2RlbHMuQmlnSW50ZWdlckZpZWxkKHZlcmJvc2VfbmFtZT1cIuiHquWinmlkXCIpXG4gICAgdXNlcm5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfotKblj7cnKVxuICAgIHBhc3N3b3JkID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn5a+G56CBJylcbiAgICByb2xlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6KeS6ImyJylcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIF9faXNBZG1pbl9fID0gJ+aYrydcbiAgICBpbWFnZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J+WktOWDjycpXG5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ3VzZXJzJ1xuXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAndXNlcnMnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+euoeeQhuWRmOihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYudXNlcm5hbWVcbi4uLlxuIiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiOTJiNzM3ZTgtOTdjMC00ZWFiLTg3YWQtZDBmM2IyYmZjZDU1O3Rvb2x1X3ZydHhfMDExYUprdUh5WmJlb216czVQWjNlaXZrIjp7InJlcXVlc3RJZCI6IjkyYjczN2U4LTk3YzAtNGVhYi04N2FkLWQwZjNiMmJmY2Q1NSIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDExYUprdUh5WmJlb216czVQWjNlaXZrIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiVGhlIGZvbGxvd2luZyBjb2RlIHNlY3Rpb25zIHdlcmUgcmV0cmlldmVkOlxuUGF0aDogbWFpbi91c2Vyc192LnB5XG4jIGNvZGluZzp1dGYtOFxuXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcblxuZnJvbSAudXNlcnNfbW9kZWwgaW1wb3J0IHVzZXJzXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGhvc3QscG9ydCx1c2VyLHBhc3N3ZCxkYk5hbWVcblxuZGVmIHVzZXJzX2xvZ2luKHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHsnY29kZSc6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGV9XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIHJlcV9kaWN0LmdldCgncm9sZScpIT1Ob25lOlxuICAgICAgICAgICAgZGVsIHJlcV9kaWN0Wydyb2xlJ11cbiAgICAgICAgXG4gICAgICAgIGRhdGFzID0gdXNlcnMuZ2V0YnlwYXJhbXModXNlcnMsIHVzZXJzLCByZXFfZGljdClcbiAgICAgICAgaWYgbm90IGRhdGFzOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBwYXNzd29yZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gbWVzLnBhc3N3b3JkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG4gICAgICAgIHJlcV9kaWN0WydpZCddID0gZGF0YXNbMF0uZ2V0KCdpZCcpXG4gICAgICAgIHJldHVybiBBdXRoLmF1dGhlbnRpY2F0ZShBdXRoLCB1c2VycywgcmVxX2RpY3QpXG5cblxuZGVmIHVzZXJzX3JlZ2lzdGVyKHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHsnY29kZSc6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGV9XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG5cbiAgICAgICAgaWRPckVyciA9IHVzZXJzLmNyZWF0ZWJ5cmVxKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXG4gICAgICAgIGlmIGlkT3JFcnIgaXMgRXhjZXB0aW9uOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBpZE9yRXJyXG4gICAgICAgIGVsc2U6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGlkT3JFcnJcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3Nlc3Npb24ocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSxcIm1zZ1wiOm1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIHJlcV9kaWN0ID0ge1wiaWRcIjogcmVxdWVzdC5zZXNzaW9uLmdldCgncGFyYW1zJykuZ2V0KFwiaWRcIil9XG4gICAgICAgIG1zZ1snZGF0YSddID0gdXNlcnMuZ2V0YnlwYXJhbXModXNlcnMsIHVzZXJzLCByZXFfZGljdClbMF1cblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfbG9nb3V0KHJlcXVlc3QpOlxuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcbiAgICAgICAgICAgIFwibXNnXCI6IFwi6YCA5Ye65oiQ5YqfXCIsXG4gICAgICAgICAgICBcImNvZGVcIjogMFxuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3BhZ2UocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLFxuICAgICAgICAgICAgICAgXCJkYXRhXCI6IHtcImN1cnJQYWdlXCI6IDEsIFwidG90YWxQYWdlXCI6IDEsIFwidG90YWxcIjogMSwgXCJwYWdlU2l6ZVwiOiAxMCwgXCJsaXN0XCI6IFtdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgdGFibGVuYW1lID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInRhYmxlbmFtZVwiKVxuICAgICAgICB0cnk6XG4gICAgICAgICAgICBfX2hhc01lc3NhZ2VfXyA9IHVzZXJzLl9faGFzTWVzc2FnZV9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9faGFzTWVzc2FnZV9fID0gTm9uZVxuICAgICAgICBpZiBfX2hhc01lc3NhZ2VfXyBhbmQgX19oYXNNZXNzYWdlX18gIT0gXCLlkKZcIjpcblxuICAgICAgICAgICAgaWYgdGFibGVuYW1lICE9IFwidXNlcnNcIjpcbiAgICAgICAgICAgICAgICByZXFfZGljdFtcInVzZXJpZFwiXSA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJwYXJhbXNcIikuZ2V0KFwiaWRcIilcbiAgICAgICAgaWYgdGFibGVuYW1lID09IFwidXNlcnNcIjpcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydsaXN0J10sIG1zZ1snZGF0YSddWydjdXJyUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWxQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbCddLCBcXFxuICAgICAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSB1c2Vycy5wYWdlKHVzZXJzLCB1c2VycywgcmVxX2RpY3QpXG4gICAgICAgIGVsc2U6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXVsnbGlzdCddLCBtc2dbJ2RhdGEnXVsnY3VyclBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWwnXSwgXFxcbiAgICAgICAgICAgIG1zZ1snZGF0YSddWydwYWdlU2l6ZSddID0gW10sMSwwLDAsMTBcblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfaW5mbyhyZXF1ZXN0LCBpZF8pOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIGRhdGEgPSB1c2Vycy5nZXRieWlkKHVzZXJzLCB1c2VycywgaW50KGlkXykpXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGRhdGFbMF1cbiAgICAgICAgIyDmtY/op4jngrnlh7vmrKHmlbBcbiAgICAgICAgdHJ5OlxuICAgICAgICAgICAgX19icm93c2VDbGlja19fID0gdXNlcnMuX19icm93c2VDbGlja19fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fYnJvd3NlQ2xpY2tfXyA9IE5vbmVcblxuICAgICAgICBpZiBfX2Jyb3dzZUNsaWNrX18gYW5kIFwiY2xpY2tudW1cIiBpbiB1c2Vycy5nZXRhbGxjb2x1bW4odXNlcnMsIHVzZXJzKTpcbiAgICAgICAgICAgIGNsaWNrX2RpY3QgPSB7XCJpZFwiOiBpbnQoaWRfKSwgXCJjbGlja251bVwiOiBzdHIoaW50KGRhdGFbMF0uZ2V0KFwiY2xpY2tudW1cIiwgMCkpICsgMSl9XG4gICAgICAgICAgICByZXQgPSB1c2Vycy51cGRhdGVieXBhcmFtcyh1c2VycywgdXNlcnMsIGNsaWNrX2RpY3QpXG4gICAgICAgICAgICBpZiByZXQgIT0gTm9uZTpcbiAgICAgICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxuICAgICAgICAgICAgICAgIG1zZ1snbXNnJ10gPSByZXRcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIHVzZXJzX3NhdmUocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgaWYgdXNlcnMub2JqZWN0cy5maWx0ZXIodXNlcm5hbWUgPSByZXFfZGljdFsndXNlcm5hbWUnXSkuY291bnQoKT4wOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBcIui0puaIt+W3suWtmOWcqFwiXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZywgZW5jb2Rlcj1DdXN0b21Kc29uRW5jb2RlcilcbiAgICAgICAgcmVxX2RpY3RbJ3JvbGUnXSA9ICfnrqHnkIblkZgnXG4gICAgICAgIGlkT3JFcnIgPSB1c2Vycy5jcmVhdGVieXJlcSh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVxuICAgICAgICBpZiBpZE9yRXJyIGlzIEV4Y2VwdGlvbjpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gaWRPckVyclxuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgbXNnWydkYXRhJ10gPSBpZE9yRXJyXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiB1c2Vyc191cGRhdGUocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIHJlcV9kaWN0LmdldChcIm1pbWFcIikgYW5kIHJlcV9kaWN0LmdldChcInBhc3N3b3JkXCIpOlxuICAgICAgICAgICAgaWYgXCJtaW1hXCIgbm90IGluIHVzZXJzLmdldGFsbGNvbHVtbih1c2Vycyx1c2Vycyk6XG4gICAgICAgICAgICAgICAgZGVsIHJlcV9kaWN0W1wibWltYVwiXVxuICAgICAgICAgICAgaWYgXCJwYXNzd29yZFwiIG5vdCBpbiB1c2Vycy5nZXRhbGxjb2x1bW4odXNlcnMsdXNlcnMpOlxuICAgICAgICAgICAgICAgIGRlbCByZXFfZGljdFtcInBhc3N3b3JkXCJdXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIGRlbCByZXFfZGljdFtcImNsaWNrbnVtXCJdXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIHBhc3NcbiAgICAgICAgZXJyb3IgPSB1c2Vycy51cGRhdGVieXBhcmFtcyh1c2VycywgdXNlcnMsIHJlcV9kaWN0KVxuICAgICAgICBpZiBlcnJvciAhPSBOb25lOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSBjcnVkX2Vycm9yX2NvZGVcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSBlcnJvclxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgdXNlcnNfZGVsZXRlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuXG4gICAgICAgIGVycm9yID0gdXNlcnMuZGVsZXRlcyh1c2VycyxcbiAgICAgICAgICAgIHVzZXJzLFxuICAgICAgICAgICAgcmVxX2RpY3QuZ2V0KFwiaWRzXCIpXG4gICAgICAgIClcbiAgICAgICAgaWYgZXJyb3IgIT0gTm9uZTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gZXJyb3JcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG4uLi5cblBhdGg6IG1haW4vdXJscy5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IG9zXG5mcm9tIGRqYW5nby51cmxzIGltcG9ydCBwYXRoXG5mcm9tIG1haW4gaW1wb3J0IHVzZXJzX3YsIGNvbmZpZ192LCBzY2hlbWFfdlxuIyBmcm9tIGRqMi5zZXR0aW5ncyBpbXBvcnQgZGJOYW1lIGFzIHNjaGVtYU5hbWVcbiMgdXJs6KeE5YiZ5YiX6KGoXG51cmxwYXR0ZXJucyA9IFtcbiAgICBwYXRoKHIndXNlcnMvcmVnaXN0ZXInLCB1c2Vyc192LnVzZXJzX3JlZ2lzdGVyKSxcbiAgICBwYXRoKHIndXNlcnMvbG9naW4nLCB1c2Vyc192LnVzZXJzX2xvZ2luKSxcbiAgICBwYXRoKHIndXNlcnMvbG9nb3V0JywgdXNlcnNfdi51c2Vyc19sb2dvdXQpLFxuICAgIHBhdGgocid1c2Vycy9zZXNzaW9uJywgdXNlcnNfdi51c2Vyc19zZXNzaW9uKSxcbiAgICBwYXRoKHIndXNlcnMvcGFnZScsIHVzZXJzX3YudXNlcnNfcGFnZSksXG4gICAgcGF0aChyJ3VzZXJzL3NhdmUnLCB1c2Vyc192LnVzZXJzX3NhdmUpLFxuICAgIHBhdGgocid1c2Vycy9pbmZvLzxpZF8+JywgdXNlcnNfdi51c2Vyc19pbmZvKSxcbiAgICBwYXRoKHIndXNlcnMvdXBkYXRlJywgdXNlcnNfdi51c2Vyc191cGRhdGUpLFxuICAgIHBhdGgocid1c2Vycy9kZWxldGUnLCB1c2Vyc192LnVzZXJzX2RlbGV0ZSksXG5cbiAgICBwYXRoKHInY29uZmlnL3BhZ2UnLCBjb25maWdfdi5jb25maWdfcGFnZSksXG4gICAgcGF0aChyJ2NvbmZpZy9saXN0JywgY29uZmlnX3YuY29uZmlnX2xpc3QpLFxuICAgIHBhdGgocidjb25maWcvc2F2ZScsIGNvbmZpZ192LmNvbmZpZ19zYXZlKSxcbiAgICBwYXRoKHInY29uZmlnL2FkZCcsIGNvbmZpZ192LmNvbmZpZ19hZGQpLFxuICAgIHBhdGgocidjb25maWcvaW5mby88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19pbmZvKSxcbiAgICBwYXRoKHInY29uZmlnL2RldGFpbC88aWRfPicsIGNvbmZpZ192LmNvbmZpZ19kZXRhaWwpLFxuICAgIHBhdGgocidjb25maWcvdXBkYXRlJywgY29uZmlnX3YuY29uZmlnX3VwZGF0ZSksXG4gICAgcGF0aChyJ2NvbmZpZy9kZWxldGUnLCBjb25maWdfdi5jb25maWdfZGVsZXRlKSxcblxuXVxuIyBtYWluIGFwcOeahOi3r+W+hFxubWFpbkRpciA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJtYWluXCIpXG5cbiMg6L+H5ruk5paH5Lu255qE5YiX6KGoXG5leGNsdWRlTGlzdCA9IFtcbiAgICBcInNjaGVtYV92LnB5XCIsXG5cbiAgICBcInVzZXJzX3YucHlcIixcbiAgICBcImNvbmZpZ192LnB5XCIsXG5dXG5cbiMg5b6q546v5b2T5YmN55uu5b2V5LiL55qEcHnmlofku7Zcblxudmlld190dXBsZSA9IHNldCgpXG5mb3IgaSBpbiBvcy5saXN0ZGlyKG1haW5EaXIpOlxuICAgIGlmIGkgbm90IGluIGV4Y2x1ZGVMaXN0IGFuZCBpWy01Ol0gPT0gXCJfdi5weVwiOlxuICAgICAgICB2aWV3TmFtZSA9IGlbOi0zXSAgIyDljrvmjokucHnlkI7nvIDlrZfnrKbkuLJcbiAgICAgICAgdmlld190dXBsZS5hZGQoXCJmcm9tIG1haW4gaW1wb3J0IHt9XCIuZm9ybWF0KHZpZXdOYW1lKSlcbi4uLlxuUGF0aDogbWFpbi9jb25maWdfdi5weVxuIyBjb2Rpbmc6dXRmLThcblxuaW1wb3J0IGxvZ2dpbmdcblxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5cbmZyb20gLmNvbmZpZ19tb2RlbCBpbXBvcnQgY29uZmlnXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbCBpbXBvcnQgbWVzc2FnZSBhcyBtZXNcblxuXG5kZWYgY29uZmlnX3BhZ2UocmVxdWVzdCk6XG4gICAgJycnXG4gICAg6I635Y+W5Y+C5pWw5L+h5oGvXG4gICAgOnJldHVybjpcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsXG4gICAgICAgICAgICAgICBcImRhdGFcIjoge1wiY3VyclBhZ2VcIjogMSwgXCJ0b3RhbFBhZ2VcIjogMSwgXCJ0b3RhbFwiOiAxLCBcInBhZ2VTaXplXCI6IDEwLCBcImxpc3RcIjogW119fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoJ3JlcV9kaWN0JylcbiAgICAgICAgbXNnWydkYXRhJ11bJ2xpc3QnXSwgbXNnWydkYXRhJ11bJ2N1cnJQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbFBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsJ10sIFxcXG4gICAgICAgIG1zZ1snZGF0YSddWydwYWdlU2l6ZSddID0gY29uZmlnLnBhZ2UoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgY29uZmlnX2xpc3QocmVxdWVzdCk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLFxuICAgICAgICAgICAgICAgXCJkYXRhXCI6IHtcImN1cnJQYWdlXCI6IDEsIFwidG90YWxQYWdlXCI6IDEsIFwidG90YWxcIjogMSwgXCJwYWdlU2l6ZVwiOiAxMCwgXCJsaXN0XCI6IFtdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcblxuICAgICAgICBtc2dbJ2RhdGEnXVsnbGlzdCddLCBtc2dbJ2RhdGEnXVsnY3VyclBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsUGFnZSddLCBtc2dbJ2RhdGEnXVsndG90YWwnXSwgXFxcbiAgICAgICAgbXNnWydkYXRhJ11bJ3BhZ2VTaXplJ10gPSBjb25maWcucGFnZShjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ19pbmZvKHJlcXVlc3QsIGlkXyk6XG4gICAgJycnXG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgZGF0YSA9IGNvbmZpZy5nZXRieWlkKGNvbmZpZywgY29uZmlnLCBpbnQoaWRfKSlcbiAgICAgICAgaWYgbGVuKGRhdGEpID4gMDpcbiAgICAgICAgICAgIG1zZ1snZGF0YSddID0gZGF0YVswXVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuXG5kZWYgY29uZmlnX2RldGFpbChyZXF1ZXN0LCBpZF8pOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuXG4gICAgICAgIGRhdGEgPSBjb25maWcuZ2V0YnlpZChjb25maWcsIGNvbmZpZywgaW50KGlkXykpXG4gICAgICAgIGlmIGxlbihkYXRhKSA+IDA6XG4gICAgICAgICAgICBtc2dbJ2RhdGEnXSA9IGRhdGFbMF1cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ19zYXZlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWIm+W7uuWPguaVsOS/oeaBr1xuICAgIDpyZXR1cm46XG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXG4gICAgICAgIHBhcmFtMSA9IGNvbmZpZy5nZXRieXBhcmFtcyhjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG4gICAgICAgIGlmIHBhcmFtMTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gaWRfZXhpc3RfY29kZVxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IG1lcy5pZF9leGlzdF9jb2RlXG4gICAgICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuICAgICAgICBlcnJvciA9IGNvbmZpZy5jcmVhdGVieXJlcShjb25maWcsIGNvbmZpZywgcmVxX2RpY3QpXG4gICAgICAgIGxvZ2dpbmcud2FybmluZyhcInNhdmVfY29uZmlnLnJlcz09PT09PT09PT57fVwiLmZvcm1hdChlcnJvcikpXG4gICAgICAgIGlmIGVycm9yICE9IE5vbmU6XG4gICAgICAgICAgICBtc2dbJ2NvZGUnXSA9IGNydWRfZXJyb3JfY29kZVxuICAgICAgICAgICAgbXNnWydtc2cnXSA9IGVycm9yXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiBjb25maWdfYWRkKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgXCJkYXRhXCI6IHt9fVxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuXG4gICAgICAgIGVycm9yID0gY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCByZXFfZGljdClcbiAgICAgICAgaWYgZXJyb3IgIT0gTm9uZTpcbiAgICAgICAgICAgIG1zZ1snY29kZSddID0gY3J1ZF9lcnJvcl9jb2RlXG4gICAgICAgICAgICBtc2dbJ21zZyddID0gZXJyb3JcbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG5cblxuZGVmIGNvbmZpZ191cGRhdGUocmVxdWVzdCk6XG4gICAgJycnXG4gICAg5pu05paw5Y+C5pWw5L+h5oGvXG4gICAgOnJldHVybjpcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsIFwiZGF0YVwiOiB7fX1cblxuICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoJ3JlcV9kaWN0JylcblxuICAgICAgICBjb25maWcudXBkYXRlYnlwYXJhbXMoY29uZmlnLCBjb25maWcsIHJlcV9kaWN0KVxuXG4gICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UobXNnKVxuXG5cbmRlZiBjb25maWdfZGVsZXRlKHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWIoOmZpOWPguaVsOS/oeaBr1xuICAgIDpyZXR1cm46XG4gICAgJycnXG4gICAgaWYgcmVxdWVzdC5tZXRob2QgaW4gW1wiUE9TVFwiLCBcIkdFVFwiXTpcbiAgICAgICAgbXNnID0ge1wiY29kZVwiOiBub3JtYWxfY29kZSwgXCJtc2dcIjogbWVzLm5vcm1hbF9jb2RlLCBcImRhdGFcIjoge319XG5cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KCdyZXFfZGljdCcpXG4gICAgICAgIGNvbmZpZy5kZWxldGVzKGNvbmZpZyxcbiAgICAgICAgICAgIGNvbmZpZyxcbiAgICAgICAgICAgIHJlcV9kaWN0LmdldChcImlkc1wiKVxuICAgICAgICApXG5cbiAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShtc2cpXG4uLi5cblBhdGg6IG1haW4vdXNlcnNfbW9kZWwucHlcbiMgY29kaW5nOnV0Zi04XG5cbmZyb20gZGphbmdvLmRiIGltcG9ydCBtb2RlbHNcblxuZnJvbSAubW9kZWwgaW1wb3J0IEJhc2VNb2RlbFxuXG5cbmNsYXNzIHVzZXJzKEJhc2VNb2RlbCk6XG4gICAgIyBpZD1tb2RlbHMuQmlnSW50ZWdlckZpZWxkKHZlcmJvc2VfbmFtZT1cIuiHquWinmlkXCIpXG4gICAgdXNlcm5hbWUgPSBtb2RlbHMuQ2hhckZpZWxkKG1heF9sZW5ndGg9MTAwLCB2ZXJib3NlX25hbWU9dSfotKblj7cnKVxuICAgIHBhc3N3b3JkID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn5a+G56CBJylcbiAgICByb2xlID0gbW9kZWxzLkNoYXJGaWVsZChtYXhfbGVuZ3RoPTEwMCwgdmVyYm9zZV9uYW1lPXUn6KeS6ImyJylcbiAgICBhZGR0aW1lID0gbW9kZWxzLkRhdGVUaW1lRmllbGQoYXV0b19ub3dfYWRkPUZhbHNlLCB2ZXJib3NlX25hbWU9dSfliJvlu7rml7bpl7QnKVxuICAgIF9faXNBZG1pbl9fID0gJ+aYrydcbiAgICBpbWFnZSA9IG1vZGVscy5DaGFyRmllbGQobWF4X2xlbmd0aD0xMDAsIHZlcmJvc2VfbmFtZT11J+WktOWDjycpXG5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ3VzZXJzJ1xuXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAndXNlcnMnXG4gICAgICAgIHZlcmJvc2VfbmFtZSA9IHZlcmJvc2VfbmFtZV9wbHVyYWwgPSB1J+euoeeQhuWRmOihqCdcblxuICAgICMgZGVmIF9fc3RyX18oc2VsZik6XG4gICAgIyAgICAgcmV0dXJuIHNlbGYudXNlcm5hbWVcbi4uLlxuUGF0aDogbWFpbi9tb2RlbHMucHlcbi4uLlxuICAgIF9fZm9yZUVuZExpc3RfXz0n5ZCmJyPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgX19pc0FkbWluX189J+WQpicj6KGo5bGe5oCnaXNBZG1pbj3igJ3mmK/igJ0s5Yi35Ye65p2l55qE55So5oi36KGo5Lmf5piv566h55CG5ZGY77yM5Y2zcGFnZeWSjGxpc3Tlj6/ku6Xmn6XnnIvmiYDmnInkurrnmoTogIPor5XorrDlvZUo5ZCM5pe25bqU55So5LqO5YW25LuW6KGoKVxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J+WIm+W7uuaXtumXtCcpXG4gICAgeW9uZ2h1bWluZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSx1bmlxdWU9VHJ1ZSwgdmVyYm9zZV9uYW1lPSfnlKjmiLflkI0nIClcbiAgICBtaW1hPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5a+G56CBJyApXG4gICAgeGluZ21pbmc9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflp5PlkI0nIClcbiAgICB4aW5nYmllPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSwgbnVsbD1UcnVlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5oCn5YirJyApXG4gICAgdG91eGlhbmc9bW9kZWxzLlRleHRGaWVsZCAgICggIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WktOWDjycgKVxuICAgIG5pYW5saW5nPW1vZGVscy5JbnRlZ2VyRmllbGQgICggIG51bGw9VHJ1ZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+W5tOm+hCcgKVxuICAgIHlvdXhpYW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n6YKu566xJyApXG4gICAgc2hvdWppPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5omL5py6JyApXG4gICAgJycnXG4gICAgeW9uZ2h1bWluZz1WQVJDSEFSXG4gICAgbWltYT1WQVJDSEFSXG4gICAgeGluZ21pbmc9VkFSQ0hBUlxuICAgIHhpbmdiaWU9VkFSQ0hBUlxuICAgIHRvdXhpYW5nPVRleHRcbiAgICBuaWFubGluZz1JbnRlZ2VyXG4gICAgeW91eGlhbmc9VkFSQ0hBUlxuICAgIHNob3VqaT1WQVJDSEFSXG4gICAgJycnXG4gICAgY2xhc3MgTWV0YTpcbiAgICAgICAgZGJfdGFibGUgPSAneW9uZ2h1J1xuICAgICAgICB2ZXJib3NlX25hbWUgPSB2ZXJib3NlX25hbWVfcGx1cmFsID0gJ+eUqOaItydcbmNsYXNzIG5vbmdodShCYXNlTW9kZWwpOlxuICAgIF9fZG9jX18gPSB1Jycnbm9uZ2h1JycnXG4gICAgX190YWJsZW5hbWVfXyA9ICdub25naHUnXG5cbiAgICBfX2xvZ2luVXNlcl9fPSd6aGFuZ2hhbydcbi4uLlxuICAgIF9fZm9yZUVuZExpc3RfXz0n5ZCmJyPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgX19pc0FkbWluX189J+WQpicj6KGo5bGe5oCnaXNBZG1pbj3igJ3mmK/igJ0s5Yi35Ye65p2l55qE55So5oi36KGo5Lmf5piv566h55CG5ZGY77yM5Y2zcGFnZeWSjGxpc3Tlj6/ku6Xmn6XnnIvmiYDmnInkurrnmoTogIPor5XorrDlvZUo5ZCM5pe25bqU55So5LqO5YW25LuW6KGoKVxuICAgIGFkZHRpbWUgPSBtb2RlbHMuRGF0ZVRpbWVGaWVsZChhdXRvX25vd19hZGQ9RmFsc2UsIHZlcmJvc2VfbmFtZT11J+WIm+W7uuaXtumXtCcpXG4gICAgemhhbmdoYW89bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsdW5pcXVlPVRydWUsIHZlcmJvc2VfbmFtZT0n6LSm5Y+3JyApXG4gICAgbWltYT1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WvhueggScgKVxuICAgIG5vbmdodXhpbmdtaW5nPW1vZGVscy5DaGFyRmllbGQgKCBtYXhfbGVuZ3RoPTI1NSxudWxsPUZhbHNlLCB1bmlxdWU9RmFsc2UsIHZlcmJvc2VfbmFtZT0n5Yac5oi35aeT5ZCNJyApXG4gICAgdG91eGlhbmc9bW9kZWxzLlRleHRGaWVsZCAgICggbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+WktOWDjycgKVxuICAgIHhpbmdiaWU9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSfmgKfliKsnIClcbiAgICBuaWFubGluZz1tb2RlbHMuSW50ZWdlckZpZWxkICAoICBudWxsPVRydWUsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSflubTpvoQnIClcbiAgICB5b3V4aWFuZz1tb2RlbHMuQ2hhckZpZWxkICggbWF4X2xlbmd0aD0yNTUsbnVsbD1GYWxzZSwgdW5pcXVlPUZhbHNlLCB2ZXJib3NlX25hbWU9J+mCrueusScgKVxuICAgIGRpYW5odWE9bW9kZWxzLkNoYXJGaWVsZCAoIG1heF9sZW5ndGg9MjU1LG51bGw9RmFsc2UsIHVuaXF1ZT1GYWxzZSwgdmVyYm9zZV9uYW1lPSfnlLXor50nIClcbiAgICAnJydcbiAgICB6aGFuZ2hhbz1WQVJDSEFSXG4gICAgbWltYT1WQVJDSEFSXG4gICAgbm9uZ2h1eGluZ21pbmc9VkFSQ0hBUlxuICAgIHRvdXhpYW5nPVRleHRcbiAgICB4aW5nYmllPVZBUkNIQVJcbiAgICBuaWFubGluZz1JbnRlZ2VyXG4gICAgeW91eGlhbmc9VkFSQ0hBUlxuICAgIGRpYW5odWE9VkFSQ0hBUlxuICAgICcnJ1xuICAgIGNsYXNzIE1ldGE6XG4gICAgICAgIGRiX3RhYmxlID0gJ25vbmdodSdcbiAgICAgICAgdmVyYm9zZV9uYW1lID0gdmVyYm9zZV9uYW1lX3BsdXJhbCA9ICflhpzmiLcnXG5jbGFzcyBndW95dWFueGlueGkoQmFzZU1vZGVsKTpcbiAgICBfX2RvY19fID0gdScnJ2d1b3l1YW54aW54aScnJ1xuICAgIF9fdGFibGVuYW1lX18gPSAnZ3VveXVhbnhpbnhpJ1xuXG5cblxuICAgIF9fYXV0aFRhYmxlc19fPXsnemhhbmdoYW8nOidub25naHUnLH1cbiAgICBfX2F1dGhQZW9wbGVfXz0n5ZCmJyPnlKjmiLfooajvvIzooajlsZ7mgKdsb2dpblVzZXJDb2x1bW7lr7nlupTnmoTlgLzlsLHmmK/nlKjmiLflkI3lrZfmrrXvvIxtaW1h5bCx5piv5a+G56CB5a2X5q61XG4gICAgX19zZnNoX189J+WQpicj6KGoc2ZzaCjmmK/lkKblrqHmoLjvvIzigJ3mmK/igJ3miJbigJ3lkKbigJ0p5a2X5q615ZKMc2ZoZijlrqHmoLjlm57lpI0p5a2X5q6177yM5ZCO5Y+w5YiX6KGoKHBhZ2Up55qE5pON5L2c5Lit6KaB5aSa5LiA5Liq4oCd5a6h5qC44oCd5oyJ6ZKu77yM54K55Ye74oCd5a6h5qC44oCd5by55Ye65LiA5Liq6aG16Z2i77yM5YyF5ZCr4oCd5piv5ZCm5a6h5qC44oCd5ZKM4oCd5a6h5qC45Zue5aSN4oCd77yM54K55Ye756Gu5a6a6LCD55SodXBkYXRl5o6l5Y+j77yM5L+u5pS5c2ZzaOWSjHNmaGbkuKTkuKrlrZfmrrXjgIJcbiAgICBfX2F1dGhTZXBhcmF0ZV9fPSflkKYnI+WQjuWPsOWIl+ihqOadg+mZkFxuICAgIF9fdGh1bWJzVXBfXz0n5ZCmJyPooajlsZ7mgKd0aHVtYnNVcFvmmK8v5ZCmXe+8jOaWsOWinnRodW1ic3VwbnVt6LWe5ZKMY3JhemlseW51bei4qeWtl+autVxuICAgIF9faW50ZWxSZWNvbV9fPSflkKYnI+aZuuiDveaOqOiNkOWKn+iDvSjooajlsZ7mgKfvvJpbaW50ZWxSZWNvbe+8iOaYry/lkKbvvIldLOaWsOWinmNsaWNrdGltZVvliY3nq6/kuI3mmL7npLror6XlrZfmrrVd5a2X5q6177yI6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnmm7TmlrDvvInvvIzmjIljbGlja3RpbWXmjpLluo/mn6Xor6IpXG4gICAgX19icm93c2VDbGlja19fPSflkKYnI+ihqOWxnuaAp1ticm93c2VDbGljazrmmK8v5ZCmXe+8jOeCueWHu+Wtl+aute+8iGNsaWNrbnVt77yJ77yM6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnlkI7nq6/oh6rliqgrMe+8ieOAgeaKleelqOWKn+iDve+8iOihqOWxnuaAp1t2b3RlOuaYry/lkKZd77yM5oqV56Wo5a2X5q6177yIdm90ZW51be+8iSzosIPnlKh2b3Rl5o6l5Y+j5ZCO56uvdm90ZW51bSsxXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXz0n5ZCmJyPliY3lj7DliJfooajmnYPpmZBmb3JlRW5kTGlzdEF1dGhb5pivL+WQpl3vvJvlvZNmb3JlRW5kTGlzdEF1dGg95piv77yM5Yi355qE6KGo5paw5aKe55So5oi35a2X5q61dXNlcmlk77yM5YmN5Y+wbGlzdOWIl+ihqOaOpeWPo+S7heiDveafpeeci+iHquW3seeahOiusOW9leWSjGFkZOaOpeWPo+WQjuWPsOi1i+WAvHVzZXJpZOeahOWAvFxuLi4uXG5cblxuXG4gICAgX19hdXRoVGFibGVzX189eyd6aGFuZ2hhbyc6J25vbmdodScsfVxuICAgIF9fYXV0aFBlb3BsZV9fPSflkKYnI+eUqOaIt+ihqO+8jOihqOWxnuaAp2xvZ2luVXNlckNvbHVtbuWvueW6lOeahOWAvOWwseaYr+eUqOaIt+WQjeWtl+aute+8jG1pbWHlsLHmmK/lr4bnoIHlrZfmrrVcbiAgICBfX3Nmc2hfXz0n5ZCmJyPooahzZnNoKOaYr+WQpuWuoeaguO+8jOKAneaYr+KAneaIluKAneWQpuKAnSnlrZfmrrXlkoxzZmhmKOWuoeaguOWbnuWkjSnlrZfmrrXvvIzlkI7lj7DliJfooagocGFnZSnnmoTmk43kvZzkuK3opoHlpJrkuIDkuKrigJ3lrqHmoLjigJ3mjInpkq7vvIzngrnlh7vigJ3lrqHmoLjigJ3lvLnlh7rkuIDkuKrpobXpnaLvvIzljIXlkKvigJ3mmK/lkKblrqHmoLjigJ3lkozigJ3lrqHmoLjlm57lpI3igJ3vvIzngrnlh7vnoa7lrprosIPnlKh1cGRhdGXmjqXlj6PvvIzkv67mlLlzZnNo5ZKMc2ZoZuS4pOS4quWtl+auteOAglxuICAgIF9fYXV0aFNlcGFyYXRlX189J+WQpicj5ZCO5Y+w5YiX6KGo5p2D6ZmQXG4gICAgX190aHVtYnNVcF9fPSflkKYnI+ihqOWxnuaAp3RodW1ic1VwW+aYry/lkKZd77yM5paw5aKedGh1bWJzdXBudW3otZ7lkoxjcmF6aWx5bnVt6Lip5a2X5q61XG4gICAgX19pbnRlbFJlY29tX189J+WQpicj5pm66IO95o6o6I2Q5Yqf6IO9KOihqOWxnuaAp++8mltpbnRlbFJlY29t77yI5pivL+WQpu+8iV0s5paw5aKeY2xpY2t0aW1lW+WJjeerr+S4jeaYvuekuuivpeWtl+autV3lrZfmrrXvvIjosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeabtOaWsO+8ie+8jOaMiWNsaWNrdGltZeaOkuW6j+afpeivoilcbiAgICBfX2Jyb3dzZUNsaWNrX189J+WQpicj6KGo5bGe5oCnW2Jyb3dzZUNsaWNrOuaYry/lkKZd77yM54K55Ye75a2X5q6177yIY2xpY2tudW3vvInvvIzosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeWQjuerr+iHquWKqCsx77yJ44CB5oqV56Wo5Yqf6IO977yI6KGo5bGe5oCnW3ZvdGU65pivL+WQpl3vvIzmipXnpajlrZfmrrXvvIh2b3RlbnVt77yJLOiwg+eUqHZvdGXmjqXlj6PlkI7nq692b3RlbnVtKzFcbiAgICBfX2ZvcmVFbmRMaXN0QXV0aF9fPSflkKYnI+WJjeWPsOWIl+ihqOadg+mZkGZvcmVFbmRMaXN0QXV0aFvmmK8v5ZCmXe+8m+W9k2ZvcmVFbmRMaXN0QXV0aD3mmK/vvIzliLfnmoTooajmlrDlop7nlKjmiLflrZfmrrV1c2VyaWTvvIzliY3lj7BsaXN05YiX6KGo5o6l5Y+j5LuF6IO95p+l55yL6Ieq5bex55qE6K6w5b2V5ZKMYWRk5o6l5Y+j5ZCO5Y+w6LWL5YC8dXNlcmlk55qE5YC8XG4uLi5cbiAgICBfX3RhYmxlbmFtZV9fID0gJ2d1b3BpbnhpbnhpJ1xuXG5cblxuICAgIF9fYXV0aFRhYmxlc19fPXsnemhhbmdoYW8nOidub25naHUnLH1cbiAgICBfX2F1dGhQZW9wbGVfXz0n5ZCmJyPnlKjmiLfooajvvIzooajlsZ7mgKdsb2dpblVzZXJDb2x1bW7lr7nlupTnmoTlgLzlsLHmmK/nlKjmiLflkI3lrZfmrrXvvIxtaW1h5bCx5piv5a+G56CB5a2X5q61XG4gICAgX19zZnNoX189J+WQpicj6KGoc2ZzaCjmmK/lkKblrqHmoLjvvIzigJ3mmK/igJ3miJbigJ3lkKbigJ0p5a2X5q615ZKMc2ZoZijlrqHmoLjlm57lpI0p5a2X5q6177yM5ZCO5Y+w5YiX6KGoKHBhZ2Up55qE5pON5L2c5Lit6KaB5aSa5LiA5Liq4oCd5a6h5qC44oCd5oyJ6ZKu77yM54K55Ye74oCd5a6h5qC44oCd5by55Ye65LiA5Liq6aG16Z2i77yM5YyF5ZCr4oCd5piv5ZCm5a6h5qC44oCd5ZKM4oCd5a6h5qC45Zue5aSN4oCd77yM54K55Ye756Gu5a6a6LCD55SodXBkYXRl5o6l5Y+j77yM5L+u5pS5c2ZzaOWSjHNmaGbkuKTkuKrlrZfmrrXjgIJcbiAgICBfX2F1dGhTZXBhcmF0ZV9fPSflkKYnI+WQjuWPsOWIl+ihqOadg+mZkFxuICAgIF9fdGh1bWJzVXBfXz0n5ZCmJyPooajlsZ7mgKd0aHVtYnNVcFvmmK8v5ZCmXe+8jOaWsOWinnRodW1ic3VwbnVt6LWe5ZKMY3JhemlseW51bei4qeWtl+autVxuICAgIF9faW50ZWxSZWNvbV9fPSflkKYnI+aZuuiDveaOqOiNkOWKn+iDvSjooajlsZ7mgKfvvJpbaW50ZWxSZWNvbe+8iOaYry/lkKbvvIldLOaWsOWinmNsaWNrdGltZVvliY3nq6/kuI3mmL7npLror6XlrZfmrrVd5a2X5q6177yI6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnmm7TmlrDvvInvvIzmjIljbGlja3RpbWXmjpLluo/mn6Xor6IpXG4gICAgX19icm93c2VDbGlja19fPSflkKYnI+ihqOWxnuaAp1ticm93c2VDbGljazrmmK8v5ZCmXe+8jOeCueWHu+Wtl+aute+8iGNsaWNrbnVt77yJ77yM6LCD55SoaW5mby9kZXRhaWzmjqXlj6PnmoTml7blgJnlkI7nq6/oh6rliqgrMe+8ieOAgeaKleelqOWKn+iDve+8iOihqOWxnuaAp1t2b3RlOuaYry/lkKZd77yM5oqV56Wo5a2X5q6177yIdm90ZW51be+8iSzosIPnlKh2b3Rl5o6l5Y+j5ZCO56uvdm90ZW51bSsxXG4gICAgX19mb3JlRW5kTGlzdEF1dGhfXz0n5ZCmJyPliY3lj7DliJfooajmnYPpmZBmb3JlRW5kTGlzdEF1dGhb5pivL+WQpl3vvJvlvZNmb3JlRW5kTGlzdEF1dGg95piv77yM5Yi355qE6KGo5paw5aKe55So5oi35a2X5q61dXNlcmlk77yM5YmN5Y+wbGlzdOWIl+ihqOaOpeWPo+S7heiDveafpeeci+iHquW3seeahOiusOW9leWSjGFkZOaOpeWPo+WQjuWPsOi1i+WAvHVzZXJpZOeahOWAvFxuLi4uXG5QYXRoOiBtYWluL21vZGVsLnB5XG4uLi5cblxuICAgIGRlZiBfX0dldEJ5UGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fbG9naW5Vc2VyX18gPSBtb2RlbC5fX2xvZ2luVXNlcl9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fbG9naW5Vc2VyX18gPSBOb25lXG5cbiAgICAgICAgaWYgX19sb2dpblVzZXJfXyAhPSBOb25lIGFuZCBfX2xvZ2luVXNlcl9fIT0ndXNlcm5hbWUnOlxuICAgICAgICAgICAgaWYgcGFyYW1zLmdldCgndXNlcm5hbWUnKTpcbiAgICAgICAgICAgICAgICBwYXJhbXNbbW9kZWwuX19sb2dpblVzZXJfX10gPSBjb3B5LmRlZXBjb3B5KHBhcmFtcy5nZXQoJ3VzZXJuYW1lJykpXG4gICAgICAgICAgICAgICAgZGVsIHBhcmFtc1sndXNlcm5hbWUnXVxuICAgICAgICBpZiBtb2RlbC5fX3RhYmxlbmFtZV9fICE9ICd1c2Vycyc6XG4gICAgICAgICAgICBpZiBwYXJhbXMuZ2V0KCdwYXNzd29yZCcpOlxuICAgICAgICAgICAgICAgIHBhcmFtc1snbWltYSddID0gY29weS5kZWVwY29weShwYXJhbXMuZ2V0KCdwYXNzd29yZCcpKVxuICAgICAgICAgICAgICAgICMgZGVsIHBhcmFtc1sncGFzc3dvcmQnXVxuXG4gICAgICAgICMg5YmN56uv5Lyg5LqG5peg55So5Y+C5pWw5ZKM5Lyg6ZSZ5Y+C5pWw5ZCN77yM5Zyo6L+Z6YeM5L+u5pS5XG4gICAgICAgIHBhcmFtc3MgPSB7fVxuICAgICAgICBjb2x1bW5MaXN0ID0gc2VsZi5nZXRhbGxjb2x1bW4obW9kZWwsIG1vZGVsKVxuICAgICAgICBmb3IgaywgdiBpbiBwYXJhbXMuaXRlbXMoKTpcbiAgICAgICAgICAgIGlmIGsgaW4gY29sdW1uTGlzdDpcbiAgICAgICAgICAgICAgICBwYXJhbXNzW2tdID0gdlxuXG4gICAgICAgIGRhdGFzXyA9IG1vZGVsLm9iamVjdHMuZmlsdGVyKCoqcGFyYW1zcykuYWxsKClcblxuICAgICAgICByZXR1cm4gc2VsZi50b19saXN0KGRhdGFzXywgZGF0YXNfLG1vZGVsKVxuXG4gICAgZGVmIGdldGJ5cGFyYW1zKHNlbGYsIG1vZGVsLCBwYXJhbXMpOlxuICAgICAgICByZXR1cm4gc2VsZi5fX0dldEJ5UGFyYW1zKG1vZGVsLCBtb2RlbCwgcGFyYW1zKVxuLi4uXG5QYXRoOiBtYWluL3NjaGVtYV92LnB5XG4uLi5cblxuICAgICAgICBpZiBmaWxlOlxuICAgICAgICAgICAgZmlsZW5hbWUgPSBmaWxlLm5hbWVcbiAgICAgICAgICAgIGZpbGVzdWZmaXggPSBmaWxlbmFtZS5zcGxpdChcIi5cIilbLTFdXG4gICAgICAgICAgICBmaWxlX25hbWUgPSBcInt9Lnt9XCIuZm9ybWF0KGludChmbG9hdCh0aW1lLnRpbWUoKSkgKiAxMDAwKSwgZmlsZXN1ZmZpeClcbiAgICAgICAgICAgIGlmIHR5cGUgIT0gTm9uZSBhbmQgJ190ZW1wbGF0ZScgaW4gdHlwZTpcbiAgICAgICAgICAgICAgICBmaWxlX25hbWU9XCJ7fS57fVwiLmZvcm1hdCh0eXBlLGZpbGVzdWZmaXgpXG4gICAgICAgICAgICBmaWxlUGF0aCA9IG9zLnBhdGguam9pbihvcy5nZXRjd2QoKSwgXCJ0ZW1wbGF0ZXMvdXBsb2FkXCIsIGZpbGVfbmFtZSlcbiAgICAgICAgICAgIHByaW50KFwiZmlsZVBhdGg9PT09PT09PT09PT5cIiwgZmlsZVBhdGgpXG5cbiAgICAgICAgICAgIHdpdGggb3BlbihmaWxlUGF0aCwgJ3diKycpIGFzIGRlc3RpbmF0aW9uOlxuICAgICAgICAgICAgICAgIGZvciBjaHVuayBpbiBmaWxlLmNodW5rcygpOlxuICAgICAgICAgICAgICAgICAgICBkZXN0aW5hdGlvbi53cml0ZShjaHVuaylcbiAgICAgICAgICAgIG1zZ1tcImZpbGVcIl0gPSBmaWxlX25hbWVcbiAgICAgICAgICAgICMg5Yik5pat5piv5ZCm6ZyA6KaB5L+d5a2Y5Li65Lq66IS46K+G5Yir5Z+656GA54Wn54mHXG4gICAgICAgICAgICByZXFfZGljdCA9IHJlcXVlc3Quc2Vzc2lvbi5nZXQoXCJyZXFfZGljdFwiKVxuICAgICAgICAgICAgdHlwZTEgPSByZXFfZGljdC5nZXQoXCJ0eXBlXCIsIDApXG4gICAgICAgICAgICBwcmludChcInR5cGUxPT09PT09PT5cIix0eXBlMSlcbiAgICAgICAgICAgIHR5cGUxID0gc3RyKHR5cGUxKVxuICAgICAgICAgICAgaWYgdHlwZTEgPT0gJzEnOlxuICAgICAgICAgICAgICAgIHBhcmFtcyA9IHtcIm5hbWVcIjpcImZhY2VGaWxlXCIsXCJ2YWx1ZVwiOiBmaWxlX25hbWV9XG4gICAgICAgICAgICAgICAgY29uZmlnLmNyZWF0ZWJ5cmVxKGNvbmZpZywgY29uZmlnLCBwYXJhbXMpXG4gICAgICAgICAgICAjIEhERlMgdXBsb2FkIHJlbW92ZWRcblxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcbi4uLlxuUGF0aDogbWFpbi9Ob25naHVfdi5weVxuLi4uXG5cbmRlZiBub25naHVfYXV0b1NvcnQocmVxdWVzdCk6XG4gICAgJycnXG4gICAg77yO5pm66IO95o6o6I2Q5Yqf6IO9KOihqOWxnuaAp++8mltpbnRlbFJlY29t77yI5pivL+WQpu+8iV0s5paw5aKeY2xpY2t0aW1lW+WJjeerr+S4jeaYvuekuuivpeWtl+autV3lrZfmrrXvvIjosIPnlKhpbmZvL2RldGFpbOaOpeWPo+eahOaXtuWAmeabtOaWsO+8ie+8jOaMiWNsaWNrdGltZeaOkuW6j+afpeivoilcbuS4u+imgeS/oeaBr+WIl+ihqO+8iOWmguWVhuWTgeWIl+ihqO+8jOaWsOmXu+WIl+ihqO+8ieS4reS9v+eUqO+8jOaYvuekuuacgOi/keeCueWHu+eahOaIluacgOaWsOa3u+WKoOeahDXmnaHorrDlvZXlsLHooYxcbiAgICAnJydcbiAgICBpZiByZXF1ZXN0Lm1ldGhvZCBpbiBbXCJQT1NUXCIsIFwiR0VUXCJdOlxuICAgICAgICBtc2cgPSB7XCJjb2RlXCI6IG5vcm1hbF9jb2RlLCBcIm1zZ1wiOiBtZXMubm9ybWFsX2NvZGUsICBcImRhdGFcIjp7XCJjdXJyUGFnZVwiOjEsXCJ0b3RhbFBhZ2VcIjoxLFwidG90YWxcIjoxLFwicGFnZVNpemVcIjoxMCxcImxpc3RcIjpbXX19XG4gICAgICAgIHJlcV9kaWN0ID0gcmVxdWVzdC5zZXNzaW9uLmdldChcInJlcV9kaWN0XCIpXG4gICAgICAgIGlmIFwiY2xpY2tudW1cIiAgaW4gbm9uZ2h1LmdldGFsbGNvbHVtbihub25naHUsbm9uZ2h1KTpcbiAgICAgICAgICAgIHJlcV9kaWN0Wydzb3J0J109J2NsaWNrbnVtJ1xuICAgICAgICBlbGlmIFwiYnJvd3NlZHVyYXRpb25cIiAgaW4gbm9uZ2h1LmdldGFsbGNvbHVtbihub25naHUsbm9uZ2h1KTpcbiAgICAgICAgICAgIHJlcV9kaWN0Wydzb3J0J109J2Jyb3dzZWR1cmF0aW9uJ1xuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgcmVxX2RpY3RbJ3NvcnQnXT0nY2xpY2t0aW1lJ1xuICAgICAgICByZXFfZGljdFsnb3JkZXInXT0nZGVzYydcbiAgICAgICAgbXNnWydkYXRhJ11bJ2xpc3QnXSwgbXNnWydkYXRhJ11bJ2N1cnJQYWdlJ10sIG1zZ1snZGF0YSddWyd0b3RhbFBhZ2UnXSwgbXNnWydkYXRhJ11bJ3RvdGFsJ10sIFxcXG4uLi5cblxuZGVmIG5vbmdodV9saXN0KHJlcXVlc3QpOlxuICAgICcnJ1xuICAgIOWJjeWPsOWIhumhtVxuICAgICcnJ1xuICAgIGlmIHJlcXVlc3QubWV0aG9kIGluIFtcIlBPU1RcIiwgXCJHRVRcIl06XG4gICAgICAgIG1zZyA9IHtcImNvZGVcIjogbm9ybWFsX2NvZGUsIFwibXNnXCI6IG1lcy5ub3JtYWxfY29kZSwgIFwiZGF0YVwiOntcImN1cnJQYWdlXCI6MSxcInRvdGFsUGFnZVwiOjEsXCJ0b3RhbFwiOjEsXCJwYWdlU2l6ZVwiOjEwLFwibGlzdFwiOltdfX1cbiAgICAgICAgcmVxX2RpY3QgPSByZXF1ZXN0LnNlc3Npb24uZ2V0KFwicmVxX2RpY3RcIilcbiAgICAgICAgI+iOt+WPluWFqOmDqOWIl+WQjVxuICAgICAgICBjb2x1bW5zPSAgbm9uZ2h1LmdldGFsbGNvbHVtbiggbm9uZ2h1LCBub25naHUpXG4gICAgICAgIGlmIFwidmlwcmVhZFwiIGluIHJlcV9kaWN0IGFuZCBcInZpcHJlYWRcIiBub3QgaW4gY29sdW1uczpcbiAgICAgICAgICBkZWwgcmVxX2RpY3RbXCJ2aXByZWFkXCJdXG4gICAgICAgICPooajlsZ7mgKdbZm9yZUVuZExpc3Rd5YmN5Y+wbGlzdDrlkozlkI7lj7Dpu5jorqTnmoRsaXN05YiX6KGo6aG155u45Ly8LOWPquaYr+aRhuWcqOWJjeWPsCzlkKY65oyH5rKh5pyJ5q2k6aG1LOaYrzrooajnpLrmnInmraTpobUo5LiN6ZyA6KaB55m76ZmG5Y2z5Y+v5p+l55yLKSzliY3opoHnmbs66KGo56S65pyJ5q2k6aG15LiU6ZyA6KaB55m76ZmG5ZCO5omN6IO95p+l55yLXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RfXz1ub25naHUuX19mb3JlRW5kTGlzdF9fXG4gICAgICAgIGV4Y2VwdDpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RfXz1Ob25lXG4gICAgICAgIHRyeTpcbiAgICAgICAgICAgIF9fZm9yZUVuZExpc3RBdXRoX189bm9uZ2h1Ll9fZm9yZUVuZExpc3RBdXRoX19cbiAgICAgICAgZXhjZS4uLiIsImlzRXJyb3IiOmZhbHNlLCJyZXF1ZXN0SWQiOiIifX0sIjRhYWUzMzdkLTZmZjctNDM0YS04MmU1LTRiN2E3NWI2ODUwODt0b29sdV92cnR4XzAxMWF3VXRMVlJ4RkNxcXFEUmNuRm9hYSI6eyJyZXF1ZXN0SWQiOiI0YWFlMzM3ZC02ZmY3LTQzNGEtODJlNS00YjdhNzViNjg1MDgiLCJ0b29sVXNlSWQiOiJ0b29sdV92cnR4XzAxMWF3VXRMVlJ4RkNxcXFEUmNuRm9hYSIsInBoYXNlIjo1LCJyZXN1bHQiOnsidGV4dCI6IlRoZSBmb2xsb3dpbmcgY29kZSBzZWN0aW9ucyB3ZXJlIHJldHJpZXZlZDpcblBhdGg6IHV0aWwvYXV0aC5weVxuIyBjb2Rpbmc6dXRmLThcbiMgYXV0aG9yOmlsYVxuaW1wb3J0IGJhc2U2NCwgY29weVxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5cbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsIGltcG9ydCBtZXNzYWdlIGFzIG1lc1xuXG5cbmNsYXNzIEF1dGgob2JqZWN0KTpcbiAgICBkZWYgYXV0aGVudGljYXRlKHNlbGYsIG1vZGVsLCByZXFfZGljdCk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDnlKjmiLfnmbvlvZXvvIznmbvlvZXmiJDlip/ov5Tlm550b2tlbu+8m+eZu+W9leWksei0pei/lOWbnuWksei0peWOn+WboFxuICAgICAgICA6cGFyYW0gdXNlcm5hbWU66LSm5Y+3XG4gICAgICAgIDpwYXJhbSBwYXNzd29yZDrlr4bnoIFcbiAgICAgICAgOnJldHVybjoganNvblxuICAgICAgICBcIlwiXCJcbiAgICAgICAgbXNnID0geydjb2RlJzogbm9ybWFsX2NvZGUsICdtc2cnOiBtZXMubm9ybWFsX2NvZGUsICdkYXRhJzoge319XG4gICAgICAgIHRhYmxlbmFtZSA9IG1vZGVsLl9fdGFibGVuYW1lX19cbiAgICAgICAgZW5jb2RlX2RpY3QgPSB7XCJ0YWJsZW5hbWVcIjogdGFibGVuYW1lLCBcInBhcmFtc1wiOiByZXFfZGljdH1cblxuICAgICAgICBlbmNvZGVfc3RyID0gYmFzZTY0LmI2NGVuY29kZShzdHIoZW5jb2RlX2RpY3QpLmVuY29kZShcInV0Zi04XCIpKVxuICAgICAgICBtc2dbJ2RhdGEnXVtcImlkXCJdID0gcmVxX2RpY3QuZ2V0KFwiaWRcIilcbiAgICAgICAgbXNnW1wiaWRcIl0gPSByZXFfZGljdC5nZXQoXCJpZFwiKVxuICAgICAgICBtc2dbJ3Rva2VuJ10gPSBlbmNvZGVfc3RyLmRlY29kZSgndXRmLTgnKVxuICAgICAgICByZXR1cm4gSnNvblJlc3BvbnNlKG1zZylcblxuICAgIGRlZiBnZXRfdG9rZW4oc2VsZiwgbW9kZWwsIHJlcV9kaWN0KTpcbiAgICAgICAgdGFibGVuYW1lPW1vZGVsLl9fdGFibGVuYW1lX19cbiAgICAgICAgZW5jb2RlX2RpY3QgPSB7XCJ0YWJsZW5hbWVcIjp0YWJsZW5hbWUsIFwicGFyYW1zXCI6IHJlcV9kaWN0fVxuXG4gICAgICAgIGVuY29kZV9zdHIgPSBiYXNlNjQuYjY0ZW5jb2RlKHN0cihlbmNvZGVfZGljdCkuZW5jb2RlKFwidXRmOFwiKSlcbiAgICAgICAgcmV0dXJuIGVuY29kZV9zdHIuZGVjb2RlKCd1dGYtOCcpXG5cbiAgICBkZWYgaWRlbnRpZnkoc2VsZiwgcmVxdWVzdCk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDnlKjmiLfpibTmnYNcbiAgICAgICAgOnBhcmFtIHJlcXVlc3Q65pys5qyh6K+35rGC5a+56LGhXG4gICAgICAgIDpyZXR1cm46IGxpc3RcbiAgICAgICAgXCJcIlwiXG5cbiAgICAgICAgbXNnID0geydjb2RlJzogbm9ybWFsX2NvZGUsICdtc2cnOiBtZXMubm9ybWFsX2NvZGUsICdkYXRhJzoge319XG4gICAgICAgICMgZGphbmdv55qEaGVhZGVy6KKr5aSE55CG6L+H5LqGXG4gICAgICAgIHRva2VuID0gcmVxdWVzdC5NRVRBLmdldCgnSFRUUF9UT0tFTicpXG5cbiAgICAgICAgaWYgdG9rZW4gIGFuZCB0b2tlbiAhPVwibnVsbFwiOlxuXG4gICAgICAgICAgICBhdXRoX3Rva2VuID0gY29weS5kZWVwY29weSh0b2tlbilcblxuICAgICAgICAgICAgZGVjb2RlX3N0ciA9IGJhc2U2NC5iNjRkZWNvZGUoYXV0aF90b2tlbikuZGVjb2RlKFwidXRmOFwiKVxuICAgICAgICAgICAgZGVjb2RlX3N0cj1kZWNvZGVfc3RyLnJlcGxhY2UoJ1wibnVsbFwiJywnXCJcIicpLnJlcGxhY2UoJ251bGwnLCdcIlwiJylcbiAgICAgICAgICAgIGRlY29kZV9kaWN0ID0gZXZhbChkZWNvZGVfc3RyKVxuXG4gICAgICAgICAgICB0YWJsZW5hbWUyID0gZGVjb2RlX2RpY3QuZ2V0KFwidGFibGVuYW1lXCIpXG5cbiAgICAgICAgICAgIHBhcmFtczIgPSBkZWNvZGVfZGljdC5nZXQoXCJwYXJhbXNcIix7fSlcblxuICAgICAgICAgICAgZGF0YXM9Tm9uZVxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxuICAgICAgICAgICAgZm9yIG1vZGVsIGluIGFsbE1vZGVsczpcbiAgICAgICAgICAgICAgICBpZiBtb2RlbC5fX3RhYmxlbmFtZV9fID09IHRhYmxlbmFtZTI6XG4gICAgICAgICAgICAgICAgICAgIGRhdGFzID0gbW9kZWwuZ2V0YnlwYXJhbXMobW9kZWwsIG1vZGVsLCBwYXJhbXMyKVxuXG4gICAgICAgICAgICBpZiBub3QgZGF0YXM6XG4gICAgICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcbiAgICAgICAgICAgICAgICBtc2dbJ21zZyddID0gJ+aJvuS4jeWIsOivpeeUqOaIt+S/oeaBrydcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBtc2dcbiAgICAgICAgICAgIGVsc2U6XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5zZXNzaW9uWyd0YWJsZW5hbWUnXSA9IHRhYmxlbmFtZTJcbiAgICAgICAgICAgICAgICByZXF1ZXN0LnNlc3Npb25bJ3BhcmFtcyddID0gcGFyYW1zMlxuICAgICAgICAgICAgICAgIG1zZ1snbXNnJ10gPSAn6Lqr5Lu96aqM6K+B6YCa6L+H44CCJ1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9IG1zZ1xuICAgICAgICBlbHNlOlxuICAgICAgICAgICAgbXNnWydjb2RlJ10gPSA0MDFcbiAgICAgICAgICAgIG1zZ1snbXNnJ10gPSAnaGVhZGVyc+acquWMheWQq+iupOivgeS/oeaBr+OAgidcbiAgICAgICAgICAgIHJlc3VsdCA9IG1zZ1xuICAgICAgICByZXR1cm4gcmVzdWx0XG4uLi5cblBhdGg6IHV0aWwvY29kZXMucHlcbiMgY29kaW5nOnV0Zi04XG5ub3JtYWxfY29kZSA9IDAgICMg5q2j5bi4XG5yZWRpcmVjdF9jb2RlID0gMzAxICAjIOi3s+i9rFxudGVtcG9yYXJ5X3JlZGlyZWN0X2NvZGUgPSAzMDIgICMg5pqC5pe26Lez6L2sXG5cbmVtcHR5X3BhcmFtX2NvZGUgPSAxMDAwMSAgIyDor7fmsYLlj4LmlbDkuLrnqbrmiJbplJnor69cbnZhbGlkYXRlX3BhcmFtX2NvZGUgPSAxMDAwMiAgIyDor7fmsYLlj4LmlbDkuI3op4TojINcbmlzX25vdF9qc29uX2NvZGUgPSAxMDAwMyAgIyDmlbDmja7moLzlvI/kuI3mmK9qc29uXG5cbm90aGVyX2NvZGUgPSAxMDAyMCAgIyDlhbblroPplJnor69cbmNydWRfZXJyb3JfY29kZSA9IDEwMDIxICAjIOaVsOaNruW6k+aTjeS9nOWksei0pVxuaGVhZGVyX2Vycm9yX2NvZGUgPSAxMDAyMyAgIyDlpLTpg6jplJnor69cbmNhcHRjaGFfZXJyb3JfY29kZSA9IDEwMDI0ICAjIOmqjOivgeeggemUmeivr1xuaWRfZXhpc3RfY29kZSA9IDEwMDI1ICAjIGlk5oiW55So5oi35ZCN5bey5a2Y5ZyoXG5pZF9ub3RleGlzdF9jb2RlID0gMTAwMjYgICMgaWTmiJbnlKjmiLflkI3kuI3lrZjlnKhcbnVzZXJuYW1lX2Vycm9yX2NvZGUgPSAxMDAyNyAgIyDnlKjmiLflkI3plJnor69cbnBhc3N3b3JkX2Vycm9yX2NvZGUgPSAxMDAyOCAgIyDlr4bnoIHplJnor69cbmZpbGVfbm90ZXhpc3RfY29kZSA9IDEwMDI5ICAjIOS4iuS8oOaWh+S7tuS4jeWtmOWcqFxuXG50b2tlbl9lcnJvcl9jb2RlID0gMjAwMDEgICMgdG9rZW7plJnor69cbnRva2VuX2V4cGlyZWRfY29kZSA9IDIwMDAyICAjIHRva2Vu6ZSZ6K+vXG5ub25fYXV0aG9yaXplZF9jb2RlID0gMjAwMDMgICMg5peg5p2D6ZmQXG5cbnN5c3RlbV9lcnJvcl9jb2RlID0gNDAwMDEgICMg57O757uf57qn6ZSZ6K+vXG5yZXF1ZXN0X2V4cGlyZWRfY29kZSA9IDQwMDAyICAjIOivt+axguW3sui/h+acn1xucmVwZWF0ZWRfcmVxdWVzdF9jb2RlID0gNDAwMDMgICMg6YeN5aSN6K+35rGCXG4uLi5cblBhdGg6IHV0aWwvY29tbW9uLnB5XG4jIGNvZGluZzp1dGYtOFxuaW1wb3J0IGhhc2hsaWJcbmltcG9ydCBiYXNlNjRcbmZyb20gQ3J5cHRvLkNpcGhlciBpbXBvcnQgREVTLEFFU1xuZnJvbSBDcnlwdG8uVXRpbC5QYWRkaW5nIGltcG9ydCBwYWQsdW5wYWRcblxuY2xhc3MgQ29tbW9uKG9iamVjdCk6XG5cbiAgICBkZWYgbWQ1KHNlbGYsIHB3ZCk6XG4gICAgICAgIG1kNSA9IGhhc2hsaWIubWQ1KClcbiAgICAgICAgbWQ1LnVwZGF0ZShwd2QuZW5jb2RlKCkpXG4gICAgICAgIHJldHVybiBtZDUuaGV4ZGlnZXN0KClcblxuXG5cbi4uLlxuUGF0aDogdXRpbC9tZXNzYWdlLnB5XG4jIGNvZGluZzp1dGYtOFxubm9ybWFsX2NvZGUgPSBcIuato+W4uFwiXG5yZWRpcmVjdF9jb2RlID0gXCLot7PovaxcIlxudGVtcG9yYXJ5X3JlZGlyZWN0X2NvZGUgPSBcIuaaguaXtui3s+i9rFwiXG5cbmVtcHR5X3BhcmFtX2NvZGUgPSBcIuivt+axguWPguaVsOS4uuepuuaIlumUmeivr1wiXG52YWxpZGF0ZV9wYXJhbV9jb2RlID0gXCLor7fmsYLlj4LmlbDkuI3op4TojINcIlxuaXNfbm90X2pzb25fY29kZSA9IFwi5pWw5o2u5qC85byP5LiN5pivanNvblwiXG5cbm90aGVyX2NvZGUgPSBcIuWFtuWug+mUmeivr1wiXG5jcnVkX2Vycm9yX2NvZGUgPSBcIuaVsOaNruW6k+aTjeS9nOWksei0pVwiXG5oZWFkZXJfZXJyb3JfY29kZSA9IFwi5aS06YOo6ZSZ6K+vXCJcbmNhcHRjaGFfZXJyb3JfY29kZSA9IFwi6aqM6K+B56CB6ZSZ6K+vXCJcbmlkX2V4aXN0X2NvZGUgPSBcImlk5oiW6K6w5b2V5bey5a2Y5ZyoXCJcbmlkX25vdGV4aXN0X2NvZGUgPSBcImlk5oiW6K6w5b2V5LiN5a2Y5ZyoXCJcbmZpbGVfbm90ZXhpc3RfY29kZSA9IFwi5LiK5Lyg5paH5Lu25LiN5a2Y5ZyoXCJcbnVzZXJuYW1lX2Vycm9yX2NvZGUgPSBcIueUqOaIt+WQjemUmeivr1wiXG5wYXNzd29yZF9lcnJvcl9jb2RlID0gXCLotKblj7fmiJbogIXlr4bnoIHplJnor69cIlxuXG50b2tlbl9lcnJvcl9jb2RlID0gXCJ0b2tlbumUmeivr1wiXG50b2tlbl9leHBpcmVkX2NvZGUgPSBcInRva2Vu6ZSZ6K+vXCJcbm5vbl9hdXRob3JpemVkX2NvZGUgPSBcIuaXoOadg+mZkFwiXG5cbnN5c3RlbV9lcnJvcl9jb2RlID0gXCLns7vnu5/nuqfplJnor69cIlxucmVxdWVzdF9leHBpcmVkX2NvZGUgPSBcIuivt+axguW3sui/h+acn1wiXG5yZXBlYXRlZF9yZXF1ZXN0X2NvZGUgPSBcIumHjeWkjeivt+axglwiXG4uLi5cblBhdGg6IG1haW4vR3VveXVhbnpodWFuZ3RhaV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBndW95dWFuemh1YW5ndGFpXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vV3VsaXVfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgd3VsaXVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9HdW95dWFueGlueGlfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgZ3VveXVhbnhpbnhpXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vQ2FuZ2NodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBjYW5nY2h1XG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IG1haW4vWW9uZ2h1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IHlvbmdodVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiBtYWluL05vbmdodV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBub25naHVcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb21tb24gaW1wb3J0IENvbW1vblxuaW1wb3J0IHV0aWwubWVzc2FnZSBhcyBtZXNcbmZyb20gZGphbmdvLmRiIGltcG9ydCBjb25uZWN0aW9uXG5pbXBvcnQgcmFuZG9tXG5mcm9tIGRqYW5nby5jb3JlLm1haWwgaW1wb3J0IHNlbmRfbWFpbFxuZnJvbSBkamFuZ28uY29uZiBpbXBvcnQgc2V0dGluZ3NcbmZyb20gZGphbmdvLnNob3J0Y3V0cyBpbXBvcnQgcmVkaXJlY3RcbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgUVxuZnJvbSB1dGlsLmJhaWR1YmNlX2FwaSBpbXBvcnQgQmFpRHVCY2Vcbi4uLlxuUGF0aDogbWFpbi9HdW9waW54aW54aV92LnB5XG4jY29kaW5nOnV0Zi04XG5pbXBvcnQgYmFzZTY0LCBjb3B5LCBsb2dnaW5nLCBvcywgc3lzLCB0aW1lLCB4bHJkLCBqc29uLCBkYXRldGltZSwgY29uZmlncGFyc2VyXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcbmltcG9ydCBudW1iZXJzXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMuYWdncmVnYXRlcyBpbXBvcnQgQ291bnQsU3VtXG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IENhc2UsIFdoZW4sIEludGVnZXJGaWVsZCwgRlxuZnJvbSBkamFuZ28uZm9ybXMgaW1wb3J0IG1vZGVsX3RvX2RpY3RcbmltcG9ydCByZXF1ZXN0c1xuZnJvbSB1dGlsLkN1c3RvbUpTT05FbmNvZGVyIGltcG9ydCBDdXN0b21Kc29uRW5jb2RlclxuZnJvbSAubW9kZWxzIGltcG9ydCBndW9waW54aW54aVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiB1dGlsL0N1c3RvbUpTT05FbmNvZGVyLnB5XG5mcm9tIGRhdGV0aW1lIGltcG9ydCBkYXRldGltZSwgZGF0ZVxuaW1wb3J0IGpzb25cbmZyb20gZGVjaW1hbCBpbXBvcnQgRGVjaW1hbFxuXG5jbGFzcyBDdXN0b21Kc29uRW5jb2Rlcihqc29uLkpTT05FbmNvZGVyKTpcbiAgICBkZWYgZGVmYXVsdChzZWxmLCBvYmopOlxuICAgICAgICBpZiBpc2luc3RhbmNlKG9iaiwgZGF0ZXRpbWUpOlxuICAgICAgICAgICAgcmV0dXJuIG9iai5zdHJmdGltZSgnJVktJW0tJWQgJUg6JU06JVMnKVxuICAgICAgICBpZiBpc2luc3RhbmNlKG9iaiwgZGF0ZSk6XG4gICAgICAgICAgICByZXR1cm4gb2JqLnN0cmZ0aW1lKCclWS0lbS0lZCcpXG4gICAgICAgIGlmIGlzaW5zdGFuY2Uob2JqLCBEZWNpbWFsKTpcbiAgICAgICAgICAgICMg5aaC5p6c5piv5pW05pWw77yM6L+U5Zue5pW05pWwXG4gICAgICAgICAgICBpZiBvYmogJSAxID09IDA6XG4gICAgICAgICAgICAgICAgcmV0dXJuIGludChvYmopXG4gICAgICAgICAgICAjIOWQpuWImei/lOWbnua1rueCueaVsFxuICAgICAgICAgICAgcmV0dXJuIGZsb2F0KG9iailcbiAgICAgICAgcmV0dXJuIHN1cGVyKCkuZGVmYXVsdChvYmopLi4uXG5QYXRoOiB4bWlkZGxld2FyZS94YXV0aC5weVxuI2NvZGluZzp1dGYtOFxuXG5mcm9tIGRqYW5nby51dGlscy5kZXByZWNhdGlvbiBpbXBvcnQgTWlkZGxld2FyZU1peGluXG5mcm9tIGRqYW5nby5odHRwIGltcG9ydCBKc29uUmVzcG9uc2VcbmZyb20gZGphbmdvLmFwcHMgaW1wb3J0IGFwcHNcblxuZnJvbSB1dGlsLmF1dGggaW1wb3J0IEF1dGhcbmZyb20gdXRpbC5jb2RlcyBpbXBvcnQgKlxuZnJvbSBkajIuc2V0dGluZ3MgaW1wb3J0IGRiTmFtZSBhcyBzY2hlbWFOYW1lXG5cblxuY2xhc3MgWGF1dGgoTWlkZGxld2FyZU1peGluKTpcbiAgICBkZWYgcHJvY2Vzc19yZXF1ZXN0KHNlbGYscmVxdWVzdCk6XG4gICAgICAgIGZ1bGxQYXRoID0gcmVxdWVzdC5nZXRfZnVsbF9wYXRoKClcbiAgICAgICAgcHJpbnQoXCJmdWxsUGF0aD09PT09PT09PT09PT09PT5cIiwgZnVsbFBhdGgpXG4gICAgICAgIGlmIHJlcXVlc3QuTUVUQS5nZXQoJ0hUVFBfVVBHUkFERScpPT0nd2Vic29ja2V0JzpcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICBpZiByZXF1ZXN0Lm1ldGhvZCA9PSAnR0VUJzpcblxuICAgICAgICAgICAgZmlsdGVyTGlzdD1bXG4gICAgICAgICAgICAgICAgXCIvaW5kZXhcIixcbiAgICAgICAgICAgICAgICBcIi9mb2xsb3dcIixcbiAgICAgICAgICAgICAgICBcIi9mYXZpY29uLmljb1wiLFxuICAgICAgICAgICAgICAgIFwiL2xvZ2luXCIsXG4gICAgICAgICAgICAgICAgXCIvcmVnaXN0ZXJcIixcbiAgICAgICAgICAgICAgICBcIi9ub3RpZnlcIixcbiAgICAgICAgICAgICAgICBcIi9maWxlXCIsXG4gICAgICAgICAgICAgICAgJy5qcycsXG4gICAgICAgICAgICAgICAgXCIuY3NzXCIsXG4gICAgICAgICAgICAgICAgXCIuanBnXCIsXG4gICAgICAgICAgICAgICAgXCIuanBlZ1wiLFxuICAgICAgICAgICAgICAgIFwiLnBuZ1wiLFxuICAgICAgICAgICAgICAgIFwiLmdpZlwiLFxuICAgICAgICAgICAgICAgIFwiLm1wNFwiLFxuICAgICAgICAgICAgICAgICcubXAzJyxcbiAgICAgICAgICAgICAgICBcIi50dGZcIixcbiAgICAgICAgICAgICAgICBcIi53b3RmXCIsXG4gICAgICAgICAgICAgICAgXCIud29mZlwiLFxuICAgICAgICAgICAgICAgIFwiLndvZmYyXCIsXG4gICAgICAgICAgICAgICAgXCIub3RmXCIsXG4gICAgICAgICAgICAgICAgXCIuZW90XCIsXG4gICAgICAgICAgICAgICAgXCIuc3ZnXCIsXG4gICAgICAgICAgICAgICAgXCIuY3N2XCIsXG4gICAgICAgICAgICAgICAgXCIud2VicFwiLFxuICAgICAgICAgICAgICAgIFwiLnhsc1wiLFxuICAgICAgICAgICAgICAgIFwiLnhsc3hcIixcbiAgICAgICAgICAgICAgICBcIi5kb2NcIixcbiAgICAgICAgICAgICAgICBcIi5kb2N4XCIsXG4gICAgICAgICAgICAgICAgXCIucHB0XCIsXG4gICAgICAgICAgICAgICAgXCIucHB0eFwiLFxuICAgICAgICAgICAgICAgIFwiLmh0bWxcIixcbiAgICAgICAgICAgICAgICBcIi5odG1cIixcbiAgICAgICAgICAgICAgICBcImRldGFpbFwiLFxuICAgICAgICAgICAgICAgIFwiL2ZvcnVtL2ZsaXN0XCIsXG4gICAgICAgICAgICAgICAgXCIvZm9ydW0vbGlzdFwiLFxuICAgICAgICAgICAgICAgIFwiL2FkbWluXCIsXG4gICAgICAgICAgICAgICAgXCIvc2VjdXJpdHlcIixcbiAgICAgICAgICAgICAgICBcIi9hdXRvU29ydFwiLFxuICAgICAgICAgICAgICAgIFwiL2NvbmZpZy9saXN0XCIsXG4gICAgICAgICAgICAgICAgXCIvbmV3cy9saXN0XCIsXG4gICAgICAgICAgICAgICAgXCIveGFkbWluXCIsXG4gICAgICAgICAgICAgICAgXCIvZmlsZS9kb3dubG9hZFwiLFxuICAgICAgICAgICAgICAgICBcIi97fS9yZW1pbmQvXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgICAgXCIve30vb3B0aW9uL1wiLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICBcInJlc2V0UGFzc1wiLFxuICAgICAgICAgICAgICAgIFwidXBkYXRlQnJvd3NlRHVyYXRpb25cIixcbiAgICAgICAgICAgIF1cblxuICAgICAgICAgICAgYWxsTW9kZWxzID0gYXBwcy5nZXRfYXBwX2NvbmZpZygnbWFpbicpLmdldF9tb2RlbHMoKVxuICAgICAgICAgICAgIyBwcmludChcImFsbE1vZGVsczpcIixhbGxNb2RlbHMuKVxuICAgICAgICAgICAgZm9yIG0gaW4gYWxsTW9kZWxzOlxuICAgICAgICAgICAgICAgIHRyeTpcbiAgICAgICAgICAgICAgICAgICAgZm9yZUVuZExpc3Q9bS5fX2ZvcmVFbmRMaXN0X19cbiAgICAgICAgICAgICAgICBleGNlcHQ6XG4gICAgICAgICAgICAgICAgICAgIGZvcmVFbmRMaXN0PU5vbmVcbiAgICAgICAgICAgICAgICBpZiAgZm9yZUVuZExpc3QgaXMgTm9uZSBvciBmb3JlRW5kTGlzdCAhPSBcIuWJjeimgeeZu1wiOlxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcIi97fS9zZW5kZW1haWxcIi5mb3JtYXQobS5fX3RhYmxlbmFtZV9fKSlcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyTGlzdC5hcHBlbmQoXCIve30vc2VuZHNtc1wiLmZvcm1hdChtLl9fdGFibGVuYW1lX18pKVxuICAgICAgICAgICAgICAgICAgICBmaWx0ZXJMaXN0LmFwcGVuZChcIi97fS9saXN0XCIuZm9ybWF0KG0uX190YWJsZW5hbWVfXykpXG4gICAgICAgICAgICAgICAgICAgIGZpbHRlckxpc3QuYXBwZW5kKFwiL3t9L2RldGFpbFwiLmZvcm1hdChtLl9fdGFibGVuYW1lX18pKVxuXG4gICAgICAgICAgICBhdXRoID0gVHJ1ZVxuXG4gICAgICAgICAgICBpZiBmdWxsUGF0aD09Jy8nOlxuICAgICAgICAgICAgICAgIHBhc3NcbiAgICAgICAgICAgIGVsc2U6XG4gICAgICAgICAgICAgICAgZm9yIGkgaW4gZmlsdGVyTGlzdDpcbiAgICAgICAgICAgICAgICAgICAgaWYgaSBpbiBmdWxsUGF0aDpcbiAgICAgICAgICAgICAgICAgICAgICAgIGF1dGg9RmFsc2VcbiAgICAgICAgICAgICAgICBpZiBhdXRoPT1UcnVlOlxuICAgICAgICAgICAgICAgICAgICByZXN1bHQgPSBBdXRoLmlkZW50aWZ5KEF1dGgsIHJlcXVlc3QpXG5cbiAgICAgICAgICAgICAgICAgICAgaWYgcmVzdWx0LmdldCgnY29kZScpICE9IG5vcm1hbF9jb2RlOlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpzb25SZXNwb25zZShyZXN1bHQpXG4gICAgICAgIGVsaWYgcmVxdWVzdC5tZXRob2QgPT0gJ1BPU1QnOlxuICAgICAgICAgICAgcG9zdF9saXN0ID0gW1xuICAgICAgICAgICAgICAgICcve30vZGVmYXVsdHVzZXIvcmVnaXN0ZXInLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICAnL3t9L2RlZmF1bHR1c2VyL2xvZ2luJy5mb3JtYXQoc2NoZW1hTmFtZSksXG4gICAgICAgICAgICAgICAgJy97fS91c2Vycy9yZWdpc3RlcicuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgICcve30vdXNlcnMvbG9naW4nLmZvcm1hdChzY2hlbWFOYW1lKSxcbiAgICAgICAgICAgICAgICBcIi97fS9leGFtdXNlcnMvbG9naW5cIi5mb3JtYXQoc2NoZW1hTmFtZSksXG4gICAgICAgICAgICAgICAgXCIve30vZXhhbXVzZXJzL3JlZ2lzdGVyXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgIFwiL3t9L2ZpbGUvdXBsb2FkXCIuZm9ybWF0KHNjaGVtYU5hbWUpLFxuICAgICAgICAgICAgICAgIFwiL3VwZGF0ZVwiXG4gICAgICAgICAgICBdICAjIOWFjeiupOivgWxpc3RcbiAgICAgICAgICAgIGlmIGZ1bGxQYXRoIG5vdCBpbiBwb3N0X2xpc3QgYW5kIFwicmVnaXN0ZXJcIiBub3QgaW4gZnVsbFBhdGggYW5kIFwibG9naW5cIiBub3QgaW4gZnVsbFBhdGggYW5kIFwiZmFjZUxvZ2luXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcInVwZGF0ZVwiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCJ1cGxvYWRcIiBub3QgaW4gZnVsbFBhdGg6ICAjIOazqOWGjOaXtuS4jeajgOa1i3Rva2Vu44CCXG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gQXV0aC5pZGVudGlmeShBdXRoLCByZXF1ZXN0KVxuXG4gICAgICAgICAgICAgICAgaWYgcmVzdWx0LmdldCgnY29kZScpICE9IG5vcm1hbF9jb2RlOlxuICAgICAgICAgICAgICAgICAgICBwcmludCgnand0IGF1dGggZmFpbCcpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBKc29uUmVzcG9uc2UocmVzdWx0KVxuLi4uXG5QYXRoOiBtYWluL1poaWxpYW5nZGVuZ2ppX3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtcyBpbXBvcnQgbW9kZWxfdG9fZGljdFxuaW1wb3J0IHJlcXVlc3RzXG5mcm9tIHV0aWwuQ3VzdG9tSlNPTkVuY29kZXIgaW1wb3J0IEN1c3RvbUpzb25FbmNvZGVyXG5mcm9tIC5tb2RlbHMgaW1wb3J0IHpoaWxpYW5nZGVuZ2ppXG5mcm9tIHV0aWwuY29kZXMgaW1wb3J0ICpcbmZyb20gdXRpbC5hdXRoIGltcG9ydCBBdXRoXG5mcm9tIHV0aWwuY29tbW9uIGltcG9ydCBDb21tb25cbmltcG9ydCB1dGlsLm1lc3NhZ2UgYXMgbWVzXG5mcm9tIGRqYW5nby5kYiBpbXBvcnQgY29ubmVjdGlvblxuaW1wb3J0IHJhbmRvbVxuZnJvbSBkamFuZ28uY29yZS5tYWlsIGltcG9ydCBzZW5kX21haWxcbmZyb20gZGphbmdvLmNvbmYgaW1wb3J0IHNldHRpbmdzXG5mcm9tIGRqYW5nby5zaG9ydGN1dHMgaW1wb3J0IHJlZGlyZWN0XG5mcm9tIGRqYW5nby5kYi5tb2RlbHMgaW1wb3J0IFFcbmZyb20gdXRpbC5iYWlkdWJjZV9hcGkgaW1wb3J0IEJhaUR1QmNlXG4uLi5cblBhdGg6IHhtaWRkbGV3YXJlL3hwYXJhbS5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGpzb24sY29weVxuZnJvbSBkamFuZ28udXRpbHMuZGVwcmVjYXRpb24gaW1wb3J0IE1pZGRsZXdhcmVNaXhpblxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5cbmNsYXNzIFhwYXJhbShNaWRkbGV3YXJlTWl4aW4pOlxuICAgIGRlZiBwcm9jZXNzX3JlcXVlc3Qoc2VsZixyZXF1ZXN0KTpcbiAgICAgICAgZnVsbFBhdGg9cmVxdWVzdC5nZXRfZnVsbF9wYXRoKClcbiAgICAgICAgcHJpbnQoXCJmdWxsUGF0aD09PT09PT09PT09PT09PT5cIixmdWxsUGF0aClcbiAgICAgICAgaWYgcmVxdWVzdC5NRVRBLmdldCgnSFRUUF9VUEdSQURFJyk9PSd3ZWJzb2NrZXQnOlxuICAgICAgICAgICAgcmV0dXJuXG4gICAgICAgIGlmIFwiL2pzL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvY3NzL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvaW1nL1wiIG5vdCBpbiBmdWxsUGF0aCBhbmQgXCIvZm9udHMvXCIgbm90IGluIGZ1bGxQYXRoIGFuZCBcIi9mcm9udC9cIiBub3QgaW4gZnVsbFBhdGg6XG4gICAgICAgICAgICByZXFfZGljdD17fVxuXG4gICAgICAgICAgICAj6I635Y+WcXVlcnnnmoTlj4LmlbBcbiAgICAgICAgICAgIGZvciBrLHYgaW4gcmVxdWVzdC5HRVQuaXRlbXMoKTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFtrXT12XG5cbiAgICAgICAgICAgIGlmIHJlcXVlc3QubWV0aG9kPT1cIlBPU1RcIjpcbiAgICAgICAgICAgICAgICBjb250ZW50VHlwZT1yZXF1ZXN0Lk1FVEEuZ2V0KCdDT05URU5UX1RZUEUnLFwiXCIpXG4gICAgICAgICAgICAgICAgIyBwcmludChcImNvbnRlbnRUeXBlPT09PT09PT09PT09PT09PT09PlwiLGNvbnRlbnRUeXBlKVxuICAgICAgICAgICAgICAgIGlmIFwianNvblwiIGluIGNvbnRlbnRUeXBlOlxuICAgICAgICAgICAgICAgICAgICAj6Kej5p6QYXBwbGljYXRpb24vanNvbueahOWPguaVsCzlpoLmnpzop6PmnpDlpLHotKUs5o+Q5YmN6L+U5Zue5oql6ZSZMjAyMDA5MjVcbiAgICAgICAgICAgICAgICAgICAgdHJ5OlxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YV8gPSBqc29uLmxvYWRzKHJlcXVlc3QuYm9keSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIHR5cGUoZGF0YV8pID09IHR5cGUoWzFdKTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXFfZGljdFsnaWRzJ10gPSBkYXRhX1xuICAgICAgICAgICAgICAgICAgICAgICAgZWxzZTpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgaywgdiBpbiBkYXRhXy5pdGVtcygpOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXFfZGljdFtrXSA9IHZcbiAgICAgICAgICAgICAgICAgICAgZXhjZXB0IEV4Y2VwdGlvbiBhcyBlOlxuICAgICAgICAgICAgICAgICAgICAgICAgIyDojrflj5Zmb3JtZGF0YeeahOWPguaVsFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9yIGssIHYgaW4gcmVxdWVzdC5QT1NULml0ZW1zKCk6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVxX2RpY3Rba10gPSB2XG4gICAgICAgICAgICAgICAgZWxzZTpcbiAgICAgICAgICAgICAgICAgICAgIyDojrflj5Zmb3JtZGF0YeeahOWPguaVsFxuICAgICAgICAgICAgICAgICAgICBmb3IgaywgdiBpbiByZXF1ZXN0LlBPU1QuaXRlbXMoKTpcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcV9kaWN0W2tdID0gdlxuICAgICAgICAgICAgI+WkhOeQhuWPguaVsO+8jOWOu+aOieaXoOeUqOeahOWSjOmUmeivr+eahOWPguaVsFxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFwiY3JlYXRlZFwiKSE9Tm9uZTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFsnYWRkdGltZSddPWNvcHkuZGVlcGNvcHkocmVxX2RpY3QuZ2V0KFwiY3JlYXRlZFwiKSlcbiAgICAgICAgICAgICAgICBkZWwgcmVxX2RpY3RbXCJjcmVhdGVkXCJdXG4gICAgICAgICAgICBpZiByZXFfZGljdC5nZXQoXCJ0XCIpICE9IE5vbmU6XG4gICAgICAgICAgICAgICAgZGVsIHJlcV9kaWN0W1widFwiXVxuICAgICAgICAgICAgaWYgcmVxX2RpY3QuZ2V0KFwiMVwiKSE9Tm9uZTpcbiAgICAgICAgICAgICAgICByZXFfZGljdFsndHlwZSddPWNvcHkuZGVlcGNvcHkocmVxX2RpY3QuZ2V0KFwiMVwiKSlcbi4uLlxuUGF0aDogdXRpbC9iYWlkdWJjZV9hcGkucHlcbi4uLlxuXG4gICAgZGVmIGdldF9maWxlX2NvbnRlbnRfYXNfYmFzZTY0KHNlbGYscGF0aCwgdXJsZW5jb2RlZD1GYWxzZSk6XG4gICAgICAgIFwiXCJcIlxuICAgICAgICDojrflj5bmlofku7ZiYXNlNjTnvJbnoIFcbiAgICAgICAgOnBhcmFtIHBhdGg6IOaWh+S7tui3r+W+hFxuICAgICAgICA6cGFyYW0gdXJsZW5jb2RlZDog5piv5ZCm5a+557uT5p6c6L+b6KGMdXJsZW5jb2RlZFxuICAgICAgICA6cmV0dXJuOiBiYXNlNjTnvJbnoIHkv6Hmga9cbiAgICAgICAgXCJcIlwiXG4gICAgICAgIHdpdGggb3BlbihwYXRoLCBcInJiXCIpIGFzIGY6XG4gICAgICAgICAgICBjb250ZW50ID0gYmFzZTY0LmI2NGVuY29kZShmLnJlYWQoKSkuZGVjb2RlKFwidXRmOFwiKVxuICAgICAgICAgICAgaWYgdXJsZW5jb2RlZDpcbiAgICAgICAgICAgICAgICBjb250ZW50ID0gdXJsbGliLnBhcnNlLnF1b3RlX3BsdXMoY29udGVudClcbiAgICAgICAgcmV0dXJuIGNvbnRlbnRcblxuaWYgX19uYW1lX189PSdfX21haW5fXyc6XG4gICAgY2xpZW50X2lkID0gJ3gyMHhPanRPc0F0YlFobTJXQnVpZnVRdycgICMgYWtcbiAgICBjbGllbnRfc2VjcmV0ID0gJ083eU1wMmRtT25DdFF0Qm9rVXQxZ042aGdGQ2NMTGNwJyAgIyBza1xuXG4gICAgIyDmnKzlnLDlm77niYflnLDlnYDvvIzmoLnmja7oh6rlt7HnmoTlm77niYfov5vooYzkv67mlLlcbiAgICBpbWFnZTEgPSAnbnVkZTEuanBnJ1xuICAgIGltYWdlMiA9ICdudWRlMi5qcGcnXG5cbiAgICBiZGI9QmFpRHVCY2UoKVxuICAgIGJkYi5iZF9jaGVjazJwaWMoaW1hZ2UxLCBpbWFnZTIpLi4uXG5QYXRoOiBkajIvc2V0dGluZ3MucHlcbi4uLlxuXG5NSURETEVXQVJFID0gW1xuICAgICdkamFuZ28ubWlkZGxld2FyZS5zZWN1cml0eS5TZWN1cml0eU1pZGRsZXdhcmUnLFxuICAgICdkamFuZ28uY29udHJpYi5zZXNzaW9ucy5taWRkbGV3YXJlLlNlc3Npb25NaWRkbGV3YXJlJyxcbiAgICAnZGphbmdvLm1pZGRsZXdhcmUuY29tbW9uLkNvbW1vbk1pZGRsZXdhcmUnLFxuICAgICMgJ2RqYW5nby5taWRkbGV3YXJlLmNzcmYuQ3NyZlZpZXdNaWRkbGV3YXJlJyxcbiAgICAnZGphbmdvLmNvbnRyaWIuYXV0aC5taWRkbGV3YXJlLkF1dGhlbnRpY2F0aW9uTWlkZGxld2FyZScsXG4gICAgJ2RqYW5nby5jb250cmliLm1lc3NhZ2VzLm1pZGRsZXdhcmUuTWVzc2FnZU1pZGRsZXdhcmUnLFxuICAgICdkamFuZ28ubWlkZGxld2FyZS5jbGlja2phY2tpbmcuWEZyYW1lT3B0aW9uc01pZGRsZXdhcmUnLFxuICAgICd0aHJlYWRsb2NhbHMubWlkZGxld2FyZS5UaHJlYWRMb2NhbE1pZGRsZXdhcmUnLFxuICAgIFwieG1pZGRsZXdhcmUueHBhcmFtLlhwYXJhbVwiLFxuICAgIFwieG1pZGRsZXdhcmUueGF1dGguWGF1dGhcIixcbiAgICAnY29yc2hlYWRlcnMubWlkZGxld2FyZS5Db3JzTWlkZGxld2FyZScsXG4gICAgJ2RqYW5nby5taWRkbGV3YXJlLmNvbW1vbi5Db21tb25NaWRkbGV3YXJlJyxcbl1cbi4uLlxuUGF0aDogdXRpbC9jb25maWdyZWFkLnB5XG4jY29kaW5nOnV0Zi04XG5mcm9tIGNvbmZpZ3BhcnNlciBpbXBvcnQgQ29uZmlnUGFyc2VyXG5cbmRlZiBjb25maWdfcmVhZChmaWxlUGF0aDpzdHIpOlxuICAgIGNmZz1Db25maWdQYXJzZXIoKVxuICAgIGNmZy5yZWFkKGZpbGVQYXRoLCBlbmNvZGluZz1cInV0Zi04LXNpZ1wiKVxuICAgIGlmIFwic3FsXCIgaW4gY2ZnLnNlY3Rpb25zKCk6XG4gICAgICAgIGRiVHlwZT1jZmcuZ2V0KCdzcWwnLCd0eXBlJylcbiAgICAgICAgaG9zdD1jZmcuZ2V0KCdzcWwnLCdob3N0JylcbiAgICAgICAgcG9ydD1jZmcuZ2V0aW50KCdzcWwnLCdwb3J0JylcbiAgICAgICAgdXNlcj1jZmcuZ2V0KCdzcWwnLCd1c2VyJylcbiAgICAgICAgcGFzc3dkPWNmZy5nZXQoJ3NxbCcsJ3Bhc3N3ZCcpXG4gICAgICAgIGRiTmFtZT1jZmcuZ2V0KCdzcWwnLCdkYicpXG4gICAgICAgIGNoYXJzZXQ9Y2ZnLmdldCgnc3FsJywnY2hhcnNldCcpXG4gICAgICAgIHJldHVybiBkYlR5cGUsaG9zdCxwb3J0LHVzZXIscGFzc3dkLGRiTmFtZSxjaGFyc2V0XG4gICAgZWxzZTpcbiAgICAgICAgcmV0dXJuIE5vbmUsTm9uZSxOb25lLE5vbmUsTm9uZSxOb25lLE5vbmUuLi5cblBhdGg6IHhtaWRkbGV3YXJlL19faW5pdF9fLnB5XG4jY29kaW5nOnV0Zi04Li4uXG5QYXRoOiBtYWluL1J1a3Vfdi5weVxuI2NvZGluZzp1dGYtOFxuaW1wb3J0IGJhc2U2NCwgY29weSwgbG9nZ2luZywgb3MsIHN5cywgdGltZSwgeGxyZCwganNvbiwgZGF0ZXRpbWUsIGNvbmZpZ3BhcnNlclxuZnJvbSBkamFuZ28uaHR0cCBpbXBvcnQgSnNvblJlc3BvbnNlXG5mcm9tIGRqYW5nby5hcHBzIGltcG9ydCBhcHBzXG5pbXBvcnQgbnVtYmVyc1xuZnJvbSBkamFuZ28uZGIubW9kZWxzLmFnZ3JlZ2F0ZXMgaW1wb3J0IENvdW50LFN1bVxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBDYXNlLCBXaGVuLCBJbnRlZ2VyRmllbGQsIEZcbmZyb20gZGphbmdvLmZvcm1zIGltcG9ydCBtb2RlbF90b19kaWN0XG5pbXBvcnQgcmVxdWVzdHNcbmZyb20gdXRpbC5DdXN0b21KU09ORW5jb2RlciBpbXBvcnQgQ3VzdG9tSnNvbkVuY29kZXJcbmZyb20gLm1vZGVscyBpbXBvcnQgcnVrdVxuZnJvbSB1dGlsLmNvZGVzIGltcG9ydCAqXG5mcm9tIHV0aWwuYXV0aCBpbXBvcnQgQXV0aFxuZnJvbSB1dGlsLmNvbW1vbiBpbXBvcnQgQ29tbW9uXG5pbXBvcnQgdXRpbC5tZXNzYWdlIGFzIG1lc1xuZnJvbSBkamFuZ28uZGIgaW1wb3J0IGNvbm5lY3Rpb25cbmltcG9ydCByYW5kb21cbmZyb20gZGphbmdvLmNvcmUubWFpbCBpbXBvcnQgc2VuZF9tYWlsXG5mcm9tIGRqYW5nby5jb25mIGltcG9ydCBzZXR0aW5nc1xuZnJvbSBkamFuZ28uc2hvcnRjdXRzIGltcG9ydCByZWRpcmVjdFxuZnJvbSBkamFuZ28uZGIubW9kZWxzIGltcG9ydCBRXG5mcm9tIHV0aWwuYmFpZHViY2VfYXBpIGltcG9ydCBCYWlEdUJjZVxuLi4uXG5QYXRoOiBtYWluL0NodWt1X3YucHlcbiNjb2Rpbmc6dXRmLThcbmltcG9ydCBiYXNlNjQsIGNvcHksIGxvZ2dpbmcsIG9zLCBzeXMsIHRpbWUsIHhscmQsIGpzb24sIGRhdGV0aW1lLCBjb25maWdwYXJzZXJcbmZyb20gZGphbmdvLmh0dHAgaW1wb3J0IEpzb25SZXNwb25zZVxuZnJvbSBkamFuZ28uYXBwcyBpbXBvcnQgYXBwc1xuaW1wb3J0IG51bWJlcnNcbmZyb20gZGphbmdvLmRiLm1vZGVscy5hZ2dyZWdhdGVzIGltcG9ydCBDb3VudCxTdW1cbmZyb20gZGphbmdvLmRiLm1vZGVscyBpbXBvcnQgQ2FzZSwgV2hlbiwgSW50ZWdlckZpZWxkLCBGXG5mcm9tIGRqYW5nby5mb3JtLi4uIiwiaXNFcnJvciI6ZmFsc2UsInJlcXVlc3RJZCI6IiJ9fSwiMGY1MWFjMjAtOTczNy00ZmIwLTk5M2ItMDFhOGExMWI5NTM0O3Rvb2x1X3ZydHhfMDFVcTdFV2VlcDFORzRGRnNVMkZDd0pBIjp7InJlcXVlc3RJZCI6IjBmNTFhYzIwLTk3MzctNGZiMC05OTNiLTAxYThhMTFiOTUzNCIsInRvb2xVc2VJZCI6InRvb2x1X3ZydHhfMDFVcTdFV2VlcDFORzRGRnNVMkZDd0pBIiwicGhhc2UiOjUsInJlc3VsdCI6eyJ0ZXh0IjoiSGVyZSBhcmUgdGhlIHJlc3VsdHMgZnJvbSBleGVjdXRpbmcgdGhlIGNvbW1hbmQuXG48cmV0dXJuLWNvZGU+XG4wXG48L3JldHVybi1jb2RlPlxuPHN0ZG91dD5cblxuPC9zdGRvdXQ+XG48c3RkZXJyPlxuXG48L3N0ZGVycj4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJkNTk1ZDVhNi01NzVhLTRkYTAtYmU2ZS0wY2M4NjAzMzY2MWE7dG9vbHVfdnJ0eF8wMVc5MnBXcWZ0OXM0OWVFZjZVV3d0NEIiOnsicmVxdWVzdElkIjoiZDU5NWQ1YTYtNTc1YS00ZGEwLWJlNmUtMGNjODYwMzM2NjFhIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVc5MnBXcWZ0OXM0OWVFZjZVV3d0NEIiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJjYzgxMTE5MC05YmJkLTRhZmMtYTUyYi1mOWY0NmMyOTY0Njk7dG9vbHVfdnJ0eF8wMTlzVGd4ZnBhZGpxQk11M0hkMk1DUVAiOnsicmVxdWVzdElkIjoiY2M4MTExOTAtOWJiZC00YWZjLWE1MmItZjlmNDZjMjk2NDY5IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTlzVGd4ZnBhZGpxQk11M0hkMk1DUVAiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIyYTUzMDU0OS00MmNlLTQ2Y2UtYWU1Ny0xMWE5ODA5MjA1ODc7dG9vbHVfdnJ0eF8wMTFhWUpoZFRhQmtjWms0YWFNTkdQMVgiOnsicmVxdWVzdElkIjoiMmE1MzA1NDktNDJjZS00NmNlLWFlNTctMTFhOTgwOTIwNTg3IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTFhWUpoZFRhQmtjWms0YWFNTkdQMVgiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI2YjliYzFhYS0wODgwLTQyM2YtOTU2MS01M2VjOTZkMjRkOTQ7dG9vbHVfdnJ0eF8wMUtyQ1dTa3VvUnI4a3R5dGV5eGlYdDYiOnsicmVxdWVzdElkIjoiNmI5YmMxYWEtMDg4MC00MjNmLTk1NjEtNTNlYzk2ZDI0ZDk0IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUtyQ1dTa3VvUnI4a3R5dGV5eGlYdDYiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJkNWUxZjZhNi1kMDRhLTQyNjgtYjI0MC03MTY3NmQ1Y2IzNGQ7dG9vbHVfdnJ0eF8wMVR2U1RqUUZGTjlXb2syWUdHS1hXYzYiOnsicmVxdWVzdElkIjoiZDVlMWY2YTYtZDA0YS00MjY4LWIyNDAtNzE2NzZkNWNiMzRkIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVR2U1RqUUZGTjlXb2syWUdHS1hXYzYiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI3NmVkMDY0MS01ZWY4LTQyNDItODVjOS0yZGUzYmFhNDY3YjU7dG9vbHVfdnJ0eF8wMUM1d0ZVUGd2S0Z0eEhrRFVjd1l0ZmkiOnsicmVxdWVzdElkIjoiNzZlZDA2NDEtNWVmOC00MjQyLTg1YzktMmRlM2JhYTQ2N2I1IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUM1d0ZVUGd2S0Z0eEhrRFVjd1l0ZmkiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIzNGMzMDhiMS05YzljLTRkZTgtYWJjMi05MGFhMDIyMzFmMDc7dG9vbHVfdnJ0eF8wMVZHNW9RTUdacmhYSmtXRVg0QmhobmoiOnsicmVxdWVzdElkIjoiMzRjMzA4YjEtOWM5Yy00ZGU4LWFiYzItOTBhYTAyMjMxZjA3IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVZHNW9RTUdacmhYSmtXRVg0QmhobmoiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJkYTk4NjJkZC1iMWUxLTRhYTYtOTc0Yy1hMTRiMmNlYjdmZmM7dG9vbHVfdnJ0eF8wMTNoYnRmeWQ2U1pLWGRmc3JSQkg5eDQiOnsicmVxdWVzdElkIjoiZGE5ODYyZGQtYjFlMS00YWE2LTk3NGMtYTE0YjJjZWI3ZmZjIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTNoYnRmeWQ2U1pLWGRmc3JSQkg5eDQiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJlZDRiNTYzNy02MDllLTRlZmItYWFjNS05OTgwNWEzYTJhNmU7dG9vbHVfdnJ0eF8wMUpTTHhnekQzRjdNQnVzZFlqbVBRajIiOnsicmVxdWVzdElkIjoiZWQ0YjU2MzctNjA5ZS00ZWZiLWFhYzUtOTk4MDVhM2EyYTZlIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUpTTHhnekQzRjdNQnVzZFlqbVBRajIiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI5NDA1MmYzZi03ZGI3LTRhNzgtYTk3MC0xNzZkNmQ1NzljYTY7dG9vbHVfdnJ0eF8wMUdWcHhjRGMyWVpKZVg0Mkp2dFFaN0IiOnsicmVxdWVzdElkIjoiOTQwNTJmM2YtN2RiNy00YTc4LWE5NzAtMTc2ZDZkNTc5Y2E2IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUdWcHhjRGMyWVpKZVg0Mkp2dFFaN0IiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJmZGU5NDc1Ni05MjcyLTRiNGMtOGU2NS01MGU5NzlmZDBjMjk7dG9vbHVfdnJ0eF8wMUVLRWJ5QkpMdEpyZGlDNHRUelNFb3YiOnsicmVxdWVzdElkIjoiZmRlOTQ3NTYtOTI3Mi00YjRjLThlNjUtNTBlOTc5ZmQwYzI5IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUVLRWJ5QkpMdEpyZGlDNHRUelNFb3YiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI4ZWIxZDA5Zi05MDZiLTQ1N2QtOGE4Zi02NzIxOTM5M2E1Yjc7dG9vbHVfdnJ0eF8wMTZrd2U0UG9TRDJYQUVCR1o2cTlwbm8iOnsicmVxdWVzdElkIjoiOGViMWQwOWYtOTA2Yi00NTdkLThhOGYtNjcyMTkzOTNhNWI3IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTZrd2U0UG9TRDJYQUVCR1o2cTlwbm8iLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI4YzQ2NTEyMy0wOTJmLTQxNmItYjU3Ny02MGMyODFkNDcyMDM7dG9vbHVfdnJ0eF8wMU4xcG4yclpKamJkS29pRWMxV0hWTVciOnsicmVxdWVzdElkIjoiOGM0NjUxMjMtMDkyZi00MTZiLWI1NzctNjBjMjgxZDQ3MjAzIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMU4xcG4yclpKamJkS29pRWMxV0hWTVciLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJlOTFlM2Y3Ni03NWUyLTQ2YzQtYTUwYS0xYjQyY2Q5MDExNmY7dG9vbHVfdnJ0eF8wMTFoUmY5OThRVjh3NW1xV2VDcWNCVjYiOnsicmVxdWVzdElkIjoiZTkxZTNmNzYtNzVlMi00NmM0LWE1MGEtMWI0MmNkOTAxMTZmIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTFoUmY5OThRVjh3NW1xV2VDcWNCVjYiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI4OGVjNjBmMy1iMjllLTQzMWItYTA3Yy1lZDU2MDM2MTQxZTI7dG9vbHVfdnJ0eF8wMTVtSm5IWmpwWEpQY2M5eXpmc3pOcTkiOnsicmVxdWVzdElkIjoiODhlYzYwZjMtYjI5ZS00MzFiLWEwN2MtZWQ1NjAzNjE0MWUyIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTVtSm5IWmpwWEpQY2M5eXpmc3pOcTkiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJjMWRlMTNkMy0wMTFiLTQwOGUtYmU1YS0wYTM3ZjIxYTQwZTM7dG9vbHVfdnJ0eF8wMVJGQzJVY0NwRUF2UFprNzJONnBDQjUiOnsicmVxdWVzdElkIjoiYzFkZTEzZDMtMDExYi00MDhlLWJlNWEtMGEzN2YyMWE0MGUzIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVJGQzJVY0NwRUF2UFprNzJONnBDQjUiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJmOWZiODZkOC04MzFkLTQ4NzItYjg3My03NzE2ZDg3MjUzMWU7dG9vbHVfdnJ0eF8wMTFUdkhUOGVoR1hGS0NkYnhwYU0yYW4iOnsicmVxdWVzdElkIjoiZjlmYjg2ZDgtODMxZC00ODcyLWI4NzMtNzcxNmQ4NzI1MzFlIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTFUdkhUOGVoR1hGS0NkYnhwYU0yYW4iLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJmYTUwZGU3NC01YjgwLTQ3OWMtYmI0NS1hZTEyZGVjOWJlYjE7dG9vbHVfdnJ0eF8wMVBvNnp5U3c4eWlDRmJkVXl6ZGk5THoiOnsicmVxdWVzdElkIjoiZmE1MGRlNzQtNWI4MC00NzljLWJiNDUtYWUxMmRlYzliZWIxIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVBvNnp5U3c4eWlDRmJkVXl6ZGk5THoiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI2ZDViNjc5MS00MzEzLTRiMjEtYmVhMi01ODM5YWVjOWRjZmY7dG9vbHVfdnJ0eF8wMVZOVURZamFyalVvdWdiTVBacm5kZFoiOnsicmVxdWVzdElkIjoiNmQ1YjY3OTEtNDMxMy00YjIxLWJlYTItNTgzOWFlYzlkY2ZmIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVZOVURZamFyalVvdWdiTVBacm5kZFoiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIyYTFkY2UwYy02ZjM2LTRjN2YtODEyZi1mZjgzOTllMzg2ZDg7dG9vbHVfdnJ0eF8wMUpZbkZDdEwxWnpDeEpSelE4cG9pcXciOnsicmVxdWVzdElkIjoiMmExZGNlMGMtNmYzNi00YzdmLTgxMmYtZmY4Mzk5ZTM4NmQ4IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUpZbkZDdEwxWnpDeEpSelE4cG9pcXciLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIzOTI1YWU5YS01MjJmLTQ3OWItYTljOC04MTIyZjRkMjExZTc7dG9vbHVfdnJ0eF8wMTg1RkU0RnJMbmJYczhQVVI4aHdSYkUiOnsicmVxdWVzdElkIjoiMzkyNWFlOWEtNTIyZi00NzliLWE5YzgtODEyMmY0ZDIxMWU3IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTg1RkU0RnJMbmJYczhQVVI4aHdSYkUiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIyOTlkNmFmYS0xMmM5LTQ1MzgtYjBlZS0wYmQ1NzNkODQ2MWM7dG9vbHVfdnJ0eF8wMTQ0dEdYaDcxaktETmo3ZjdZRUZTRlgiOnsicmVxdWVzdElkIjoiMjk5ZDZhZmEtMTJjOS00NTM4LWIwZWUtMGJkNTczZDg0NjFjIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTQ0dEdYaDcxaktETmo3ZjdZRUZTRlgiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI1YjY3MmY4Yy1lNzJjLTQ2M2MtYmUyNS02M2IzZmY5Yjg0NGU7dG9vbHVfdnJ0eF8wMUR5YlV1WURzOEJINER3Z3pGcXZ6SFoiOnsicmVxdWVzdElkIjoiNWI2NzJmOGMtZTcyYy00NjNjLWJlMjUtNjNiM2ZmOWI4NDRlIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUR5YlV1WURzOEJINER3Z3pGcXZ6SFoiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI4YWFlZGZiYy05NDc0LTQ3YzItODgxOS03ZTRiN2ZlNjQ5MzI7dG9vbHVfdnJ0eF8wMVhNaHoxQmVjZnp2VFpSakthQUgzUmsiOnsicmVxdWVzdElkIjoiOGFhZWRmYmMtOTQ3NC00N2MyLTg4MTktN2U0YjdmZTY0OTMyIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMVhNaHoxQmVjZnp2VFpSakthQUgzUmsiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJjNWY1NjhlNy0yNmFkLTRmYjAtOWVkYS02NTFjYjhlYjE1MzM7dG9vbHVfdnJ0eF8wMUc1cmdnQUhTMVlydEhQRzN4dDhkYjEiOnsicmVxdWVzdElkIjoiYzVmNTY4ZTctMjZhZC00ZmIwLTllZGEtNjUxY2I4ZWIxNTMzIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUc1cmdnQUhTMVlydEhQRzN4dDhkYjEiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCI3MzQ4ZjA0NC1kNTlmLTQ1YjctYmM4Yi0yY2Y3MGVlMTA5YzA7dG9vbHVfdnJ0eF8wMUF5U2dCTXN2ZE4xZWhUazlDRmVEZE4iOnsicmVxdWVzdElkIjoiNzM0OGYwNDQtZDU5Zi00NWI3LWJjOGItMmNmNzBlZTEwOWMwIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUF5U2dCTXN2ZE4xZWhUazlDRmVEZE4iLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCIyOTQ1YzE5Zi05ZTg3LTQwOTYtOGMyZi01YThmYjNhMzBlZDY7dG9vbHVfdnJ0eF8wMU1WVHczU3FKdDRVVnBtcHM4UzdNMWciOnsicmVxdWVzdElkIjoiMjk0NWMxOWYtOWU4Ny00MDk2LThjMmYtNWE4ZmIzYTMwZWQ2IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMU1WVHczU3FKdDRVVnBtcHM4UzdNMWciLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJjYTUwMTE4Ny1lNmM4LTQ4MzgtYjg5NC03MTIyNDNkZTBjYjk7dG9vbHVfdnJ0eF8wMTJ5ZlFNMjRwZktWVGRhZDZjRTZtdUUiOnsicmVxdWVzdElkIjoiY2E1MDExODctZTZjOC00ODM4LWI4OTQtNzEyMjQzZGUwY2I5IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMTJ5ZlFNMjRwZktWVGRhZDZjRTZtdUUiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJiNTg2ZjI1MC01NjExLTQwZmUtYWY0My1mMDI0YTliOWNiYmU7dG9vbHVfdnJ0eF8wMUpUemhRRlZHS2VFY3BSRHhqUUZwbVUiOnsicmVxdWVzdElkIjoiYjU4NmYyNTAtNTYxMS00MGZlLWFmNDMtZjAyNGE5YjljYmJlIiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUpUemhRRlZHS2VFY3BSRHhqUUZwbVUiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJGaWxlIHNhdmVkIHN1Y2Nlc3NmdWxseS4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19LCJiZTYxOTI0NC0xNzU3LTRhMGEtOWFiYi1kYzUzNmY5NzM5Njg7dG9vbHVfdnJ0eF8wMUVCU0NiYTVEdVNKV3FvY283bThTWFMiOnsicmVxdWVzdElkIjoiYmU2MTkyNDQtMTc1Ny00YTBhLTlhYmItZGM1MzZmOTczOTY4IiwidG9vbFVzZUlkIjoidG9vbHVfdnJ0eF8wMUVCU0NiYTVEdVNKV3FvY283bThTWFMiLCJwaGFzZSI6NSwicmVzdWx0Ijp7InRleHQiOiJIZXJlIGFyZSB0aGUgcmVzdWx0cyBmcm9tIGV4ZWN1dGluZyB0aGUgY29tbWFuZC5cbiAgICAgICAgPHJldHVybi1jb2RlPlxuICAgICAgICAwXG4gICAgICAgIDwvcmV0dXJuLWNvZGU+XG4gICAgICAgIDxzdGRvdXQ+XG4gICAgICAgIG5ldy9kajIvX19pbml0X18ucHlcbm5ldy9kajIvc2V0dGluZ3MucHlcbm5ldy9kajIvdXJscy5weVxubmV3L2RqMi92aWV3cy5weVxubmV3L2RqMi93c2dpLnB5XG5uZXcvbWFpbi9hZG1pbi5weVxubmV3L21haW4vYXBwcy5weVxubmV3L21haW4vY29uZmlnX21vZGVsLnB5XG5uZXcvbWFpbi9jb25maWdfdi5weVxubmV3L21haW4vX19pbml0X18ucHlcbm5ldy9tYWluL21vZGVsLnB5XG5uZXcvbWFpbi9tb2RlbHMucHlcbm5ldy9tYWluL3NjaGVtYV92LnB5XG5uZXcvbWFpbi91cmxzLnB5XG5uZXcvbWFpbi91c2Vyc19tb2RlbC5weVxubmV3L21haW4vdXNlcnNfdi5weVxubmV3L21hbmFnZS5weVxubmV3L1JFQURNRS5tZFxubmV3L3V0aWwvYXV0aC5weVxubmV3L3V0aWwvYmFpZHViY2VfYXBpLnB5XG5uZXcvdXRpbC9jb2Rlcy5weVxubmV3L3V0aWwvY29tbW9uLnB5XG5uZXcvdXRpbC9jb25maWdyZWFkLnB5XG5uZXcvdXRpbC9DdXN0b21KU09ORW5jb2Rlci5weVxubmV3L3V0aWwvX19pbml0X18ucHlcbm5ldy91dGlsL21lc3NhZ2UucHlcbm5ldy94bWlkZGxld2FyZS9fX2luaXRfXy5weVxubmV3L3htaWRkbGV3YXJlL3hhdXRoLnB5XG5uZXcveG1pZGRsZXdhcmUveHBhcmFtLnB5XG5cbiAgICAgICAgPC9zdGRvdXQ+XG4gICAgICAgIDxzdGRlcnI+XG4gICAgICAgIFxuICAgICAgICA8L3N0ZGVycj4iLCJpc0Vycm9yIjpmYWxzZSwicmVxdWVzdElkIjoiIn19fSwiZHJhZnRFeGNoYW5nZSI6eyJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJyaWNoX3RleHRfanNvbl9yZXByIjp7InR5cGUiOiJkb2MiLCJjb250ZW50IjpbeyJ0eXBlIjoicGFyYWdyYXBoIn1dfSwic3RhdHVzIjoiZHJhZnQifSwiZHJhZnRBY3RpdmVDb250ZXh0SWRzIjpbIi9ob21lL21hbm5peC9EZXNrdG9wL2RqYW5nb2syZ2c2MzczLXJlYnVpbGRmYWxzZWZhbHNlIiwidXNlckd1aWRlbGluZXMiLCJhZ2VudE1lbW9yaWVzIl0sInJlcXVlc3RJZHMiOltdLCJpc1Bpbm5lZCI6ZmFsc2UsImlzU2hhcmVhYmxlIjp0cnVlLCJleHRyYURhdGEiOnsiaXNBZ2VudENvbnZlcnNhdGlvbiI6dHJ1ZSwiaGFzQWdlbnRPbmJvYXJkZWQiOnRydWUsImhhc0RpcnR5RWRpdHMiOmZhbHNlfSwicGVyc29uYVR5cGUiOjB9LCI3OWVmY2U1OS1mMzkxLTQwYWYtOGVlNC02OTA0MGMyYzFlNjIiOnsiaWQiOiI3OWVmY2U1OS1mMzkxLTQwYWYtOGVlNC02OTA0MGMyYzFlNjIiLCJjcmVhdGVkQXRJc28iOiIyMDI1LTA1LTA1VDA4OjQxOjIzLjc4NFoiLCJsYXN0SW50ZXJhY3RlZEF0SXNvIjoiMjAyNS0wNS0wNVQwODo0MToyMy43ODRaIiwiY2hhdEhpc3RvcnkiOltdLCJmZWVkYmFja1N0YXRlcyI6e30sInRvb2xVc2VTdGF0ZXMiOnt9LCJkcmFmdEV4Y2hhbmdlIjp7InJlcXVlc3RfbWVzc2FnZSI6IiIsInJpY2hfdGV4dF9qc29uX3JlcHIiOnsidHlwZSI6ImRvYyIsImNvbnRlbnQiOlt7InR5cGUiOiJwYXJhZ3JhcGgifV19LCJzdGF0dXMiOiJkcmFmdCJ9LCJyZXF1ZXN0SWRzIjpbXSwiaXNQaW5uZWQiOmZhbHNlLCJpc1NoYXJlYWJsZSI6ZmFsc2UsImV4dHJhRGF0YSI6eyJoYXNEaXJ0eUVkaXRzIjpmYWxzZX0sInBlcnNvbmFUeXBlIjowfSwiM2RjMDgwYzAtMDFiNi00MjUwLTg4MzctYzVjZmYxYTE1ZGI4Ijp7ImlkIjoiM2RjMDgwYzAtMDFiNi00MjUwLTg4MzctYzVjZmYxYTE1ZGI4IiwiY3JlYXRlZEF0SXNvIjoiMjAyNS0wNS0wNVQwODo0MToyNC42NDFaIiwibGFzdEludGVyYWN0ZWRBdElzbyI6IjIwMjUtMDUtMDVUMDg6NDE6MjQuNjQyWiIsImNoYXRIaXN0b3J5IjpbXSwiZmVlZGJhY2tTdGF0ZXMiOnt9LCJ0b29sVXNlU3RhdGVzIjp7fSwiZHJhZnRFeGNoYW5nZSI6eyJyZXF1ZXN0X21lc3NhZ2UiOiIiLCJyaWNoX3RleHRfanNvbl9yZXByIjp7InR5cGUiOiJkb2MiLCJjb250ZW50IjpbeyJ0eXBlIjoicGFyYWdyYXBoIn1dfSwic3RhdHVzIjoiZHJhZnQifSwicmVxdWVzdElkcyI6W10sImlzUGlubmVkIjpmYWxzZSwiaXNTaGFyZWFibGUiOmZhbHNlLCJleHRyYURhdGEiOnsiaGFzRGlydHlFZGl0cyI6ZmFsc2V9LCJwZXJzb25hVHlwZSI6MH19LCJhZ2VudEV4ZWN1dGlvbk1vZGUiOiJtYW51YWwiLCJpc0FnZW50RWRpdHNDb2xsYXBzZWQiOnRydWUsInNvcnRDb252ZXJzYXRpb25zQnkiOiJsYXN0TWVzc2FnZVRpbWVzdGFtcCJ9" />
      </map>
    </option>
  </component>
</project>